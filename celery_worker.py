# celery_worker.py
from celery import Celery
from run import app as flask_app
import time
from celery.app.control import Control
import subprocess
import eventlet
from moviepy.audio.io.readers import FFMPEG_AudioReader

eventlet.monkey_patch()


def patched_close_proc(self):
    if self.proc:
        eventlet.spawn(self.proc.wait)
        self.proc = None


FFMPEG_AudioReader.close_proc = patched_close_proc


celery = Celery(
    flask_app.import_name,
    backend=flask_app.config["CELERY_RESULT_BACKEND"],
    broker=flask_app.config["CELERY_BROKER_URL"],
)


def create_celery(app):
    celery.conf.update(app.config)

    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    return celery


def get_active_tasks():
    control = Control(celery)
    active_tasks = control.inspect().active()
    return active_tasks


def wait_for_tasks_completion():
    print("Checking for active tasks...")
    while True:
        active_tasks = get_active_tasks()
        if not active_tasks or all(len(tasks) == 0 for tasks in active_tasks.values()):
            print("No active tasks found. Safe to restart.")
            break
        print(f"Active tasks found: {active_tasks}")
        print("Waiting for tasks to complete...")
        time.sleep(5)  # Wait for 5 seconds before checking again


def restart_celery_worker():
    print("Stopping Celery worker...")
    subprocess.run(["pkill", "-f", "celery worker"])
    time.sleep(2)  # Give it a moment to stop
    print("Starting Celery worker...")
    subprocess.Popen(["celery", "-A", "your_app_name", "worker", "--loglevel=info"])


celery = create_celery(flask_app)
