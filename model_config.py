from config import OPENAI_API_KEY, CLAUDE_KEY, GEMINI_API_KEY

# Configuration list for various AI models
config_list = [
    # OpenAI models
    {"model": "gpt-4o", "api_key": OPENAI_API_KEY},
    {"model": "gpt-4-turbo", "api_key": OPENAI_API_KEY},
    {"model": "gpt-3.5-turbo", "api_key": OPENAI_API_KEY, "tags": ["gpt3", "turbo"]},
    {"model": "gpt-4", "api_key": OPENAI_API_KEY, "tags": ["gpt4", "openai"]},
    {"model": "gpt-4-vision-preview", "api_key": OPENAI_API_KEY},
    # {"model": "dalle", "api_key": OPENAI_API_KEY},
    {
        "model": "gpt-3.5-turbo-16k",
        "api_key": OPENAI_API_KEY,
    },  # Azure OpenAI API endpoint for gpt-3.5-turbo
    # Google models
    # {"model": "gemini-pro", "api_key": GEMINI_API_KEY, "api_type": "google"},
    # {"model": "gemini-pro-vision", "api_key": GEMINI_API_KEY, "api_type": "google"},
    # Anthropic models
    {
        "model": "claude-3-haiku-20240307",
        "base_url": "https://api.anthropic.com/v1/messages",
        "api_key": CLAUDE_KEY,
        "tags": ["claude", "haiku"],
    },
    {
        "model": "claude-3-opus-20240229",
        "base_url": "https://api.anthropic.com/v1/messages",
        "api_key": CLAUDE_KEY,
        "tags": ["claude", "opus"],
    },
]

# Default models for each provider
default_openai_model = "gpt-4o"
default_gemini_model = "gemini-pro"
default_claude_model = "claude-3-opus-20240229"
