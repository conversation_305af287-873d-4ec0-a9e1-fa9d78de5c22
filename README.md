# Analytics Service

A pure gRPC microservice for comprehensive analytics data collection, reporting, and dashboard functionality.

> **Note**: This service has been converted from a hybrid FastAPI + gRPC service to a pure gRPC-only microservice for better performance and consistency in microservice architecture. All functionality is now available exclusively through gRPC endpoints.

## Features

- **Analytics Tracking**: Usage tracking for MCP, Workflow, and Agent services
- **Dashboard Analytics**: Comprehensive dashboard metrics and overview data
- **Application Management**: Application lifecycle and metrics tracking
- **Rating Analytics**: Service rating collection and analysis
- **Performance Metrics**: Request tracking and performance monitoring
- **User Activity Reporting**: Detailed user activity and engagement analytics
- **Credit Usage Tracking**: Credit consumption monitoring and breakdown
- **Webhook Management**: Event-driven webhook notifications
- **Activation Tracking**: User activation funnel and metrics

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- PostgreSQL database

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration.

## Running the Service

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Initialize database
poetry run python -m app.db.init_db

# Start the service
poetry run python -m app.main
```

The gRPC service will start on port 50059.

## gRPC API Documentation

The Analytics Service provides two main gRPC services:

### AnalyticsService

**Core Analytics Methods:**

- `TrackEvent`: Record usage or interaction events
- `GetServiceMetrics`: Retrieve metrics for specific services
- `GetUserActivity`: Get user activity data and engagement metrics
- `GetRatingAnalytics`: Get rating statistics for services, workflows, or MCPs
- `GetOverviewAnalytics`: Get comprehensive dashboard overview data

**Dashboard Analytics Methods:**

- `GetDashboardMetrics`: Get enhanced dashboard metrics
- `GetCreditUsageBreakdown`: Get detailed credit usage breakdown by category
- `GetAppCreditUsage`: Get application-specific credit usage
- `GetLatestApiRequests`: Get recent API request logs
- `GetAgentPerformance`: Get agent performance metrics
- `GetWorkflowUtilization`: Get workflow utilization statistics
- `GetSystemActivity`: Get system activity and events
- `RecordApiRequest`: Record API request events for tracking
- `RecordSystemActivity`: Record system activity events

**Activation & Webhook Methods:**

- `TrackActivation`: Track user activation events
- `GetActivationMetrics`: Get activation funnel metrics
- `CreateWebhook`: Create webhook subscriptions
- `GetWebhooks`: List configured webhooks
- `UpdateWebhook`: Update webhook configurations
- `DeleteWebhook`: Remove webhook subscriptions
- `GetWebhookLogs`: Get webhook delivery logs

### ApplicationService

**Application Management Methods:**

- `CreateApplication`: Create new applications
- `GetApplications`: List user applications
- `GetApplication`: Get specific application details
- `UpdateApplication`: Update application configuration
- `DeleteApplication`: Remove applications
- `AttachImageToApplication`: Attach images to applications

## Development

### Project Structure

```
analytics-service/
├── app/
│   ├── core/          # Core functionality (config, security)
│   ├── db/            # Database models and session
│   ├── grpc/          # Generated gRPC code
│   ├── models/        # SQLAlchemy models
│   ├── scripts/       # Scripts for setup and maintenance
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── .env.example       # Example environment variables
├── Dockerfile         # Docker configuration
├── pyproject.toml     # Project configuration
├── poetry.lock        # Lock file for dependencies
├── README.md          # Documentation
└── run_local.sh       # Script to run the service locally
```

### Testing

Run tests with:

```bash
poetry run pytest
```

### Testing gRPC Service

To verify the gRPC service is working correctly:

```bash
# 1. Start the service
poetry run python -m app.main

# 2. In another terminal, test with grpcurl
grpcurl -plaintext localhost:50059 list

# 3. Test a specific method
grpcurl -plaintext -d '{"user_id": "test_user", "days": 7}' \
  localhost:50059 analytics.AnalyticsService/GetDashboardMetrics

# 4. Run gRPC-specific tests
poetry run pytest tests/test_grpc_dashboard_analytics.py -v
```

### Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```

## gRPC Client Usage Examples

### Python gRPC Client

```python
import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc

# Connect to the gRPC server
channel = grpc.insecure_channel('localhost:50059')
stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)

# Example: Get dashboard metrics
request = analytics_pb2.GetDashboardMetricsRequest(
    user_id="user123",
    days=7
)
response = stub.GetDashboardMetrics(request)

if response.success:
    print(f"Total credits used: {response.metrics.total_credits_used}")
    print(f"Active agents: {response.metrics.active_agents_count}")
else:
    print(f"Error: {response.message}")

# Example: Track an event
track_request = analytics_pb2.TrackEventRequest(
    event_type=analytics_pb2.EVENT_TYPE_USAGE,
    service_type=analytics_pb2.SERVICE_TYPE_AGENT,
    entity_id="agent_123",
    user_id="user123",
    metadata='{"action": "invoke", "duration": 1500}'
)
track_response = stub.TrackEvent(track_request)

channel.close()
```

### Using grpcurl for Testing

```bash
# List available services
grpcurl -plaintext localhost:50059 list

# Get dashboard metrics
grpcurl -plaintext -d '{
  "user_id": "user123",
  "days": 7
}' localhost:50059 analytics.AnalyticsService/GetDashboardMetrics

# Track an event
grpcurl -plaintext -d '{
  "event_type": "EVENT_TYPE_USAGE",
  "service_type": "SERVICE_TYPE_AGENT",
  "entity_id": "agent_123",
  "user_id": "user123",
  "metadata": "{\"action\": \"invoke\"}"
}' localhost:50059 analytics.AnalyticsService/TrackEvent
```

## Docker Usage

### Building the Image

```bash
docker build -t analytics-service .
```

### Running the Container

```bash
# Run with environment variables
docker run -d \
  --name analytics-service \
  -p 50059:50059 \
  -e DB_HOST=your_db_host \
  -e DB_PORT=5432 \
  -e DB_USER=your_db_user \
  -e DB_PASSWORD=your_db_password \
  -e DB_NAME=analytics_db \
  -e REDIS_HOST=your_redis_host \
  analytics-service

# Check logs
docker logs analytics-service

# Test the service
grpcurl -plaintext localhost:50059 list
```

## Environment Configuration

The service requires the following environment variables:

### Database Configuration

```bash
DB_HOST=localhost          # PostgreSQL host
DB_PORT=5432              # PostgreSQL port
DB_USER=analytics_user    # Database user
DB_PASSWORD=your_password # Database password
DB_NAME=analytics_db      # Database name
```

### Redis Configuration

```bash
REDIS_HOST=localhost      # Redis host
REDIS_PORT=6379          # Redis port (default: 6379)
REDIS_DB=0               # Redis database number (default: 0)
REDIS_PASSWORD=          # Redis password (optional)
```

### Service Configuration

```bash
PORT=50059               # gRPC service port (default: 50059)
ENV=production           # Environment (dev/staging/production)
DEBUG=false              # Debug mode (default: false)
```

### Optional Configuration

```bash
REPO_URL=                # Repository URL (for CI/CD)
GIT_TOKEN=               # Git token (for CI/CD)
FRONTEND_URL=            # Frontend URL (for CORS)
BOOTSTRAP_SERVERS=       # Kafka bootstrap servers (optional)
```

## Deployment

### Production Deployment

1. **Environment Setup**: Ensure all required environment variables are configured
2. **Database Migration**: Run database migrations before starting the service
3. **Health Checks**: The service exposes gRPC health checks
4. **Monitoring**: Monitor gRPC metrics and database connections
5. **Scaling**: The service is stateless and can be horizontally scaled

### Kubernetes Deployment Example

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analytics-service
  template:
    metadata:
      labels:
        app: analytics-service
    spec:
      containers:
        - name: analytics-service
          image: analytics-service:latest
          ports:
            - containerPort: 50059
              name: grpc
          env:
            - name: DB_HOST
              value: "postgres-service"
            - name: DB_PORT
              value: "5432"
            - name: REDIS_HOST
              value: "redis-service"
          # Add other environment variables
          livenessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50059"]
            initialDelaySeconds: 30
          readinessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50059"]
            initialDelaySeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: analytics-service
spec:
  selector:
    app: analytics-service
  ports:
    - port: 50059
      targetPort: 50059
      name: grpc
```

## Troubleshooting

### Common Issues

1. **gRPC Connection Failed**

   - Check if the service is running on the correct port (50059)
   - Verify firewall settings allow gRPC traffic
   - Ensure the client is using the correct server address

2. **Database Connection Issues**

   - Verify database credentials and connectivity
   - Check if database migrations have been applied
   - Ensure PostgreSQL is running and accessible

3. **Proto Definition Errors**

   - Regenerate gRPC code: `poetry run python -m app.scripts.generate_grpc`
   - Ensure proto definitions are up to date
   - Check for import path issues in generated code

4. **Performance Issues**
   - Monitor database query performance
   - Check Redis connectivity for caching
   - Review gRPC connection pool settings
   - Monitor memory and CPU usage

### Logging

The service uses structured logging. Log levels can be controlled via environment variables:

```bash
# Set log level (DEBUG, INFO, WARNING, ERROR)
export LOG_LEVEL=INFO

# Enable detailed gRPC logging
export GRPC_VERBOSITY=debug
export GRPC_TRACE=all
```
