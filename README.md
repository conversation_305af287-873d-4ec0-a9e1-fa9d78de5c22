# Ciny.ai - Auto Video Generation Platform

Ciny.ai is an innovative auto video generation platform designed to streamline and simplify the video creation process. It allows users to generate videos effortlessly from various sources, including topics, blog articles, YouTube videos, or custom scripts. Ciny.ai empowers users with advanced editing capabilities, the option to clone their voice and avatar, and the ability to create videos on trending topics and special events.

## Features

- Generate videos from various sources (topics, blog articles, YouTube videos, custom scripts)
- Advanced editing capabilities
- Voice and avatar cloning
- Create videos on trending topics and special events

## Installation

### Prerequisites

- Python 3.12.3+
- Poetry (for dependency management)
- FFmpeg (for video processing)
- ImageMagick
- gcc

### macOS Installation

1. Clone the repository:

   ```
   git clone https://gitlab.rapidinnovation.tech/ciny-ai/ciny-python-backend
   cd ciny-python-backend
   ```

2. Install system dependencies using Homebrew:

   ```
   brew install imagemagick ffmpeg
   ```

3. Install Poetry:

   ```
   curl -sSL https://install.python-poetry.org | python3 -
   ```

4. Install project dependencies:

   ```
   poetry install
   ```

5. Install additional Python packages:
   ```
   pip install numpy opencv-python-headless moviepy langchain langchain-community youtube-transcript-api celery eventlet
   ```

### Windows Installation

1. Clone the repository:

   ```
   git clone https://gitlab.rapidinnovation.tech/ciny-ai/ciny-python-backend
   cd ciny-python-backend
   ```

2. Install system dependencies:

   - Download and install ImageMagick from https://imagemagick.org/script/download.php
   - Download and install FFmpeg from https://ffmpeg.org/download.html
   - Add both to your system PATH

3. Install Poetry:

   ```
   (Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -
   ```

4. Install project dependencies:

   ```
   poetry install
   ```

5. Install additional Python packages:
   ```
   pip install numpy opencv-python-headless moviepy langchain langchain-community youtube-transcript-api celery eventlet
   ```

## Running the Application

1. Activate the virtual environment:

   ```
   poetry shell
   ```

2. Run the application:

   ```
   python run.py
   ```

3. Start the Celery worker for background processing:
   ```
   celery -A celery_worker.celery worker --loglevel=info --pool=eventlet
   ```

## Docker

To run the application using Docker:

1. Build the Docker image:

   ```
   docker build -t ciny-python-backend .
   ```

2. Run the container:
   ```
   docker run -p 5001:5001 ciny-python-backend
   ```

## Usage

1. Log in to your account or sign up if you're a new user.
2. Choose your video source (topic, blog article, YouTube video, or custom script).
3. Customize your video settings, including voice and avatar options.
4. Click "Generate Video" and wait for the process to complete.
5. Review and edit your video using the advanced editing tools.
6. Export and share your finished video.

For more detailed instructions, please refer to our [User Guide](link-to-user-guide).

## Troubleshooting

If you encounter any issues during installation or running the application, please check our [Troubleshooting Guide](link-to-troubleshooting-guide) or open an issue in the project repository.
