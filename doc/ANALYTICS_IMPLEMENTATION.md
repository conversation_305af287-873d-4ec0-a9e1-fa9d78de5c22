# 📊 Enhanced Analytics Dashboard Implementation

This document provides a comprehensive overview of the enhanced analytics dashboard implementation with both REST API and gRPC support, based on the provided dashboard interface images.

## 🆕 What's New in Enhanced Version

### **Dual Protocol Support**

- ✅ **REST API**: FastAPI-based endpoints for web integration
- ✅ **gRPC**: High-performance gRPC service for microservice communication
- ✅ **Unified Data Layer**: Both protocols share the same service layer

### **Enhanced Proto Definitions**

- ✅ **Updated analytics.proto**: New message types for dashboard analytics
- ✅ **Type Safety**: Strongly typed gRPC messages
- ✅ **Backward Compatibility**: Existing proto functions remain unchanged

### **Production-Ready Features**

- ✅ **CLI Management**: Comprehensive command-line tools
- ✅ **Data Seeding**: Realistic test data generation
- ✅ **Automated Setup**: One-command setup script
- ✅ **Comprehensive Testing**: Unit tests for all components

## 🎯 Overview

The analytics dashboard provides comprehensive insights into platform performance and activity, including:

- **Dashboard Overview**: Key metrics and KPIs
- **Credit Usage Breakdown**: Token consumption by category
- **App Credit Usage**: Time series visualization
- **Platform Analytics**: Agent performance and workflow utilization
- **API Requests & Events**: Real-time activity monitoring
- **System Activity**: Issues and alerts tracking

## 🏗️ Architecture

### Database Models

#### Core Analytics Tables

1. **`dashboard_metrics`** - Daily aggregated overview metrics
2. **`credit_usage_breakdown`** - Credit usage by category
3. **`api_request_events`** - Individual API requests and events
4. **`agent_performance_metrics`** - Agent performance data
5. **`workflow_utilization_metrics`** - Workflow execution metrics
6. **`app_credit_usage`** - Application credit consumption over time
7. **`system_activity`** - System activities and issues

### Service Layer

#### DashboardAnalyticsService

- **Data Retrieval**: Query methods for dashboard components
- **Data Recording**: Methods to record events and metrics
- **Aggregation Support**: Helper methods for metric calculations

#### AnalyticsAggregationService

- **Daily Aggregation**: Processes raw data into daily metrics
- **Backfill Support**: Historical data processing
- **Performance Optimization**: Efficient bulk operations

#### AnalyticsDataSeeder

- **Test Data Generation**: Creates realistic sample data
- **Development Support**: Populates database for testing
- **Configurable Parameters**: Customizable data generation

## 📈 Dashboard Components

### 1. Dashboard Overview

**Metrics Displayed:**

- Active Agents: 45 (Currently deployed)
- Credit Usage: $1,915.88 (+$45.56 This month)
- Agent Requests: 4,283 (+22% Last 30 days)
- Workflow Requests: 1,259 (+18% Last 30 days)
- Custom MCPs: 12 (+3 Active components)

**API Endpoint:** `GET /api/v1/analytics/overview`

**Query Parameters:**

- `user_id` (optional): User-specific metrics
- `days` (default: 7): Time period for data

### 2. Credit Usage Breakdown

**Visualization:** Bar chart showing token consumption by category

- Agents
- Workflows
- Custom MCPs
- App Credits
- Other

**API Endpoint:** `GET /api/v1/analytics/credit-usage-breakdown`

### 3. App Credit Usage

**Visualization:** Time series chart showing credit consumption over time

- Total App Credits: 2450 ($245.00)
- Daily credit usage trends
- Cost tracking over time

**API Endpoint:** `GET /api/v1/analytics/app-credit-usage`

### 4. Platform Analytics

**Components:**

- Agent Performance tab
- Workflow Utilization tab
- Time series charts with requests and completion rates

**API Endpoints:**

- `GET /api/v1/analytics/agent-performance`
- `GET /api/v1/analytics/workflow-utilization`

### 5. Latest API Requests & Events

**Real-time Activity Table:**

- Type (API Request, Workflow Exec, Auth Event)
- Endpoint
- Status (Success, Error, Pending)
- Time and Duration
- User information

**API Endpoint:** `GET /api/v1/analytics/api-requests`

### 6. System Activity

**Activity Types:**

- New Customer Inquiry
- Resolution Time Alert
- System maintenance events

**API Endpoint:** `GET /api/v1/analytics/system-activity`

## 🚀 Getting Started

### Quick Setup (Recommended)

Use the automated setup script for a complete installation:

```bash
# Run the enhanced setup script
python setup_enhanced_analytics.py
```

This script will:

- ✅ Check Python and Poetry installation
- ✅ Install all dependencies
- ✅ Generate gRPC code from proto files
- ✅ Set up database and run migrations
- ✅ Create startup scripts
- ✅ Seed sample data (optional)
- ✅ Run tests (optional)

### Manual Setup

If you prefer manual setup:

#### 1. Install Dependencies

```bash
# Install Python dependencies
poetry add fastapi uvicorn grpcio grpcio-tools sqlalchemy alembic psycopg2-binary pydantic click pytest

# Generate gRPC code
python -m grpc_tools.protoc --proto_path=proto-definitions --python_out=app/grpc --grpc_python_out=app/grpc analytics.proto
```

#### 2. Database Setup

```bash
# Apply the analytics migration
alembic upgrade head
```

#### 3. Seed Sample Data

```bash
# Seed 7 days of sample data
python -m app.cli.analytics_commands seed-data --days 7 --events-per-day 100

# Seed with specific user IDs
python -m app.cli.analytics_commands seed-data --days 7 --user-ids "user1,user2,user3"

# Seed without running aggregation
python -m app.cli.analytics_commands seed-data --days 7 --no-aggregation
```

#### 4. Start the Servers

```bash
# Start both gRPC and REST API servers
python -m app.cli.analytics_commands serve

# Start only gRPC server
python -m app.cli.analytics_commands serve --grpc-only

# Start only REST API server
python -m app.cli.analytics_commands serve --rest-only

# Or use the generated startup scripts
./start_both.sh    # Both servers
./start_grpc.sh    # gRPC only
./start_rest.sh    # REST API only
```

### 3. Run Daily Aggregation

Process raw data into daily metrics:

```bash
# Aggregate yesterday's data
python -m app.cli.analytics_commands aggregate-daily

# Aggregate specific date
python -m app.cli.analytics_commands aggregate-daily --date 2024-01-15

# User-specific aggregation
python -m app.cli.analytics_commands aggregate-daily --user-id user123
```

### 4. Backfill Historical Data

Process multiple days of historical data:

```bash
# Backfill last 30 days
python -m app.cli.analytics_commands backfill-aggregations \
  --start-date 2024-01-01 \
  --end-date 2024-01-30
```

### 5. View Current Metrics

Check current dashboard state:

```bash
# Show overview for last 7 days
python -m app.cli.analytics_commands show-overview

# Show user-specific overview
python -m app.cli.analytics_commands show-overview --user-id user123 --days 30
```

## 🔧 API Usage Examples

### REST API Examples

#### Get Dashboard Overview

```python
import requests

response = requests.get(
    "http://localhost:8000/api/v1/analytics/overview",
    params={"user_id": "user123", "days": 7}
)
overview = response.json()
print(f"Active Agents: {overview['active_agents']}")
print(f"Credit Usage: ${overview['credit_usage']:.2f}")
```

#### Record API Request Event

```python
import requests

event_data = {
    "request_type": "agent_invoke",
    "endpoint": "/api/agents/invoke",
    "status": "success",
    "duration_ms": 320,
    "user_id": "user123",
    "user_email": "<EMAIL>",
    "credits_used": 50.0,
    "cost": 0.50
}

response = requests.post(
    "http://localhost:8000/api/v1/analytics/record-api-request",
    json=event_data
)
```

#### Get Credit Usage Breakdown

```python
response = requests.get(
    "http://localhost:8000/api/v1/analytics/credit-usage-breakdown",
    params={"days": 7}
)
breakdown = response.json()

for item in breakdown:
    print(f"{item['category']}: {item['credits_used']} credits")
```

### gRPC Examples

#### Get Dashboard Metrics

```python
import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc

# Connect to gRPC server
channel = grpc.insecure_channel('localhost:50051')
stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)

# Create request
request = analytics_pb2.GetDashboardMetricsRequest(
    user_id="user123",
    days=7
)

# Make gRPC call
response = stub.GetDashboardMetrics(request)

if response.success:
    print(f"Active Agents: {response.metrics.active_agents}")
    print(f"Credit Usage: ${response.metrics.credit_usage:.2f}")
    print(f"Total Cost: ${response.metrics.total_cost:.2f}")
else:
    print(f"Error: {response.message}")

channel.close()
```

#### Record API Request via gRPC

```python
import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc

channel = grpc.insecure_channel('localhost:50051')
stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)

request = analytics_pb2.RecordApiRequestRequest(
    request_type=analytics_pb2.REQUEST_TYPE_AGENT_INVOKE,
    endpoint="/api/agents/invoke",
    status=analytics_pb2.REQUEST_STATUS_SUCCESS,
    duration_ms=320,
    user_id="user123",
    user_email="<EMAIL>",
    method="POST",
    credits_used=50.0,
    cost=0.50
)

response = stub.RecordApiRequest(request)
print(f"Event recorded: {response.event_id}")

channel.close()
```

#### Get Credit Usage Breakdown via gRPC

```python
import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc

channel = grpc.insecure_channel('localhost:50051')
stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)

request = analytics_pb2.GetCreditUsageBreakdownRequest(
    user_id="user123",
    days=7
)

response = stub.GetCreditUsageBreakdown(request)

if response.success:
    for item in response.breakdown:
        print(f"{item.category}: {item.credits_used} credits (${item.cost:.2f})")
else:
    print(f"Error: {response.message}")

channel.close()
```

#### Using the gRPC Client Helper

```python
from app.utils.grpc_client_example import AnalyticsGRPCClient

# Create client and connect
client = AnalyticsGRPCClient("localhost:50051")
client.connect()

# Get dashboard metrics
metrics = client.get_dashboard_metrics("user123", 7)

# Get credit breakdown
breakdown = client.get_credit_usage_breakdown("user123", 7)

# Record an API request
event_id = client.record_api_request("/api/test", "success", "user123")

# Disconnect
client.disconnect()
```

## 🧪 Testing

### Run Analytics Tests

```bash
# Run all analytics tests
pytest tests/test_dashboard_analytics.py -v

# Run specific test class
pytest tests/test_dashboard_analytics.py::TestDashboardAnalyticsService -v

# Run with coverage
pytest tests/test_dashboard_analytics.py --cov=app.services.dashboard_analytics_service
```

### Test Data Generation

```python
from app.utils.analytics_data_seeder import AnalyticsDataSeeder
from app.core.database import get_db

db = next(get_db())
seeder = AnalyticsDataSeeder(db)

# Generate test data
summary = seeder.run_full_seed(
    days=7,
    user_ids=["test_user_1", "test_user_2"],
    run_aggregation=True
)

print(f"Generated {summary['total_records']} records")
```

## 📊 Data Flow

### 1. Event Recording

```
API Request → record_api_request() → api_request_events table
```

### 2. Daily Aggregation

```
Raw Events → aggregate_daily_metrics() → dashboard_metrics table
Raw Events → aggregate_credit_usage_breakdown() → credit_usage_breakdown table
Raw Events → aggregate_agent_performance() → agent_performance_metrics table
Raw Events → aggregate_workflow_utilization() → workflow_utilization_metrics table
```

### 3. Dashboard Queries

```
dashboard_metrics → get_dashboard_overview() → Dashboard Overview
credit_usage_breakdown → get_credit_usage_breakdown() → Bar Chart
api_request_events → get_latest_api_requests() → Activity Table
```

## 🔍 Monitoring & Maintenance

### Daily Aggregation Schedule

Set up a cron job or scheduled task to run daily aggregation:

```bash
# Add to crontab for daily execution at 1 AM
0 1 * * * /path/to/python -m app.cli.analytics_commands aggregate-daily
```

### Performance Optimization

1. **Indexes**: All tables include appropriate indexes for query performance
2. **Partitioning**: Consider partitioning large tables by date
3. **Archival**: Implement data retention policies for old events
4. **Caching**: Add Redis caching for frequently accessed metrics

### Data Retention

```sql
-- Example: Delete events older than 90 days
DELETE FROM api_request_events
WHERE timestamp < NOW() - INTERVAL '90 days';

-- Archive old aggregated metrics
INSERT INTO dashboard_metrics_archive
SELECT * FROM dashboard_metrics
WHERE date < NOW() - INTERVAL '1 year';
```

## 🚨 Troubleshooting

### Common Issues

1. **Missing Aggregations**: Run backfill command for missing dates
2. **Performance Issues**: Check database indexes and query plans
3. **Data Inconsistencies**: Verify aggregation logic and re-run if needed

### Debug Commands

```bash
# Check recent API events
python -c "
from app.services.dashboard_analytics_service import DashboardAnalyticsService
from app.core.database import get_db
db = next(get_db())
service = DashboardAnalyticsService(db)
events = service.get_latest_api_requests(limit=10)
for event in events:
    print(f'{event[\"timestamp\"]}: {event[\"endpoint\"]} - {event[\"status\"]}')
"

# Verify aggregation data
python -m app.cli.analytics_commands show-overview --days 1
```

## 📝 Next Steps

1. **Real-time Updates**: Implement WebSocket connections for live dashboard updates
2. **Advanced Analytics**: Add machine learning insights and anomaly detection
3. **Custom Dashboards**: Allow users to create personalized dashboard views
4. **Export Features**: Add data export capabilities (CSV, PDF reports)
5. **Alerting**: Implement threshold-based alerting for key metrics
