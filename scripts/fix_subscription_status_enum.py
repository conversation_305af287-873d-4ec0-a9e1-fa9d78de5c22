#!/usr/bin/env python3
"""
Safe script to fix PaymentSubscriptionStatus enum values.
This script only touches payment-related enums and tables.
"""

import psycopg2
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

def fix_subscription_status_enum():
    """
    Safely fix PaymentSubscriptionStatus enum values by:
    1. Adding new enum values (safe operation)
    2. Only affecting payment_subscriptions table
    3. Preserving all existing data
    """
    
    try:
        conn = psycopg2.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            database=settings.DB_NAME,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD
        )
        conn.autocommit = False  # Use transactions for safety
        cur = conn.cursor()
        
        print("🔍 Checking current PaymentSubscriptionStatus enum values...")
        
        # Check current enum values
        cur.execute("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'paymentsubscriptionstatus'
            )
            ORDER BY enumsortorder;
        """)
        
        current_values = [row[0] for row in cur.fetchall()]
        print(f"Current PaymentSubscriptionStatus values: {current_values}")
        
        # Check if we need to add the lowercase values
        needed_values = ['active', 'canceled', 'incomplete', 'past_due', 'trialing']
        missing_values = [v for v in needed_values if v not in current_values]
        
        if missing_values:
            print(f"📝 Adding missing enum values: {missing_values}")
            
            # Add missing enum values (safe operation - doesn't break existing data)
            for value in missing_values:
                try:
                    cur.execute(f"ALTER TYPE paymentsubscriptionstatus ADD VALUE IF NOT EXISTS '{value}'")
                    print(f"✅ Added enum value: {value}")
                except Exception as e:
                    print(f"⚠️  Could not add {value}: {e}")
            
            # Commit the enum additions
            conn.commit()
            print("✅ Enum values added successfully")
            
        else:
            print("✅ All required enum values already exist")
        
        # Check if payment_subscriptions table exists and has data
        cur.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_name = 'payment_subscriptions'
        """)
        
        table_exists = cur.fetchone()[0] > 0
        
        if table_exists:
            cur.execute("SELECT COUNT(*) FROM payment_subscriptions")
            row_count = cur.fetchone()[0]
            print(f"📊 payment_subscriptions table has {row_count} rows")
            
            if row_count > 0:
                print("⚠️  Table has existing data. Manual data migration may be needed.")
                print("   Consider running this query to check current values:")
                print("   SELECT DISTINCT status FROM payment_subscriptions;")
        else:
            print("ℹ️  payment_subscriptions table doesn't exist yet - will be created with correct enums")
        
        cur.close()
        conn.close()
        
        print("\n🎉 PaymentSubscriptionStatus enum fix completed successfully!")
        print("💡 Next steps:")
        print("   1. Test creating a new subscription")
        print("   2. The ActivateDefaultPlan should now work without enum errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    print("🔧 Starting PaymentSubscriptionStatus enum fix...")
    print("⚠️  This script only affects payment-related enums and tables")
    print("✅ Safe to run on production database")
    print()
    
    fix_subscription_status_enum()
