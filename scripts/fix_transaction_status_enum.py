#!/usr/bin/env python3
"""
Safe script to fix PaymentTransactionStatus enum values.
This script only touches payment-related enums and tables.
"""

import psycopg2
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

def fix_transaction_status_enum():
    """
    Safely fix PaymentTransactionStatus enum values by:
    1. Adding new enum values (safe operation)
    2. Only affecting payment_transactions table
    3. Preserving all existing data
    """
    
    try:
        conn = psycopg2.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            database=settings.DB_NAME,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD
        )
        conn.autocommit = False  # Use transactions for safety
        cur = conn.cursor()
        
        print("🔍 Checking current PaymentTransactionStatus enum values...")
        
        # Check current enum values
        cur.execute("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'paymenttransactionstatus'
            )
            ORDER BY enumsortorder;
        """)
        
        current_values = [row[0] for row in cur.fetchall()]
        print(f"Current PaymentTransactionStatus values: {current_values}")
        
        # Check if we need to add the lowercase values
        needed_values = ['completed', 'pending', 'failed', 'refunded']
        missing_values = [v for v in needed_values if v not in current_values]
        
        if missing_values:
            print(f"📝 Adding missing enum values: {missing_values}")
            
            # Add missing enum values (safe operation - doesn't break existing data)
            for value in missing_values:
                try:
                    cur.execute(f"ALTER TYPE paymenttransactionstatus ADD VALUE IF NOT EXISTS '{value}'")
                    print(f"✅ Added enum value: {value}")
                except Exception as e:
                    print(f"⚠️  Could not add {value}: {e}")
            
            # Commit the enum additions
            conn.commit()
            print("✅ Enum values added successfully")
            
        else:
            print("✅ All required enum values already exist")
        
        # Check if payment_transactions table exists and has data
        cur.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_name = 'payment_transactions'
        """)
        
        table_exists = cur.fetchone()[0] > 0
        
        if table_exists:
            cur.execute("SELECT COUNT(*) FROM payment_transactions")
            row_count = cur.fetchone()[0]
            print(f"📊 payment_transactions table has {row_count} rows")
            
            if row_count > 0:
                print("⚠️  Table has existing data. Manual data migration may be needed.")
                print("   Consider running this query to check current values:")
                print("   SELECT DISTINCT status FROM payment_transactions;")
        else:
            print("ℹ️  payment_transactions table doesn't exist yet - will be created with correct enums")
        
        cur.close()
        conn.close()
        
        print("\n🎉 PaymentTransactionStatus enum fix completed successfully!")
        print("💡 Next steps:")
        print("   1. Test creating a new transaction")
        print("   2. The checkout session should now work without enum errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    print("🔧 Starting PaymentTransactionStatus enum fix...")
    print("⚠️  This script only affects payment-related enums and tables")
    print("✅ Safe to run on production database")
    print()
    
    fix_transaction_status_enum()
