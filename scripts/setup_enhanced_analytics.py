#!/usr/bin/env python3
"""
Enhanced Analytics Service Setup Script
Comprehensive setup for the analytics dashboard functionality
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command, description, check=True):
    """Run a shell command with logging"""
    logger.info(f"🔧 {description}")
    logger.info(f"Running: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=check,
            capture_output=True,
            text=True
        )
        
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        
        if result.stderr and result.returncode != 0:
            logger.error(f"Error: {result.stderr.strip()}")
            
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    logger.info("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True


def check_poetry():
    """Check if Poetry is installed"""
    logger.info("📦 Checking Poetry installation...")
    
    result = run_command("poetry --version", "Checking Poetry", check=False)
    if not result:
        logger.error("Poetry is not installed. Please install Poetry first:")
        logger.error("curl -sSL https://install.python-poetry.org | python3 -")
        return False
    
    logger.info("✅ Poetry is installed")
    return True


def install_dependencies():
    """Install Python dependencies"""
    logger.info("📚 Installing Python dependencies...")
    
    dependencies = [
        "fastapi",
        "uvicorn[standard]",
        "grpcio",
        "grpcio-tools",
        "sqlalchemy",
        "alembic",
        "psycopg2-binary",
        "pydantic",
        "click",
        "pytest",
        "pytest-asyncio",
        "python-multipart"
    ]
    
    for dep in dependencies:
        if not run_command(f"poetry add {dep}", f"Installing {dep}"):
            logger.error(f"Failed to install {dep}")
            return False
    
    logger.info("✅ All dependencies installed")
    return True


def generate_grpc_code():
    """Generate gRPC Python code from proto files"""
    logger.info("🔄 Generating gRPC Python code...")
    
    proto_dir = Path("proto-definitions")
    grpc_dir = Path("app/grpc")
    
    if not proto_dir.exists():
        logger.error("proto-definitions directory not found")
        return False
    
    # Create gRPC directory if it doesn't exist
    grpc_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate Python code from proto files
    command = (
        f"python -m grpc_tools.protoc "
        f"--proto_path={proto_dir} "
        f"--python_out={grpc_dir} "
        f"--grpc_python_out={grpc_dir} "
        f"{proto_dir}/analytics.proto"
    )
    
    if not run_command(command, "Generating gRPC code"):
        logger.error("Failed to generate gRPC code")
        return False
    
    logger.info("✅ gRPC code generated successfully")
    return True


def setup_database():
    """Set up database and run migrations"""
    logger.info("🗄️ Setting up database...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning("⚠️  .env file not found. Creating a sample one...")
        
        sample_env = """
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/analytics_db

# Server Configuration
GRPC_PORT=50051
REST_PORT=8000

# Logging
LOG_LEVEL=INFO
"""
        
        with open(env_file, 'w') as f:
            f.write(sample_env.strip())
        
        logger.info("📝 Sample .env file created. Please update with your database credentials.")
    
    # Run database migrations
    if not run_command("alembic upgrade head", "Running database migrations"):
        logger.warning("⚠️  Database migrations failed. Make sure your database is running and configured correctly.")
        return False
    
    logger.info("✅ Database setup completed")
    return True


def seed_sample_data():
    """Seed the database with sample data"""
    logger.info("🌱 Seeding sample data...")
    
    command = "python -m app.cli.analytics_commands seed-data --days 7 --events-per-day 50"
    
    if not run_command(command, "Seeding sample data", check=False):
        logger.warning("⚠️  Sample data seeding failed. You can run it manually later.")
        return False
    
    logger.info("✅ Sample data seeded successfully")
    return True


def run_tests():
    """Run the test suite"""
    logger.info("🧪 Running tests...")
    
    if not run_command("poetry run pytest tests/ -v", "Running tests", check=False):
        logger.warning("⚠️  Some tests failed. Check the output above.")
        return False
    
    logger.info("✅ All tests passed")
    return True


def create_startup_scripts():
    """Create convenient startup scripts"""
    logger.info("📜 Creating startup scripts...")
    
    # Create start_grpc.sh
    grpc_script = """#!/bin/bash
echo "🚀 Starting Analytics gRPC Server..."
python -m app.cli.analytics_commands serve --grpc-only
"""
    
    with open("start_grpc.sh", 'w') as f:
        f.write(grpc_script)
    
    os.chmod("start_grpc.sh", 0o755)
    
    # Create start_rest.sh
    rest_script = """#!/bin/bash
echo "🌐 Starting Analytics REST API Server..."
python -m app.cli.analytics_commands serve --rest-only
"""
    
    with open("start_rest.sh", 'w') as f:
        f.write(rest_script)
    
    os.chmod("start_rest.sh", 0o755)
    
    # Create start_both.sh
    both_script = """#!/bin/bash
echo "🚀 Starting Both gRPC and REST API Servers..."
python -m app.cli.analytics_commands serve
"""
    
    with open("start_both.sh", 'w') as f:
        f.write(both_script)
    
    os.chmod("start_both.sh", 0o755)
    
    logger.info("✅ Startup scripts created:")
    logger.info("   - start_grpc.sh: Start only gRPC server")
    logger.info("   - start_rest.sh: Start only REST API server")
    logger.info("   - start_both.sh: Start both servers")
    
    return True


def main():
    """Main setup function"""
    logger.info("🎯 Enhanced Analytics Service Setup")
    logger.info("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Checking Poetry", check_poetry),
        ("Installing dependencies", install_dependencies),
        ("Generating gRPC code", generate_grpc_code),
        ("Setting up database", setup_database),
        ("Creating startup scripts", create_startup_scripts),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            logger.error(f"❌ {step_name} failed with exception: {e}")
            failed_steps.append(step_name)
    
    # Optional steps
    optional_steps = [
        ("Seeding sample data", seed_sample_data),
        ("Running tests", run_tests),
    ]
    
    logger.info("\n🔧 Running optional setup steps...")
    
    for step_name, step_func in optional_steps:
        try:
            step_func()
        except Exception as e:
            logger.warning(f"⚠️  {step_name} failed: {e}")
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Setup Summary")
    
    if failed_steps:
        logger.error(f"❌ Failed steps: {', '.join(failed_steps)}")
        logger.error("Please fix the issues above and run the setup again.")
        return False
    else:
        logger.info("✅ All core setup steps completed successfully!")
        
        logger.info("\n🚀 Next Steps:")
        logger.info("1. Update .env file with your database credentials")
        logger.info("2. Start the servers:")
        logger.info("   - Both servers: ./start_both.sh")
        logger.info("   - gRPC only: ./start_grpc.sh")
        logger.info("   - REST API only: ./start_rest.sh")
        logger.info("3. Access the API documentation at: http://localhost:8000/docs")
        logger.info("4. Test gRPC service: python -m app.cli.analytics_commands test-grpc")
        
        logger.info("\n📚 Available CLI Commands:")
        logger.info("   - Seed data: python -m app.cli.analytics_commands seed-data")
        logger.info("   - Aggregate data: python -m app.cli.analytics_commands aggregate-daily")
        logger.info("   - Show overview: python -m app.cli.analytics_commands show-overview")
        
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
