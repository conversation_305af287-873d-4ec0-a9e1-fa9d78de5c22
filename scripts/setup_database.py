#!/usr/bin/env python3
"""
Analytics Service Database Setup Script
Sets up the database with optimized schema and indexes for efficient analytics queries.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.models.analytics import Base

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:password@localhost:5432/analytics_db")

def create_tables(engine):
    """Create all tables using SQLAlchemy models"""
    print("Creating database tables...")
    Base.metadata.create_all(engine)
    print("✓ Tables created successfully")

def run_sql_file(engine, file_path: Path):
    """Execute SQL commands from a file"""
    print(f"Executing SQL file: {file_path}")
    
    with open(file_path, 'r') as file:
        sql_content = file.read()
    
    # Split by semicolon and execute each statement
    statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
    
    with engine.connect() as conn:
        for statement in statements:
            if statement:
                try:
                    conn.execute(text(statement))
                    conn.commit()
                except Exception as e:
                    print(f"Warning: Failed to execute statement: {e}")
                    # Continue with other statements
    
    print(f"✓ SQL file executed: {file_path}")

def setup_database():
    """Main database setup function"""
    print("Starting Analytics Service database setup...")
    print(f"Database URL: {DATABASE_URL}")
    
    # Create database engine
    engine = create_engine(DATABASE_URL)
    
    try:
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✓ Database connection successful")
        
        # Create tables
        create_tables(engine)
        
        # Run optimization scripts
        migrations_dir = Path(__file__).parent.parent / "migrations"
        
        # Execute index creation script
        indexes_file = migrations_dir / "create_analytics_indexes.sql"
        if indexes_file.exists():
            run_sql_file(engine, indexes_file)
        else:
            print(f"Warning: Index file not found: {indexes_file}")
        
        print("✓ Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Run the data seeding script to populate sample data:")
        print("   python scripts/seed_analytics_data.py")
        print("2. Start the analytics service:")
        print("   python app/main.py")
        
    except Exception as e:
        print(f"✗ Database setup failed: {str(e)}")
        sys.exit(1)
    finally:
        engine.dispose()

def check_prerequisites():
    """Check if required dependencies are available"""
    print("Checking prerequisites...")
    
    try:
        import sqlalchemy
        print(f"✓ SQLAlchemy version: {sqlalchemy.__version__}")
    except ImportError:
        print("✗ SQLAlchemy not found. Install with: pip install sqlalchemy")
        return False
    
    try:
        import psycopg2
        print(f"✓ psycopg2 available")
    except ImportError:
        try:
            import psycopg2_binary
            print(f"✓ psycopg2-binary available")
        except ImportError:
            print("✗ PostgreSQL adapter not found. Install with: pip install psycopg2-binary")
            return False
    
    return True

def main():
    """Main function"""
    print("=" * 60)
    print("Analytics Service Database Setup")
    print("=" * 60)
    
    if not check_prerequisites():
        print("\nPlease install missing dependencies and try again.")
        sys.exit(1)
    
    setup_database()

if __name__ == "__main__":
    main()
