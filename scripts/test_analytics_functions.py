#!/usr/bin/env python3
"""
Analytics Service Function Testing Script
Tests all implemented analytics functions to ensure they work correctly.
"""

import os
import sys
import json
from datetime import datetime, timezone

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.services.analytics_service import AnalyticsService


class MockContext:
    """Mock gRPC context for testing"""

    def __init__(self):
        self.code = None
        self.details = None

    def set_code(self, code):
        self.code = code

    def set_details(self, details):
        self.details = details


def test_dashboard_overview():
    """Test the main dashboard overview function"""
    print("\n" + "=" * 50)
    print("Testing Dashboard Overview")
    print("=" * 50)

    service = AnalyticsService()
    context = MockContext()

    request = analytics_pb2.GetDashboardOverviewRequest(user_id="user_001", time_period_days=7)

    try:
        response = service.GetDashboardOverview(request, context)

        if response.success:
            print("✓ Dashboard overview retrieved successfully")
            overview = response.overview
            print(f"  Active Agents: {overview.active_agents}")
            print(f"  Credit Usage: ${overview.credit_usage:.2f}")
            print(f"  Agent Requests: {overview.agent_requests}")
            print(f"  Workflow Requests: {overview.workflow_requests}")
            print(f"  Custom MCPs: {overview.custom_mcps}")
            print(f"  Credit Breakdown: {dict(overview.credit_breakdown)}")
            print(f"  App Credit Usage Points: {len(overview.app_credit_usage)}")
            print(f"  Agent Performance Points: {len(overview.agent_performance)}")
            print(f"  Recent Events: {len(overview.recent_events)}")
        else:
            print(f"✗ Dashboard overview failed: {response.message}")

    except Exception as e:
        print(f"✗ Dashboard overview error: {str(e)}")


def test_application_management():
    """Test application CRUD operations"""
    print("\n" + "=" * 50)
    print("Testing Application Management")
    print("=" * 50)

    service = AnalyticsService()
    context = MockContext()

    # Test Create Application
    create_request = analytics_pb2.CreateApplicationRequest(
        user_id="test_user",
        name="Test Application",
        description="Test application for analytics",
        environment="development",
        tags=["test", "analytics"],
    )

    try:
        create_response = service.CreateApplication(create_request, context)
        if create_response.success:
            print("✓ Application created successfully")
            app_id = create_response.application.id

            # Test Get Applications
            get_request = analytics_pb2.GetApplicationsRequest(
                user_id="test_user", limit=10, offset=0
            )

            get_response = service.GetApplications(get_request, context)
            if get_response.success:
                print(f"✓ Retrieved {len(get_response.applications)} applications")
            else:
                print(f"✗ Get applications failed: {get_response.message}")

        else:
            print(f"✗ Application creation failed: {create_response.message}")

    except Exception as e:
        print(f"✗ Application management error: {str(e)}")


def test_api_key_management():
    """Test API key operations"""
    print("\n" + "=" * 50)
    print("Testing API Key Management")
    print("=" * 50)

    service = AnalyticsService()
    context = MockContext()

    # First create an application
    app_request = analytics_pb2.CreateApplicationRequest(
        user_id="test_user",
        name="Test App for API Keys",
        description="Test application for API key testing",
    )

    try:
        app_response = service.CreateApplication(app_request, context)
        if app_response.success:
            app_id = app_response.application.id

            # Test Create API Key
            key_request = analytics_pb2.CreateAPIKeyRequest(
                application_id=app_id,
                user_id="test_user",
                name="Test API Key",
                description="Test API key for analytics",
                scopes=["read", "write"],
            )

            key_response = service.CreateAPIKey(key_request, context)
            if key_response.success:
                print("✓ API key created successfully")
                print(f"  Public Key: {key_response.api_key.public_key}")
                print(f"  Private Key: {key_response.private_key[:20]}...")

                # Test Get API Keys
                get_keys_request = analytics_pb2.GetAPIKeysRequest(
                    application_id=app_id, user_id="test_user"
                )

                get_keys_response = service.GetAPIKeys(get_keys_request, context)
                if get_keys_response.success:
                    print(f"✓ Retrieved {len(get_keys_response.api_keys)} API keys")
                else:
                    print(f"✗ Get API keys failed: {get_keys_response.message}")

            else:
                print(f"✗ API key creation failed: {key_response.message}")
        else:
            print(f"✗ Application creation failed: {app_response.message}")

    except Exception as e:
        print(f"✗ API key management error: {str(e)}")


def test_webhook_management():
    """Test webhook operations"""
    print("\n" + "=" * 50)
    print("Testing Webhook Management")
    print("=" * 50)

    service = AnalyticsService()
    context = MockContext()

    # Test Create Webhook
    webhook_request = analytics_pb2.CreateWebhookRequest(
        user_id="test_user",
        url="https://api.example.com/webhook",
        event_types=[
            analytics_pb2.WebhookEventType.WEBHOOK_EVENT_TYPE_AGENT_INVOKED,
            analytics_pb2.WebhookEventType.WEBHOOK_EVENT_TYPE_WORKFLOW_EXECUTED,
        ],
        description="Test webhook for analytics",
        is_active=True,
    )

    try:
        webhook_response = service.CreateWebhook(webhook_request, context)
        if webhook_response.success:
            print("✓ Webhook created successfully")
            print(f"  URL: {webhook_response.webhook.url}")
            print(f"  Event Types: {len(webhook_response.webhook.event_types)}")

            # Test Get Webhooks
            get_webhooks_request = analytics_pb2.GetWebhooksRequest(user_id="test_user")

            get_webhooks_response = service.GetWebhooks(get_webhooks_request, context)
            if get_webhooks_response.success:
                print(f"✓ Retrieved {len(get_webhooks_response.webhooks)} webhooks")
            else:
                print(f"✗ Get webhooks failed: {get_webhooks_response.message}")

        else:
            print(f"✗ Webhook creation failed: {webhook_response.message}")

    except Exception as e:
        print(f"✗ Webhook management error: {str(e)}")


def test_activation_tracking():
    """Test activation tracking"""
    print("\n" + "=" * 50)
    print("Testing Activation Tracking")
    print("=" * 50)

    service = AnalyticsService()
    context = MockContext()

    # Test Track Activation
    activation_request = analytics_pb2.TrackActivationRequest(
        user_id="test_user",
        event_type=analytics_pb2.ActivationEventType.ACTIVATION_EVENT_TYPE_USER_SIGNUP,
        entity_id="test_user",
        metadata='{"source": "web"}',
    )

    try:
        activation_response = service.TrackActivation(activation_request, context)
        if activation_response.success:
            print("✓ Activation event tracked successfully")
            print(f"  Activation ID: {activation_response.activation_id}")

            # Test Get Activation Metrics
            metrics_request = analytics_pb2.GetActivationMetricsRequest(
                user_id="test_user", time_period_days=30
            )

            metrics_response = service.GetActivationMetrics(metrics_request, context)
            if metrics_response.success:
                print("✓ Activation metrics retrieved successfully")
                metrics = metrics_response.metrics
                print(f"  Total Signups: {metrics.total_signups}")
                print(f"  Activated Users: {metrics.activated_users}")
                print(f"  Activation Rate: {metrics.activation_rate:.2f}%")
            else:
                print(f"✗ Get activation metrics failed: {metrics_response.message}")

        else:
            print(f"✗ Activation tracking failed: {activation_response.message}")

    except Exception as e:
        print(f"✗ Activation tracking error: {str(e)}")


def run_all_tests():
    """Run all analytics function tests"""
    print("Analytics Service Function Tests")
    print("=" * 60)
    print("Testing all implemented analytics functions...")

    # Run individual tests
    test_dashboard_overview()
    test_application_management()
    test_api_key_management()
    test_webhook_management()
    test_activation_tracking()

    print("\n" + "=" * 60)
    print("Analytics Function Tests Completed")
    print("=" * 60)
    print("\nNote: Some tests may show errors if the database is empty.")
    print("Run the data seeding script first for more comprehensive testing:")
    print("  python scripts/seed_analytics_data.py")


def main():
    """Main function"""
    try:
        run_all_tests()
    except KeyboardInterrupt:
        print("\n\nTests interrupted by user")
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {str(e)}")


if __name__ == "__main__":
    main()
