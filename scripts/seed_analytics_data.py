#!/usr/bin/env python3
"""
Analytics Service Data Seeding Script
Populates the database with sample analytics data for testing dashboard functionality.
"""

import os
import sys
import uuid
import random
import json
from datetime import datetime, timezone, timedelta
from typing import List

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.analytics import (
    Base,
    AnalyticsEvent,
    ServiceMetrics,
    UserActivity,
    Application,
    APIKey,
    Webhook,
    WebhookLog,
    ActivationEvent,
    UsageEvent,
)
from app.utils.constants.constants import (
    EventType,
    ServiceType,
    AnalyticsStatus,
    ApplicationStatus,
    APIKeyStatus,
    WebhookStatus,
    ActivationEventType,
)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:password@localhost:5432/analytics_db")


def create_sample_users() -> List[str]:
    """Create sample user IDs"""
    return ["user_001", "user_002", "user_003", "user_004", "user_005"]


def create_sample_applications(session, user_ids: List[str]) -> List[str]:
    """Create sample applications"""
    app_ids = []
    environments = ["development", "staging", "production"]

    for user_id in user_ids:
        for i in range(random.randint(1, 3)):
            app = Application(
                user_id=user_id,
                name=f"App {i+1} - {user_id}",
                description=f"Sample application {i+1} for user {user_id}",
                environment=random.choice(environments),
                tags=random.sample(
                    ["web", "mobile", "api", "ml", "analytics"], random.randint(1, 3)
                ),
                status=ApplicationStatus.ACTIVE,
            )
            session.add(app)
            session.flush()
            app_ids.append(app.id)

    return app_ids


def create_sample_api_keys(session, app_ids: List[str], user_ids: List[str]):
    """Create sample API keys"""
    scopes_options = [["read", "write"], ["read"], ["write"], ["admin"], ["read", "write", "admin"]]

    for app_id in app_ids:
        # Get the user_id for this application
        app = session.query(Application).filter(Application.id == app_id).first()
        if not app:
            continue

        for i in range(random.randint(1, 4)):
            api_key = APIKey(
                application_id=app_id,
                user_id=app.user_id,
                name=f"API Key {i+1}",
                description=f"Sample API key {i+1} for application {app_id}",
                public_key=f"ruh_pk_{uuid.uuid4().hex[:32]}",
                private_key_hash=f"hash_{uuid.uuid4().hex}",
                scopes=random.choice(scopes_options),
                status=random.choice(
                    [APIKeyStatus.ACTIVE, APIKeyStatus.ACTIVE, APIKeyStatus.REVOKED]
                ),
                expires_at=(
                    datetime.now(timezone.utc) + timedelta(days=random.randint(30, 365))
                    if random.choice([True, False])
                    else None
                ),
                usage_count=random.randint(0, 1000),
            )
            session.add(api_key)


def create_sample_webhooks(session, app_ids: List[str], user_ids: List[str]):
    """Create sample webhooks"""
    event_types_options = [
        ["agent_invoked", "workflow_executed"],
        ["mcp_used"],
        ["user_signup", "user_activated"],
        ["agent_invoked", "workflow_executed", "mcp_used"],
    ]

    for user_id in user_ids:
        user_apps = [
            app_id
            for app_id in app_ids
            if session.query(Application)
            .filter(Application.id == app_id, Application.user_id == user_id)
            .first()
        ]

        for i in range(random.randint(0, 2)):
            webhook = Webhook(
                user_id=user_id,
                application_id=(
                    random.choice(user_apps) if user_apps and random.choice([True, False]) else None
                ),
                url=f"https://api.example.com/webhooks/{uuid.uuid4().hex[:8]}",
                event_types=random.choice(event_types_options),
                secret=f"secret_{uuid.uuid4().hex[:16]}" if random.choice([True, False]) else None,
                description=f"Sample webhook {i+1} for user {user_id}",
                is_active=random.choice([True, True, False]),
                status=WebhookStatus.ACTIVE,
                delivery_count=random.randint(0, 500),
                failure_count=random.randint(0, 50),
            )
            session.add(webhook)


def create_sample_usage_events(session, user_ids: List[str]):
    """Create sample usage events for dashboard analytics"""
    entity_types = ["agent", "workflow", "mcp", "app_credit", "other"]
    actions = ["invoke", "execute", "run", "use", "create", "update", "delete"]

    # Create events for the last 30 days
    start_date = datetime.now(timezone.utc) - timedelta(days=30)

    for user_id in user_ids:
        # Create varying amounts of usage per user
        num_events = random.randint(100, 500)

        for _ in range(num_events):
            # Random date within the last 30 days
            event_date = start_date + timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59),
            )

            entity_type = random.choice(entity_types)

            usage_event = UsageEvent(
                user_id=user_id,
                entity_type=entity_type,
                entity_id=f"{entity_type}_{uuid.uuid4().hex[:8]}",
                action=random.choice(actions),
                credits_used=round(random.uniform(0.1, 10.0), 2),
                cost=round(random.uniform(0.01, 1.0), 2),
                event_metadata={
                    "success": random.choice([True, True, True, False]),
                    "duration": f"{random.randint(100, 5000)}ms",
                    "model": (
                        random.choice(["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"])
                        if entity_type == "agent"
                        else None
                    ),
                },
                created_at=event_date,
            )
            session.add(usage_event)


def create_sample_activation_events(session, user_ids: List[str]):
    """Create sample activation events"""
    activation_types = [
        "user_signup",
        "first_agent_created",
        "first_workflow_run",
        "first_mcp_used",
        "api_key_created",
    ]

    for user_id in user_ids:
        # Create activation funnel for each user
        signup_date = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 60))

        # User signup (always first)
        signup_event = ActivationEvent(
            user_id=user_id,
            event_type="user_signup",
            entity_id=user_id,
            event_metadata={"source": random.choice(["web", "mobile", "api"])},
            created_at=signup_date,
        )
        session.add(signup_event)

        # Other activation events (may or may not happen)
        current_date = signup_date
        for event_type in activation_types[1:]:
            if random.choice([True, False]):  # 50% chance of each activation step
                current_date += timedelta(days=random.randint(0, 7))
                activation_event = ActivationEvent(
                    user_id=user_id,
                    event_type=event_type,
                    entity_id=f"{event_type}_{uuid.uuid4().hex[:8]}",
                    event_metadata={"step": event_type},
                    created_at=current_date,
                )
                session.add(activation_event)


def create_sample_analytics_events(session, user_ids: List[str]):
    """Create sample analytics events"""
    service_types = ["mcp", "workflow", "agent"]
    event_types = ["usage", "creation", "rating", "error"]

    # Create events for the last 30 days
    start_date = datetime.now(timezone.utc) - timedelta(days=30)

    for user_id in user_ids:
        num_events = random.randint(50, 200)

        for _ in range(num_events):
            event_date = start_date + timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59),
            )

            service_type = random.choice(service_types)
            event_type = random.choice(event_types)

            metadata = {}
            if event_type == "rating":
                metadata["rating"] = random.randint(1, 5)
            elif event_type == "error":
                metadata["error_code"] = random.choice(["500", "404", "403", "400"])
                metadata["error_message"] = "Sample error message"

            analytics_event = AnalyticsEvent(
                event_type=event_type,
                service_type=service_type,
                entity_id=f"{service_type}_{uuid.uuid4().hex[:8]}",
                user_id=user_id,
                event_metadata=metadata,
                status=AnalyticsStatus.ACTIVE,
                created_at=event_date,
            )
            session.add(analytics_event)


def main():
    """Main seeding function"""
    print("Starting analytics data seeding...")

    # Create database engine and session
    engine = create_engine(DATABASE_URL)
    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()

    try:
        # Create sample data
        print("Creating sample users...")
        user_ids = create_sample_users()

        print("Creating sample applications...")
        app_ids = create_sample_applications(session, user_ids)
        session.commit()

        print("Creating sample API keys...")
        create_sample_api_keys(session, app_ids, user_ids)
        session.commit()

        print("Creating sample webhooks...")
        create_sample_webhooks(session, app_ids, user_ids)
        session.commit()

        print("Creating sample usage events...")
        create_sample_usage_events(session, user_ids)
        session.commit()

        print("Creating sample activation events...")
        create_sample_activation_events(session, user_ids)
        session.commit()

        print("Creating sample analytics events...")
        create_sample_analytics_events(session, user_ids)
        session.commit()

        print("Analytics data seeding completed successfully!")
        print(f"Created data for {len(user_ids)} users")
        print(f"Created {len(app_ids)} applications")

    except Exception as e:
        print(f"Error during seeding: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()


if __name__ == "__main__":
    main()
