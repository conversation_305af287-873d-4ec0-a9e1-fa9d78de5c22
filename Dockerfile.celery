# Use Python 3.12.3 as the base image
FROM python:3.12.3-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1 

# Set the working directory in the container
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    imagemagick \
    ffmpeg \
    libmagick++-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    fonts-liberation \
    && rm -rf /var/lib/apt/lists/*

# Set the ImageMagick policy to allow necessary operations
RUN sed -i 's/rights="none" pattern="@\*"/rights="read|write" pattern="@*"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/<policy domain="coder" rights="none" pattern="VIDEO" \/>/<policy domain="coder" rights="read|write" pattern="VIDEO" \/>/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/<policy domain="coder" rights="none" pattern="FILE" \/>/<policy domain="coder" rights="read|write" pattern="FILE" \/>/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/rights="none" pattern="font"/rights="read" pattern="font"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="memory" value="256MiB"/domain="resource" name="memory" value="1GiB"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="map" value="512MiB"/domain="resource" name="map" value="1GiB"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="width" value="16KP"/domain="resource" name="width" value="64KP"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="height" value="16KP"/domain="resource" name="height" value="64KP"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="coder" rights="none" pattern="http"/domain="coder" rights="read" pattern="http"/' /etc/ImageMagick-6/policy.xml

# Set the IMAGEMAGICK_BINARY environment variable
ENV IMAGEMAGICK_BINARY=/usr/bin/convert

# Install Python dependencies
RUN pip install --no-cache-dir numpy opencv-python-headless moviepy Wand langchain langchain-community youtube-transcript-api

# Install Poetry
RUN pip install --no-cache-dir poetry

# Copy only requirements to cache them in docker layer
COPY pyproject.toml poetry.lock* /app/

# Configure Poetry
RUN poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-ansi

# # Install openai-whisper using pip within the Poetry environment
# RUN poetry run pip install openai-whisper

# Copy project
COPY . /app

RUN poetry export --output=requirements.txt --without-hashes

# Install poetry and project dependencies
RUN pip install --no-cache-dir --no-deps --requirement=requirements.txt

# Install Celery and eventlet
RUN pip install celery eventlet

# Run the Celery worker
CMD ["celery", "-A", "celery_worker.celery", "worker", "--concurrency=3", "--loglevel=info", "--pool=eventlet"]