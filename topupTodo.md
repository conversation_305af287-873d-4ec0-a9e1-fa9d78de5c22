# Topup Plans Implementation Todo

## Overview
Implement topup plans functionality similar to payment_plans in payment-service. Topup plans allow adding credits over existing plans (both free and pro).

## Payment Service Tasks

### 1. Database & Models
- [x] TopupPlan model already exists in `app/models/topup.py`
- [x] Verify TopupPlan model is imported in `app/models/__init__.py`
- [ ] Create database migration for topup_plans table if needed

### 2. Proto Definitions
- [x] Update `proto-definitions/payment.proto` to include:
  - [x] TopupPlan message (similar to Plan message)
  - [x] CreateTopupCheckoutSessionRequest message
  - [x] CreateTopupCheckoutSessionResponse message
  - [x] ListTopupPlansRequest message
  - [x] ListTopupPlansResponse message
  - [x] CreateTopupPlanRequest message
  - [x] TopupPurchaseWebhook handling messages (will be handled by existing webhook)
- [x] Add topup-related RPC methods to PaymentService:
  - [x] CreateTopupCheckoutSession
  - [x] ListTopupPlans
  - [x] CreateTopupPlan

### 3. Service Implementation
- [x] Create topup service functions in `app/services/payment_service.py`:
  - [x] `CreateTopupCheckoutSession` RPC method
  - [x] `ListTopupPlans` RPC method
  - [x] `CreateTopupPlan` RPC method
  - [x] `_to_topup_plan_proto` helper method
  - [x] Webhook handling for topup purchases
  - [x] Credit addition logic for topup purchases

### 4. Initialization Logic
- [x] Create modular function `initialize_topup_plans()` in `app/services/payment_service.py`
- [x] Add topup plans initialization to `app/main.py` startup
- [x] Define default topup plans:
  - [x] Small Topup: 50 credits for $5
  - [x] Large Topup: 200 credits for $15

### 5. Schemas
- [ ] Add topup schemas to `app/schemas/payment.py`:
  - [ ] TopupPlanCreateSchema
  - [ ] TopupPlanSchema
  - [ ] CreateTopupCheckoutSessionRequestSchema
  - [ ] CreateTopupCheckoutSessionResponseSchema

### 6. API Routes (Optional)
- [ ] Add topup routes to `app/api/routers/payment_routes.py`:
  - [ ] GET /topup-plans (list available topup plans)
  - [ ] POST /topup/checkout (create topup checkout session)

### 7. gRPC Code Generation
- [ ] Regenerate gRPC code after proto updates:
  - [ ] Run proto generation script
  - [ ] Verify payment_pb2.py and payment_pb2_grpc.py are updated

## API Gateway Tasks

### 8. gRPC Client Updates
- [ ] Update `app/services/payment_service.py` in api-gateway:
  - [ ] Add `create_topup_checkout_session` method
  - [ ] Add `list_topup_plans` method
  - [ ] Add error handling for topup operations

### 9. Schemas
- [ ] Update `app/schemas/payment.py` in api-gateway:
  - [ ] Add TopupPlanSchema
  - [ ] Add CreateTopupCheckoutSessionRequestSchema
  - [ ] Add CreateTopupCheckoutSessionResponseSchema

### 10. API Routes
- [ ] Add topup routes to payment router in api-gateway:
  - [ ] GET /api/v1/payment/topup-plans
  - [ ] POST /api/v1/payment/topup/checkout

### 11. Proto Sync
- [ ] Copy updated payment.proto to api-gateway proto-definitions
- [ ] Regenerate gRPC code in api-gateway

## Implementation Details

### Default Topup Plans
```python
TOPUP_PLANS = [
    {
        "plan_id_code": "topup_small",
        "name": "Small Credit Topup",
        "credit_amount": 50,
        "price": 5.00
    },
    {
        "plan_id_code": "topup_large", 
        "name": "Large Credit Topup",
        "credit_amount": 200,
        "price": 15.00
    }
]
```

### Stripe Integration
- Create one-time payment products (not recurring)
- Handle successful payment webhooks
- Add credits to existing subscription

### Credit Addition Logic
- Find existing subscription by organisation_id
- Add topup credits to current_credits
- Log transaction as CREDIT_PURCHASE type
- Send notification/confirmation

## Testing Checklist
- [ ] Test topup plan creation on service startup
- [ ] Test topup checkout session creation
- [ ] Test webhook processing for topup purchases
- [ ] Test credit addition to existing subscriptions
- [ ] Test API endpoints in api-gateway
- [ ] Test error handling for invalid requests

## Notes
- Topup plans are one-time purchases, not recurring subscriptions
- Credits are added to existing subscription balance
- Topup plans work with both free and pro plans
- Follow existing payment_plans patterns for consistency
- Maintain backward compatibility with existing payment functionality
