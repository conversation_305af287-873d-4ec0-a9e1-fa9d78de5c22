-- Analytics Service Database Optimization
-- Create indexes for efficient analytics data retrieval

-- Indexes for analytics_events table
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_entity_id ON analytics_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_service_type ON analytics_events(service_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_analytics_events_status ON analytics_events(status);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_service_type ON analytics_events(user_id, service_type, event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_entity_created ON analytics_events(entity_id, created_at);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_created ON analytics_events(user_id, created_at);

-- Indexes for usage_events table (for dashboard analytics)
CREATE INDEX IF NOT EXISTS idx_usage_events_user_id ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_entity_type ON usage_events(entity_type);
CREATE INDEX IF NOT EXISTS idx_usage_events_entity_id ON usage_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_created_at ON usage_events(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_events_action ON usage_events(action);

-- Composite indexes for usage analytics
CREATE INDEX IF NOT EXISTS idx_usage_events_user_entity_type ON usage_events(user_id, entity_type);
CREATE INDEX IF NOT EXISTS idx_usage_events_user_created ON usage_events(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_events_entity_type_created ON usage_events(entity_type, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_events_user_entity_created ON usage_events(user_id, entity_type, created_at);

-- Indexes for service_metrics table
CREATE INDEX IF NOT EXISTS idx_service_metrics_service_type ON service_metrics(service_type);
CREATE INDEX IF NOT EXISTS idx_service_metrics_entity_id ON service_metrics(entity_id);
CREATE INDEX IF NOT EXISTS idx_service_metrics_usage_count ON service_metrics(usage_count);
CREATE INDEX IF NOT EXISTS idx_service_metrics_average_rating ON service_metrics(average_rating);

-- Composite index for service metrics lookup
CREATE INDEX IF NOT EXISTS idx_service_metrics_service_entity ON service_metrics(service_type, entity_id);

-- Indexes for user_activities table
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_last_activity ON user_activities(last_activity_date);
CREATE INDEX IF NOT EXISTS idx_user_activities_status ON user_activities(status);

-- Indexes for applications table
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_applications_environment ON applications(environment);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);

-- Composite index for application queries
CREATE INDEX IF NOT EXISTS idx_applications_user_status ON applications(user_id, status);

-- Indexes for api_keys table
CREATE INDEX IF NOT EXISTS idx_api_keys_application_id ON api_keys(application_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_public_key ON api_keys(public_key);
CREATE INDEX IF NOT EXISTS idx_api_keys_status ON api_keys(status);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_last_used_at ON api_keys(last_used_at);

-- Composite indexes for API key queries
CREATE INDEX IF NOT EXISTS idx_api_keys_app_user ON api_keys(application_id, user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_status ON api_keys(user_id, status);

-- Indexes for webhooks table
CREATE INDEX IF NOT EXISTS idx_webhooks_user_id ON webhooks(user_id);
CREATE INDEX IF NOT EXISTS idx_webhooks_application_id ON webhooks(application_id);
CREATE INDEX IF NOT EXISTS idx_webhooks_status ON webhooks(status);
CREATE INDEX IF NOT EXISTS idx_webhooks_is_active ON webhooks(is_active);
CREATE INDEX IF NOT EXISTS idx_webhooks_last_delivery_at ON webhooks(last_delivery_at);

-- Composite index for webhook queries
CREATE INDEX IF NOT EXISTS idx_webhooks_user_app ON webhooks(user_id, application_id);

-- Indexes for webhook_logs table
CREATE INDEX IF NOT EXISTS idx_webhook_logs_webhook_id ON webhook_logs(webhook_id);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_event_type ON webhook_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_success ON webhook_logs(success);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_delivered_at ON webhook_logs(delivered_at);

-- Composite index for webhook log queries
CREATE INDEX IF NOT EXISTS idx_webhook_logs_webhook_delivered ON webhook_logs(webhook_id, delivered_at);

-- Indexes for activation_events table
CREATE INDEX IF NOT EXISTS idx_activation_events_user_id ON activation_events(user_id);
CREATE INDEX IF NOT EXISTS idx_activation_events_event_type ON activation_events(event_type);
CREATE INDEX IF NOT EXISTS idx_activation_events_entity_id ON activation_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_activation_events_created_at ON activation_events(created_at);

-- Composite indexes for activation analytics
CREATE INDEX IF NOT EXISTS idx_activation_events_user_type ON activation_events(user_id, event_type);
CREATE INDEX IF NOT EXISTS idx_activation_events_user_created ON activation_events(user_id, created_at);

-- Partial indexes for active records only (to improve performance)
CREATE INDEX IF NOT EXISTS idx_analytics_events_active ON analytics_events(user_id, created_at)
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_applications_active ON applications(user_id, created_at)
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(application_id, user_id)
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_webhooks_active ON webhooks(user_id, application_id)
WHERE status = 'active' AND is_active = true;

-- Indexes for JSON metadata queries (PostgreSQL specific)
-- These help with queries on event_metadata fields
CREATE INDEX IF NOT EXISTS idx_analytics_events_metadata_gin ON analytics_events USING GIN (event_metadata);
CREATE INDEX IF NOT EXISTS idx_usage_events_metadata_gin ON usage_events USING GIN (event_metadata);
CREATE INDEX IF NOT EXISTS idx_activation_events_metadata_gin ON activation_events USING GIN (event_metadata);

-- Create materialized views for common analytics queries
-- This will significantly speed up dashboard queries

-- Daily usage summary materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_usage_summary AS
SELECT
    user_id,
    entity_type,
    DATE(created_at) as usage_date,
    COUNT(*) as request_count,
    SUM(credits_used) as total_credits,
    SUM(cost) as total_cost,
    COUNT(DISTINCT entity_id) as unique_entities
FROM usage_events
GROUP BY user_id, entity_type, DATE(created_at);

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_daily_usage_summary_user_date ON daily_usage_summary(user_id, usage_date);
CREATE INDEX IF NOT EXISTS idx_daily_usage_summary_entity_date ON daily_usage_summary(entity_type, usage_date);

-- Weekly usage summary materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS weekly_usage_summary AS
SELECT
    user_id,
    entity_type,
    DATE_TRUNC('week', created_at) as week_start,
    COUNT(*) as request_count,
    SUM(credits_used) as total_credits,
    SUM(cost) as total_cost,
    COUNT(DISTINCT entity_id) as unique_entities
FROM usage_events
GROUP BY user_id, entity_type, DATE_TRUNC('week', created_at);

-- Create index on weekly summary
CREATE INDEX IF NOT EXISTS idx_weekly_usage_summary_user_week ON weekly_usage_summary(user_id, week_start);

-- User activity summary materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS user_activity_summary AS
SELECT
    user_id,
    COUNT(DISTINCT CASE WHEN entity_type = 'agent' THEN entity_id END) as active_agents,
    COUNT(DISTINCT CASE WHEN entity_type = 'workflow' THEN entity_id END) as active_workflows,
    COUNT(DISTINCT CASE WHEN entity_type = 'mcp' THEN entity_id END) as active_mcps,
    SUM(CASE WHEN entity_type = 'agent' THEN credits_used ELSE 0 END) as agent_credits,
    SUM(CASE WHEN entity_type = 'workflow' THEN credits_used ELSE 0 END) as workflow_credits,
    SUM(CASE WHEN entity_type = 'mcp' THEN credits_used ELSE 0 END) as mcp_credits,
    SUM(CASE WHEN entity_type = 'app_credit' THEN credits_used ELSE 0 END) as app_credits,
    MAX(created_at) as last_activity
FROM usage_events
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY user_id;

-- Create index on user activity summary
CREATE INDEX IF NOT EXISTS idx_user_activity_summary_user ON user_activity_summary(user_id);

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_usage_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY weekly_usage_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_activity_summary;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to refresh views (requires pg_cron extension)
-- This should be run every hour to keep analytics data fresh
-- SELECT cron.schedule('refresh-analytics', '0 * * * *', 'SELECT refresh_analytics_views();');
