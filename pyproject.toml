[tool.poetry]
name = "analytics-service"
version = "0.1.0"
description = "Analytics Microservice"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"
psycopg2-binary = "^2.9.9"
grpcio = "^1.60.0"
grpcio-tools = "^1.60.0"
python-jose = { extras = ["cryptography"], version = "^3.4.0" }
passlib = { extras = ["bcrypt"], version = "^1.7.4" }
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"
structlog = "^24.1.0"
redis = "^5.0.1"
bcrypt = "4.0.1"
jsonschema = "^4.23.0"
google-cloud-storage = "^3.1.0"
kafka-python = "^2.0.6"
confluent-kafka = "^2.8.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "1.15.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 100
