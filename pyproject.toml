[tool.poetry]
name = "video-automation-backend"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
flask-cors = "^4.0.1"
flask-executor = "^1.0.0"
python-dotenv = "^1.0.1"
flask-restx = "^1.3.0"
flask = "^3.0.3"
pyautogen = "^0.2.28"
tavily-python = "^0.3.3"
langchain-openai = "^0.1.8"
python-docx = "^1.1.2"
beautifulsoup4 = "^4.12.3"
redis = "^5.0.5"
sentry-sdk = "^2.5.1"
pymongo = "^4.7.3"
pyjwt = "^2.8.0"
cryptography = "^42.0.8"
pypdf2 = "^3.0.1"
flask-dance = "^7.1.0"
google-api-python-client = "^2.133.0"
google-auth-oauthlib = "^1.2.0"
oauth2client = "^4.1.3"
pyht = "^0.0.28"
assemblyai = "^0.29.0"
moviepy = "^1.0.3"
pytube = "^15.0.0"
srt-equalizer = "^0.1.9"
google-generativeai = "^0.7.2"
boto3 = "^1.34.143"
g4f = "^*******"
curl-cffi = "^0.7.0"
opencv-python = "^*********"
ffmpeg-python = "^0.2.0"
scipy = "^1.14.0"
google-search-results = "^2.4.2"
yt-dlp = "^2024.7.9"
celery = "^5.4.0"
eventlet = "^0.36.1"
apscheduler = "^3.10.4"
fake-useragent = "^1.5.1"
schedule = "^1.2.2"
stripe = "^10.8.0"
openai = "^1.42.0"
anthropic = "^0.34.1"
langchain-community = "^0.2.15"
youtube-transcript-api = "^0.6.2"
langchain = "^0.2.15"
langchain-core = "^0.2.37"
langchain-text-splitters = "^0.2.2"
firebase-admin = "^6.5.0"
scikit-learn = "^1.5.2"
xgboost = "^2.1.1"
flask-limiter = "^3.8.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
