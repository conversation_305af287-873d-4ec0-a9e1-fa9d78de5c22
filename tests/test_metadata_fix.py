#!/usr/bin/env python3
"""
Test script to verify that the metadata column conflict has been resolved.
"""

def test_model_imports():
    """Test that all models can be imported without metadata conflicts"""
    try:
        from app.models.analytics import (
            Base,
            AnalyticsEvent,
            ServiceMetrics,
            UserActivity,
            Application,
            APIKey,
            Webhook,
            WebhookLog,
            ActivationEvent,
            UsageEvent,
        )
        print("✓ All models imported successfully")
        return True
    except Exception as e:
        print(f"✗ Model import failed: {e}")
        return False

def test_service_import():
    """Test that the analytics service can be imported"""
    try:
        from app.services.analytics_service import AnalyticsService
        print("✓ AnalyticsService imported successfully")
        return True
    except Exception as e:
        print(f"✗ Service import failed: {e}")
        return False

def test_model_creation():
    """Test that models can be instantiated"""
    try:
        from app.models.analytics import AnalyticsEvent, UsageEvent, ActivationEvent
        
        # Test AnalyticsEvent with event_metadata
        event = AnalyticsEvent(
            event_type="test",
            service_type="test",
            entity_id="test_entity",
            user_id="test_user",
            event_metadata={"test": "data"}
        )
        print("✓ AnalyticsEvent created with event_metadata")
        
        # Test UsageEvent with event_metadata
        usage = UsageEvent(
            user_id="test_user",
            entity_type="agent",
            entity_id="test_agent",
            action="invoke",
            credits_used=1.0,
            cost=0.1,
            event_metadata={"success": True}
        )
        print("✓ UsageEvent created with event_metadata")
        
        # Test ActivationEvent with event_metadata
        activation = ActivationEvent(
            user_id="test_user",
            event_type="user_signup",
            entity_id="test_user",
            event_metadata={"source": "web"}
        )
        print("✓ ActivationEvent created with event_metadata")
        
        return True
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing metadata column conflict resolution...")
    print("=" * 50)
    
    tests = [
        test_model_imports,
        test_service_import,
        test_model_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Metadata conflict resolved.")
        return True
    else:
        print("✗ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
