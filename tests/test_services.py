#!/usr/bin/env python3
"""
Simple test script to verify both Analytics and Application services are working correctly.
"""

import grpc
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.grpc import analytics_pb2, analytics_pb2_grpc


def test_analytics_service():
    """Test the Analytics Service"""
    print("Testing Analytics Service...")
    
    # Create a gRPC channel
    channel = grpc.insecure_channel('localhost:50059')
    
    # Create a stub for the Analytics Service
    analytics_stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)
    
    try:
        # Test TrackEvent
        request = analytics_pb2.TrackEventRequest(
            user_id="test_user_123",
            event_type=analytics_pb2.EventType.EVENT_TYPE_USER_ACTION,
            event_data='{"action": "test_action"}',
            metadata='{"source": "test"}'
        )
        
        response = analytics_stub.TrackEvent(request)
        print(f"✓ TrackEvent: {response.success} - {response.message}")
        
    except grpc.RpcError as e:
        print(f"✗ Analytics Service Error: {e.code()} - {e.details()}")
    
    channel.close()


def test_application_service():
    """Test the Application Service"""
    print("\nTesting Application Service...")
    
    # Create a gRPC channel
    channel = grpc.insecure_channel('localhost:50059')
    
    # Create a stub for the Application Service
    app_stub = analytics_pb2_grpc.ApplicationServiceStub(channel)
    
    try:
        # Test CreateApplication
        request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_123",
            name="Test Application",
            description="A test application for verification",
            workflow_ids=["workflow_1", "workflow_2"],
            agent_ids=["agent_1"]
        )
        
        response = app_stub.CreateApplication(request)
        print(f"✓ CreateApplication: {response.success} - {response.message}")
        
        if response.success:
            app_id = response.application.id
            print(f"  Created application with ID: {app_id}")
            
            # Test GetApplications
            get_request = analytics_pb2.GetApplicationsRequest(
                user_id="test_user_123",
                limit=10,
                offset=0
            )
            
            get_response = app_stub.GetApplications(get_request)
            print(f"✓ GetApplications: {get_response.success} - Found {get_response.total_count} applications")
            
            # Test GetApplication
            get_one_request = analytics_pb2.GetApplicationRequest(
                application_id=app_id,
                user_id="test_user_123"
            )
            
            get_one_response = app_stub.GetApplication(get_one_request)
            print(f"✓ GetApplication: {get_one_response.success} - {get_one_response.application.name}")
            
            # Test UpdateApplication
            update_request = analytics_pb2.UpdateApplicationRequest(
                application_id=app_id,
                user_id="test_user_123",
                name="Updated Test Application",
                description="Updated description"
            )
            
            update_response = app_stub.UpdateApplication(update_request)
            print(f"✓ UpdateApplication: {update_response.success} - {update_response.message}")
            
            # Test DeleteApplication
            delete_request = analytics_pb2.DeleteApplicationRequest(
                application_id=app_id,
                user_id="test_user_123"
            )
            
            delete_response = app_stub.DeleteApplication(delete_request)
            print(f"✓ DeleteApplication: {delete_response.success} - {delete_response.message}")
        
    except grpc.RpcError as e:
        print(f"✗ Application Service Error: {e.code()} - {e.details()}")
    
    channel.close()


def main():
    """Main test function"""
    print("🚀 Testing Analytics and Application Services")
    print("=" * 50)
    
    try:
        test_analytics_service()
        test_application_service()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
