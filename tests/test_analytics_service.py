import unittest
from unittest.mock import MagicMock, patch
import grpc
import json
from datetime import datetime, timezone
from app.grpc import analytics_pb2
from app.services.analytics_service import AnalyticsService
from app.models.analytics import AnalyticsEvent, ServiceMetrics, UserActivity


class TestAnalyticsService(unittest.TestCase):
    def setUp(self):
        self.service = AnalyticsService()
        self.context = MagicMock()
        
    @patch('app.services.analytics_service.SessionLocal')
    def test_track_event(self, mock_session):
        # Mock database session
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        
        # Create request
        request = analytics_pb2.TrackEventRequest(
            event_type=analytics_pb2.EventType.EVENT_TYPE_USAGE,
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="test-mcp-id",
            user_id="test-user-id",
            metadata=json.dumps({"test": "data"})
        )
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Call the service method
        response = self.service.TrackEvent(request, self.context)
        
        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Event tracked successfully")
        self.assertNotEqual(response.event_id, "")
        
        # Verify database interactions
        mock_db.add.assert_called()
        mock_db.commit.assert_called_once()
        
    @patch('app.services.analytics_service.SessionLocal')
    def test_get_service_metrics(self, mock_session):
        # Mock database session
        mock_db = MagicMock()
        mock_session.return_value = mock_db
        
        # Create request
        request = analytics_pb2.GetServiceMetricsRequest(
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="test-mcp-id",
            time_period_days=7
        )
        
        # Mock service metrics
        mock_metrics = MagicMock()
        mock_metrics.entity_id = "test-mcp-id"
        mock_metrics.service_type = "mcp"
        mock_metrics.usage_count = 10
        mock_metrics.average_rating = 4.5
        mock_metrics.rating_count = 5
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_metrics
        mock_db.query.return_value.filter.return_value.group_by.return_value.order_by.return_value.all.return_value = []
        
        # Call the service method
        response = self.service.GetServiceMetrics(request, self.context)
        
        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Service metrics retrieved successfully")
        self.assertEqual(response.metrics.entity_id, "test-mcp-id")
        self.assertEqual(response.metrics.usage_count, 10)
        self.assertEqual(response.metrics.average_rating, 4.5)
        self.assertEqual(response.metrics.rating_count, 5)


if __name__ == '__main__':
    unittest.main()
