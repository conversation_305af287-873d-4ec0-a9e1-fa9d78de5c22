"""
Tests for gRPC Dashboard Analytics Service
"""

import pytest
import grpc
import json
from datetime import datetime
from concurrent import futures
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.grpc.dashboard_analytics_service import EnhancedAnalyticsServicer
from app.utils.analytics_data_seeder import AnalyticsDataSeeder
from sqlalchemy.orm import Session


@pytest.fixture
def grpc_server():
    """Create a test gRPC server"""
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # Add the enhanced analytics servicer
    servicer = EnhancedAnalyticsServicer()
    analytics_pb2_grpc.add_AnalyticsServiceServicer_to_server(servicer, server)
    
    # Start server on a test port
    port = server.add_insecure_port('[::]:0')  # Let system choose port
    server.start()
    
    yield server, port
    
    server.stop(grace=1)


@pytest.fixture
def grpc_client(grpc_server):
    """Create a test gRPC client"""
    server, port = grpc_server
    channel = grpc.insecure_channel(f'localhost:{port}')
    stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)
    
    yield stub
    
    channel.close()


class TestEnhancedAnalyticsGRPC:
    """Test cases for Enhanced Analytics gRPC Service"""
    
    def test_get_dashboard_metrics(self, grpc_client, db_session: Session):
        """Test getting dashboard metrics via gRPC"""
        # Seed some test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["grpc_test_user"], run_aggregation=True)
        
        # Make gRPC request
        request = analytics_pb2.GetDashboardMetricsRequest(
            user_id="grpc_test_user",
            days=1
        )
        
        response = grpc_client.GetDashboardMetrics(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert response.metrics.active_agents >= 0
        assert response.metrics.credit_usage >= 0
        assert response.metrics.total_cost >= 0
    
    def test_get_credit_usage_breakdown(self, grpc_client, db_session: Session):
        """Test getting credit usage breakdown via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["grpc_test_user"], run_aggregation=True)
        
        # Make gRPC request
        request = analytics_pb2.GetCreditUsageBreakdownRequest(
            user_id="grpc_test_user",
            days=1
        )
        
        response = grpc_client.GetCreditUsageBreakdown(request)
        
        assert response.success is True
        assert "successfully" in response.message
        # Should have some breakdown items
        if response.breakdown:
            for item in response.breakdown:
                assert item.category in ["agents", "workflows", "custom_mcps", "app_credits", "other"]
                assert item.credits_used >= 0
                assert item.cost >= 0
                assert item.request_count >= 0
    
    def test_get_app_credit_usage(self, grpc_client, db_session: Session):
        """Test getting app credit usage via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_app_credit_usage(days=1, entries_per_day=4, user_ids=["grpc_test_user"])
        
        # Make gRPC request
        request = analytics_pb2.GetAppCreditUsageRequest(
            user_id="grpc_test_user",
            days=1
        )
        
        response = grpc_client.GetAppCreditUsage(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert response.data.total_credits >= 0
        assert response.data.total_cost >= 0
        assert len(response.data.timeseries) > 0
        
        # Check time series data structure
        for point in response.data.timeseries:
            assert point.timestamp  # Should have timestamp
            assert point.credits_used >= 0
            assert point.cost >= 0
            assert point.cumulative_credits >= 0
            assert point.cumulative_cost >= 0
    
    def test_get_latest_api_requests(self, grpc_client, db_session: Session):
        """Test getting latest API requests via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_api_request_events(days=1, events_per_day=10, user_ids=["grpc_test_user"])
        
        # Make gRPC request
        request = analytics_pb2.GetLatestApiRequestsRequest(
            user_id="grpc_test_user",
            limit=5
        )
        
        response = grpc_client.GetLatestApiRequests(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert len(response.events) <= 5
        
        # Check event data structure
        for event in response.events:
            assert event.id  # Should have ID
            assert event.type in ["api_request", "workflow_exec", "auth_event", "agent_invoke", "mcp_request"]
            assert event.status in ["success", "error", "pending", "timeout"]
            assert event.timestamp  # Should have timestamp
            assert event.credits_used >= 0
            assert event.cost >= 0
    
    def test_get_agent_performance(self, grpc_client, db_session: Session):
        """Test getting agent performance via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["grpc_test_user"], run_aggregation=True)
        
        # Make gRPC request
        request = analytics_pb2.GetAgentPerformanceRequest(
            user_id="grpc_test_user",
            days=1
        )
        
        response = grpc_client.GetAgentPerformance(request)
        
        assert response.success is True
        assert "successfully" in response.message
        
        # Check performance data if available
        for perf in response.performance:
            assert perf.agent_id  # Should have agent ID
            assert perf.total_requests >= 0
            assert perf.successful_requests >= 0
            assert perf.failed_requests >= 0
            assert 0 <= perf.success_rate <= 100
            assert perf.avg_response_time_ms >= 0
            assert perf.total_credits_used >= 0
            assert perf.total_cost >= 0
    
    def test_get_workflow_utilization(self, grpc_client, db_session: Session):
        """Test getting workflow utilization via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["grpc_test_user"], run_aggregation=True)
        
        # Make gRPC request
        request = analytics_pb2.GetWorkflowUtilizationRequest(
            user_id="grpc_test_user",
            days=1
        )
        
        response = grpc_client.GetWorkflowUtilization(request)
        
        assert response.success is True
        assert "successfully" in response.message
        
        # Check utilization data if available
        for util in response.utilization:
            assert util.workflow_id  # Should have workflow ID
            assert util.total_executions >= 0
            assert util.successful_executions >= 0
            assert util.failed_executions >= 0
            assert 0 <= util.success_rate <= 100
            assert util.avg_execution_time_ms >= 0
            assert 0 <= util.completion_rate_pct <= 100
            assert util.total_credits_used >= 0
            assert util.total_cost >= 0
    
    def test_get_system_activity(self, grpc_client, db_session: Session):
        """Test getting system activity via gRPC"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_system_activities(days=1, activities_per_day=5, user_ids=["grpc_test_user"])
        
        # Make gRPC request
        request = analytics_pb2.GetSystemActivityRequest(
            user_id="grpc_test_user",
            limit=3
        )
        
        response = grpc_client.GetSystemActivity(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert len(response.activities) <= 3
        
        # Check activity data structure
        for activity in response.activities:
            assert activity.id  # Should have ID
            assert activity.activity_type  # Should have type
            assert activity.title  # Should have title
            assert activity.timestamp  # Should have timestamp
            # Metadata should be valid JSON if present
            if activity.metadata:
                try:
                    json.loads(activity.metadata)
                except json.JSONDecodeError:
                    pytest.fail("Invalid JSON in metadata")
    
    def test_record_api_request(self, grpc_client, db_session: Session):
        """Test recording API request via gRPC"""
        request = analytics_pb2.RecordApiRequestRequest(
            request_type=analytics_pb2.REQUEST_TYPE_API_REQUEST,
            endpoint="/api/test/grpc",
            status=analytics_pb2.REQUEST_STATUS_SUCCESS,
            duration_ms=150,
            user_id="grpc_test_user",
            user_email="<EMAIL>",
            method="POST",
            credits_used=25.0,
            cost=0.25,
            request_data='{"test": "data"}',
            response_data='{"result": "success"}'
        )
        
        response = grpc_client.RecordApiRequest(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert response.event_id  # Should return event ID
    
    def test_record_system_activity(self, grpc_client, db_session: Session):
        """Test recording system activity via gRPC"""
        request = analytics_pb2.RecordSystemActivityRequest(
            activity_type="test",
            title="gRPC Test Activity",
            description="Test activity recorded via gRPC",
            severity="info",
            status="new",
            user_id="grpc_test_user",
            customer_id="customer_123",
            metadata='{"source": "grpc_test", "automated": true}'
        )
        
        response = grpc_client.RecordSystemActivity(request)
        
        assert response.success is True
        assert "successfully" in response.message
        assert response.activity_id  # Should return activity ID
    
    def test_error_handling(self, grpc_client):
        """Test error handling in gRPC service"""
        # Test with invalid user ID that might cause issues
        request = analytics_pb2.GetDashboardMetricsRequest(
            user_id="",  # Empty user ID
            days=0  # Invalid days
        )
        
        # Should handle gracefully and return default values
        response = grpc_client.GetDashboardMetrics(request)
        
        # Should still succeed with default values
        assert response.success is True
        assert response.metrics.active_agents >= 0
    
    def test_concurrent_requests(self, grpc_client, db_session: Session):
        """Test handling concurrent gRPC requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            request = analytics_pb2.GetDashboardMetricsRequest(
                user_id="concurrent_test_user",
                days=7
            )
            response = grpc_client.GetDashboardMetrics(request)
            results.append(response.success)
        
        # Create multiple threads to make concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(results) == 5
        assert all(results)
