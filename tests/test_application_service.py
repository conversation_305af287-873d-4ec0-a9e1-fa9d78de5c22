"""
Unit tests for Application Service APIs
"""

import pytest
import grpc
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import uuid

# Import the service and models
from app.services.application_service import ApplicationService
from app.models.analytics import Application, ApplicationImage
from app.grpc import analytics_pb2
from app.utils.constants.constants import ApplicationStatus


class TestApplicationService:
    """Test class for Application Service"""

    def setup_method(self):
        """Setup method called before each test"""
        self.service = ApplicationService()
        self.mock_db = Mock()
        self.mock_context = Mock()

        # Mock the get_db method
        self.service.get_db = Mock(return_value=self.mock_db)

    def test_create_application_success(self):
        """Test successful application creation"""
        # Arrange
        request = analytics_pb2.CreateApplicationRequest(
            user_id="user123",
            name="Test App",
            description="Test Description",
            workflow_ids=["workflow1", "workflow2"],
            agent_ids=["agent1"],
        )

        # Mock application object
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"
        mock_app.name = "Test App"
        mock_app.description = "Test Description"
        mock_app.workflow_ids = ["workflow1", "workflow2"]
        mock_app.agent_ids = ["agent1"]
        mock_app.status = ApplicationStatus.ACTIVE
        mock_app.created_at = datetime.now(timezone.utc)
        mock_app.updated_at = datetime.now(timezone.utc)
        mock_app.image_ids = []
        mock_app.api_keys = []
        mock_app.is_deleted = False

        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()

        # Mock Application constructor
        with patch("app.services.application_service.Application", return_value=mock_app):
            # Act
            response = self.service.CreateApplication(request, self.mock_context)

            # Assert
            assert response.success is True
            assert response.message == "Application created successfully"
            assert response.application.id == "app123"
            assert response.application.name == "Test App"
            assert response.application.user_id == "user123"
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()

    def test_create_application_failure(self):
        """Test application creation failure"""
        # Arrange
        request = analytics_pb2.CreateApplicationRequest(
            user_id="user123", name="Test App", description="Test Description"
        )

        # Mock database error
        self.mock_db.add.side_effect = Exception("Database error")
        self.mock_db.rollback = Mock()

        with patch("app.services.application_service.Application"):
            # Act
            response = self.service.CreateApplication(request, self.mock_context)

            # Assert
            assert response.success is False
            assert "Error creating application" in response.message
            self.mock_db.rollback.assert_called_once()
            self.mock_context.set_code.assert_called_with(grpc.StatusCode.INTERNAL)

    def test_get_applications_success(self):
        """Test successful retrieval of applications"""
        # Arrange
        request = analytics_pb2.GetApplicationsRequest(user_id="user123", limit=10, offset=0)

        # Mock applications
        mock_app1 = Mock()
        mock_app1.id = "app1"
        mock_app1.user_id = "user123"
        mock_app1.name = "App 1"
        mock_app1.description = "Description 1"
        mock_app1.workflow_ids = ["workflow1"]
        mock_app1.agent_ids = ["agent1"]
        mock_app1.status = ApplicationStatus.ACTIVE
        mock_app1.created_at = datetime.now(timezone.utc)
        mock_app1.updated_at = datetime.now(timezone.utc)
        mock_app1.image_ids = []
        mock_app1.api_keys = []
        mock_app1.is_deleted = False

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.limit.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [mock_app1]

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.GetApplications(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.message == "Applications retrieved successfully"
        assert response.total_count == 1
        assert len(response.applications) == 1
        assert response.applications[0].id == "app1"
        assert response.applications[0].name == "App 1"

    def test_get_applications_with_status_filter(self):
        """Test retrieval of applications with status filter"""
        # Arrange
        request = analytics_pb2.GetApplicationsRequest(
            user_id="user123",
            status=analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE,
            limit=10,
            offset=0,
        )

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 0
        mock_query.limit.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = []

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.GetApplications(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.total_count == 0
        assert len(response.applications) == 0
        # Verify that filter was called twice (user_id and status)
        assert mock_query.filter.call_count == 2

    def test_get_application_success(self):
        """Test successful retrieval of single application"""
        # Arrange
        request = analytics_pb2.GetApplicationRequest(application_id="app123", user_id="user123")

        # Mock application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"
        mock_app.name = "Test App"
        mock_app.description = "Test Description"
        mock_app.workflow_ids = ["workflow1"]
        mock_app.agent_ids = ["agent1"]
        mock_app.status = ApplicationStatus.ACTIVE
        mock_app.created_at = datetime.now(timezone.utc)
        mock_app.updated_at = datetime.now(timezone.utc)
        mock_app.image_ids = []
        mock_app.api_keys = []
        mock_app.is_deleted = False

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        # Mock image count query
        mock_image_query = Mock()
        mock_image_query.filter.return_value = mock_image_query
        mock_image_query.count.return_value = 2

        self.mock_db.query.side_effect = [mock_query, mock_image_query]

        # Act
        response = self.service.GetApplication(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.message == "Application retrieved successfully"
        assert response.application.id == "app123"
        assert response.application.name == "Test App"
        assert response.metrics.application_id == "app123"

    def test_get_application_not_found(self):
        """Test retrieval of non-existent application"""
        # Arrange
        request = analytics_pb2.GetApplicationRequest(
            application_id="nonexistent", user_id="user123"
        )

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.GetApplication(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Application not found" in response.message

    def test_update_application_success(self):
        """Test successful application update"""
        # Arrange
        request = analytics_pb2.UpdateApplicationRequest(
            application_id="app123",
            user_id="user123",
            name="Updated App",
            description="Updated Description",
            workflow_ids=["workflow1", "workflow2"],
            agent_ids=["agent1", "agent2"],
        )

        # Mock existing application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"
        mock_app.name = "Test App"
        mock_app.description = "Test Description"
        mock_app.workflow_ids = ["workflow1"]
        mock_app.agent_ids = ["agent1"]
        mock_app.status = ApplicationStatus.ACTIVE
        mock_app.created_at = datetime.now(timezone.utc)
        mock_app.updated_at = datetime.now(timezone.utc)
        mock_app.image_ids = []
        mock_app.api_keys = []
        mock_app.is_deleted = False

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        self.mock_db.query.return_value = mock_query
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()

        # Act
        response = self.service.UpdateApplication(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.message == "Application updated successfully"
        assert mock_app.name == "Updated App"
        assert mock_app.description == "Updated Description"
        assert mock_app.workflow_ids == ["workflow1", "workflow2"]
        assert mock_app.agent_ids == ["agent1", "agent2"]
        self.mock_db.commit.assert_called_once()

    def test_update_application_not_found(self):
        """Test update of non-existent application"""
        # Arrange
        request = analytics_pb2.UpdateApplicationRequest(
            application_id="nonexistent", user_id="user123", name="Updated App"
        )

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.UpdateApplication(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Application not found" in response.message

    def test_attach_image_to_application_success(self):
        """Test successful image attachment to application"""
        # Arrange
        request = analytics_pb2.AttachImageToApplicationRequest(
            application_id="app123",
            user_id="user123",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"fake_image_data",
            description="Test image",
        )

        # Mock existing application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"
        mock_app.image_ids = []
        mock_app.updated_at = datetime.now(timezone.utc)

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        # Mock new image
        mock_image = Mock()
        mock_image.id = "img123"
        mock_image.application_id = "app123"
        mock_image.user_id = "user123"
        mock_image.image_name = "test_image.jpg"
        mock_image.image_type = "image/jpeg"
        mock_image.image_size = len(request.image_data)
        mock_image.image_path = ""
        mock_image.description = "Test image"

        self.mock_db.query.return_value = mock_query
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()

        with patch("app.services.application_service.ApplicationImage", return_value=mock_image):
            # Act
            response = self.service.AttachImageToApplication(request, self.mock_context)

            # Assert
            assert response.success is True
            assert response.message == "Image attached successfully"
            assert response.image_id == "img123"
            assert "/api/images/img123" in response.image_url
            assert mock_app.image_ids == ["img123"]
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called()

    def test_attach_image_application_not_found(self):
        """Test image attachment to non-existent application"""
        # Arrange
        request = analytics_pb2.AttachImageToApplicationRequest(
            application_id="nonexistent",
            user_id="user123",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"fake_image_data",
        )

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.AttachImageToApplication(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Application not found" in response.message
        assert response.image_id == ""
        assert response.image_url == ""

    def test_attach_image_no_data(self):
        """Test image attachment without image data"""
        # Arrange
        request = analytics_pb2.AttachImageToApplicationRequest(
            application_id="app123",
            user_id="user123",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"",  # Empty image data
            description="Test image",
        )

        # Mock existing application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        self.mock_db.query.return_value = mock_query

        # Act
        response = self.service.AttachImageToApplication(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Image data is required" in response.message
        assert response.image_id == ""
        assert response.image_url == ""

    def test_attach_image_database_error(self):
        """Test image attachment with database error"""
        # Arrange
        request = analytics_pb2.AttachImageToApplicationRequest(
            application_id="app123",
            user_id="user123",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"fake_image_data",
        )

        # Mock existing application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        self.mock_db.query.return_value = mock_query
        self.mock_db.add.side_effect = Exception("Database error")
        self.mock_db.rollback = Mock()

        with patch("app.services.application_service.ApplicationImage"):
            # Act
            response = self.service.AttachImageToApplication(request, self.mock_context)

            # Assert
            assert response.success is False
            assert "Error attaching image" in response.message
            self.mock_db.rollback.assert_called_once()
            self.mock_context.set_code.assert_called_with(grpc.StatusCode.INTERNAL)

    def test_get_applications_database_error(self):
        """Test get applications with database error"""
        # Arrange
        request = analytics_pb2.GetApplicationsRequest(user_id="user123", limit=10, offset=0)

        # Mock database error
        self.mock_db.query.side_effect = Exception("Database connection error")

        # Act
        response = self.service.GetApplications(request, self.mock_context)

        # Assert
        assert response.success is False
        assert "Error getting applications" in response.message
        assert response.total_count == 0
        assert len(response.applications) == 0
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.INTERNAL)

    def test_update_application_with_status(self):
        """Test application update with status change"""
        # Arrange
        request = analytics_pb2.UpdateApplicationRequest(
            application_id="app123", user_id="user123", name="Updated App"
        )

        # Mock existing application
        mock_app = Mock()
        mock_app.id = "app123"
        mock_app.user_id = "user123"
        mock_app.name = "Test App"
        mock_app.description = "Test Description"
        mock_app.workflow_ids = []
        mock_app.agent_ids = []
        mock_app.status = ApplicationStatus.ACTIVE
        mock_app.image_ids = []

        # Mock datetime objects properly
        iso_time = "2023-01-01T00:00:00+00:00"
        mock_created_at = Mock()
        mock_created_at.isoformat.return_value = iso_time
        mock_updated_at = Mock()
        mock_updated_at.isoformat.return_value = iso_time

        mock_app.created_at = mock_created_at
        mock_app.updated_at = mock_updated_at

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_app

        self.mock_db.query.return_value = mock_query
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()

        # Act
        response = self.service.UpdateApplication(request, self.mock_context)

        # Assert
        assert response.success is True
        assert mock_app.name == "Updated App"
        self.mock_db.commit.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
