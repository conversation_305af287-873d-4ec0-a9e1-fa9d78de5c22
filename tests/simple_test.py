#!/usr/bin/env python3
"""
Simple test to verify metadata column fix without database dependencies.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic SQLAlchemy imports"""
    try:
        from sqlalchemy import Column, String, JSON
        from sqlalchemy.orm import declarative_base
        print("✓ SQLAlchemy imports successful")
        return True
    except Exception as e:
        print(f"✗ SQLAlchemy import failed: {e}")
        return False

def test_model_definitions():
    """Test that our models can be defined without metadata conflicts"""
    try:
        from sqlalchemy import Column, <PERSON>, JSO<PERSON>, Integer, Float
        from sqlalchemy.orm import declarative_base
        import uuid
        
        Base = declarative_base()
        
        class TestEvent(Base):
            __tablename__ = "test_events"
            
            id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
            user_id = Column(String, nullable=False)
            event_metadata = Column(JSON, nullable=True)  # This should work now
        
        class TestUsage(Base):
            __tablename__ = "test_usage"
            
            id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
            user_id = Column(String, nullable=False)
            credits_used = Column(Float, default=0.0)
            event_metadata = Column(JSON, nullable=True)  # This should work now
        
        print("✓ Test models defined successfully with event_metadata")
        return True
    except Exception as e:
        print(f"✗ Model definition failed: {e}")
        return False

def test_actual_models():
    """Test importing our actual models"""
    try:
        # Set environment variables to avoid config validation
        os.environ.setdefault('DB_HOST', 'localhost')
        os.environ.setdefault('DB_PORT', '5432')
        os.environ.setdefault('DB_USER', 'test')
        os.environ.setdefault('DB_PASSWORD', 'test')
        os.environ.setdefault('DB_NAME', 'test')
        os.environ.setdefault('REDIS_HOST', 'localhost')
        
        from app.models.analytics import AnalyticsEvent, UsageEvent, ActivationEvent
        print("✓ Actual models imported successfully")
        
        # Test that we can access the event_metadata attribute
        print(f"✓ AnalyticsEvent has event_metadata: {hasattr(AnalyticsEvent, 'event_metadata')}")
        print(f"✓ UsageEvent has event_metadata: {hasattr(UsageEvent, 'event_metadata')}")
        print(f"✓ ActivationEvent has event_metadata: {hasattr(ActivationEvent, 'event_metadata')}")
        
        return True
    except Exception as e:
        print(f"✗ Actual model import failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing metadata column conflict resolution...")
    print("=" * 60)
    
    tests = [
        ("Basic SQLAlchemy imports", test_basic_imports),
        ("Model definitions", test_model_definitions),
        ("Actual model imports", test_actual_models),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Metadata conflict has been resolved.")
        print("\nThe issue was that 'metadata' is a reserved attribute in SQLAlchemy.")
        print("We've renamed all 'metadata' columns to 'event_metadata' to fix this.")
        return True
    else:
        print("❌ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
