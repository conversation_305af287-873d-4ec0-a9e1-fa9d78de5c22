"""
Tests for Dashboard Analytics functionality
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models.dashboard_analytics import (
    RequestType, RequestStatus, CreditCategory
)
from app.services.dashboard_analytics_service import DashboardAnalyticsService
from app.services.analytics_aggregation_service import AnalyticsAggregationService
from app.utils.analytics_data_seeder import AnalyticsDataSeeder


class TestDashboardAnalyticsService:
    """Test cases for DashboardAnalyticsService"""
    
    def test_record_api_request(self, db_session: Session):
        """Test recording an API request event"""
        service = DashboardAnalyticsService(db_session)
        
        event = service.record_api_request(
            request_type=RequestType.AGENT_INVOKE,
            endpoint="/api/agents/invoke",
            status=RequestStatus.SUCCESS,
            duration_ms=320,
            user_id="test_user",
            user_email="<EMAIL>",
            method="POST",
            credits_used=50.0,
            cost=0.50
        )
        
        assert event.id is not None
        assert event.request_type == RequestType.AGENT_INVOKE
        assert event.endpoint == "/api/agents/invoke"
        assert event.status == RequestStatus.SUCCESS
        assert event.duration_ms == 320
        assert event.user_id == "test_user"
        assert event.credits_used == 50.0
        assert event.cost == 0.50
    
    def test_record_system_activity(self, db_session: Session):
        """Test recording a system activity"""
        service = DashboardAnalyticsService(db_session)
        
        activity = service.record_system_activity(
            activity_type="inquiry",
            title="New Customer Inquiry",
            description="Test inquiry description",
            severity="info",
            status="new",
            user_id="test_user",
            customer_id="customer_123"
        )
        
        assert activity.id is not None
        assert activity.activity_type == "inquiry"
        assert activity.title == "New Customer Inquiry"
        assert activity.severity == "info"
        assert activity.status == "new"
        assert activity.user_id == "test_user"
        assert activity.customer_id == "customer_123"
    
    def test_update_dashboard_metrics(self, db_session: Session):
        """Test updating dashboard metrics"""
        service = DashboardAnalyticsService(db_session)
        date = datetime.utcnow()
        
        metrics = service.update_dashboard_metrics(
            date=date,
            user_id="test_user",
            active_agents=45,
            credit_usage=1915.88,
            agent_requests=4283,
            workflow_requests=1259,
            custom_mcps=12,
            total_cost=1509.00
        )
        
        assert metrics.id is not None
        assert metrics.active_agents == 45
        assert metrics.credit_usage == 1915.88
        assert metrics.agent_requests == 4283
        assert metrics.workflow_requests == 1259
        assert metrics.custom_mcps == 12
        assert metrics.total_cost == 1509.00
    
    def test_record_credit_usage(self, db_session: Session):
        """Test recording credit usage breakdown"""
        service = DashboardAnalyticsService(db_session)
        date = datetime.utcnow()
        
        breakdown = service.record_credit_usage(
            date=date,
            category=CreditCategory.AGENTS,
            credits_used=100.0,
            cost=1.0,
            request_count=5,
            user_id="test_user"
        )
        
        assert breakdown.id is not None
        assert breakdown.category == CreditCategory.AGENTS
        assert breakdown.credits_used == 100.0
        assert breakdown.cost == 1.0
        assert breakdown.request_count == 5
        assert breakdown.user_id == "test_user"
    
    def test_record_app_credit_usage(self, db_session: Session):
        """Test recording app credit usage"""
        service = DashboardAnalyticsService(db_session)
        timestamp = datetime.utcnow()
        
        usage = service.record_app_credit_usage(
            timestamp=timestamp,
            credits_used=245.0,
            cost=24.50,
            application_id="app_1",
            application_name="Test App",
            user_id="test_user"
        )
        
        assert usage.id is not None
        assert usage.credits_used == 245.0
        assert usage.cost == 24.50
        assert usage.application_id == "app_1"
        assert usage.application_name == "Test App"
        assert usage.cumulative_credits == 245.0
        assert usage.cumulative_cost == 24.50


class TestAnalyticsAggregationService:
    """Test cases for AnalyticsAggregationService"""
    
    def test_aggregate_daily_metrics(self, db_session: Session):
        """Test daily metrics aggregation"""
        # First seed some test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_api_request_events(days=1, events_per_day=50, user_ids=["test_user"])
        
        # Then aggregate
        service = AnalyticsAggregationService(db_session)
        date = datetime.utcnow()
        
        metrics = service.aggregate_daily_metrics(date, user_id="test_user")
        
        assert metrics.id is not None
        assert metrics.user_id == "test_user"
        assert metrics.active_agents >= 0
        assert metrics.credit_usage >= 0
        assert metrics.agent_requests >= 0
        assert metrics.workflow_requests >= 0
        assert metrics.total_cost >= 0
    
    def test_aggregate_credit_usage_breakdown(self, db_session: Session):
        """Test credit usage breakdown aggregation"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_api_request_events(days=1, events_per_day=50, user_ids=["test_user"])
        
        # Aggregate
        service = AnalyticsAggregationService(db_session)
        date = datetime.utcnow()
        
        breakdowns = service.aggregate_credit_usage_breakdown(date, user_id="test_user")
        
        assert isinstance(breakdowns, list)
        # Should have at least some categories with usage
        assert len(breakdowns) > 0
        
        for breakdown in breakdowns:
            assert breakdown.category in list(CreditCategory)
            assert breakdown.credits_used >= 0
            assert breakdown.cost >= 0
            assert breakdown.request_count >= 0
    
    def test_run_daily_aggregation(self, db_session: Session):
        """Test complete daily aggregation"""
        # Seed test data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_api_request_events(days=1, events_per_day=30, user_ids=["test_user"])
        
        # Run aggregation
        service = AnalyticsAggregationService(db_session)
        date = datetime.utcnow()
        
        result = service.run_daily_aggregation(date, user_id="test_user")
        
        assert result["status"] == "success"
        assert result["user_id"] == "test_user"
        assert "dashboard_metrics_id" in result
        assert result["credit_breakdowns_count"] >= 0
        assert result["agent_metrics_count"] >= 0
        assert result["workflow_metrics_count"] >= 0


class TestAnalyticsDataSeeder:
    """Test cases for AnalyticsDataSeeder"""
    
    def test_seed_api_request_events(self, db_session: Session):
        """Test seeding API request events"""
        seeder = AnalyticsDataSeeder(db_session)
        
        event_ids = seeder.seed_api_request_events(
            days=2,
            events_per_day=10,
            user_ids=["test_user_1", "test_user_2"]
        )
        
        assert len(event_ids) == 20  # 2 days * 10 events per day
        
        # Verify events were created
        service = DashboardAnalyticsService(db_session)
        events = service.get_latest_api_requests(limit=25)
        
        assert len(events) == 20
        
        # Check that we have different request types
        request_types = {event["type"] for event in events}
        assert len(request_types) > 1
    
    def test_seed_system_activities(self, db_session: Session):
        """Test seeding system activities"""
        seeder = AnalyticsDataSeeder(db_session)
        
        activity_ids = seeder.seed_system_activities(
            days=2,
            activities_per_day=3,
            user_ids=["test_user_1", "test_user_2"]
        )
        
        assert len(activity_ids) == 6  # 2 days * 3 activities per day
        
        # Verify activities were created
        service = DashboardAnalyticsService(db_session)
        activities = service.get_system_activity(limit=10)
        
        assert len(activities) == 6
        
        # Check that we have different activity types
        activity_types = {activity["activity_type"] for activity in activities}
        assert len(activity_types) > 1
    
    def test_seed_app_credit_usage(self, db_session: Session):
        """Test seeding app credit usage"""
        seeder = AnalyticsDataSeeder(db_session)
        
        usage_ids = seeder.seed_app_credit_usage(
            days=1,
            entries_per_day=4,  # 4 entries per day
            user_ids=["test_user"]
        )
        
        # 1 day * 4 entries * 3 applications = 12 total entries
        assert len(usage_ids) == 12
        
        # Verify usage data was created
        service = DashboardAnalyticsService(db_session)
        usage_data = service.get_app_credit_usage_timeseries(
            user_id="test_user",
            days=1
        )
        
        assert usage_data["total_credits"] > 0
        assert usage_data["total_cost"] > 0
        assert len(usage_data["timeseries"]) == 12
    
    def test_run_full_seed(self, db_session: Session):
        """Test complete data seeding"""
        seeder = AnalyticsDataSeeder(db_session)
        
        summary = seeder.run_full_seed(
            days=1,
            user_ids=["test_user"],
            run_aggregation=True
        )
        
        assert summary["days"] == 1
        assert summary["api_events_count"] > 0
        assert summary["system_activities_count"] > 0
        assert summary["app_usage_count"] > 0
        assert summary["total_records"] > 0
        assert "aggregation_results" in summary
        
        # Verify aggregation ran successfully
        aggregation_results = summary["aggregation_results"]
        assert len(aggregation_results) > 0
        assert all(result["status"] == "success" for result in aggregation_results)


class TestDashboardAnalyticsQueries:
    """Test cases for dashboard analytics query methods"""
    
    def test_get_dashboard_overview(self, db_session: Session):
        """Test getting dashboard overview"""
        # Seed and aggregate data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["test_user"], run_aggregation=True)
        
        service = DashboardAnalyticsService(db_session)
        overview = service.get_dashboard_overview(user_id="test_user", days=1)
        
        assert "active_agents" in overview
        assert "credit_usage" in overview
        assert "agent_requests" in overview
        assert "workflow_requests" in overview
        assert "custom_mcps" in overview
        assert "total_cost" in overview
        
        # Values should be non-negative
        assert overview["active_agents"] >= 0
        assert overview["credit_usage"] >= 0
        assert overview["total_cost"] >= 0
    
    def test_get_credit_usage_breakdown(self, db_session: Session):
        """Test getting credit usage breakdown"""
        # Seed and aggregate data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.run_full_seed(days=1, user_ids=["test_user"], run_aggregation=True)
        
        service = DashboardAnalyticsService(db_session)
        breakdown = service.get_credit_usage_breakdown(user_id="test_user", days=1)
        
        assert isinstance(breakdown, list)
        
        if breakdown:  # If there's data
            for item in breakdown:
                assert "category" in item
                assert "credits_used" in item
                assert "cost" in item
                assert "request_count" in item
                assert item["credits_used"] >= 0
                assert item["cost"] >= 0
                assert item["request_count"] >= 0
    
    def test_get_latest_api_requests(self, db_session: Session):
        """Test getting latest API requests"""
        # Seed data
        seeder = AnalyticsDataSeeder(db_session)
        seeder.seed_api_request_events(days=1, events_per_day=20, user_ids=["test_user"])
        
        service = DashboardAnalyticsService(db_session)
        requests = service.get_latest_api_requests(user_id="test_user", limit=10)
        
        assert isinstance(requests, list)
        assert len(requests) <= 10
        
        if requests:  # If there's data
            for request in requests:
                assert "id" in request
                assert "type" in request
                assert "endpoint" in request
                assert "status" in request
                assert "timestamp" in request
                assert request["type"] in [rt.value for rt in RequestType]
                assert request["status"] in [rs.value for rs in RequestStatus]
