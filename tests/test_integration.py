"""
Integration tests for Application Service APIs
Tests the actual gRPC service with a real database connection
"""

import pytest
import grpc
import time
from concurrent import futures
import threading

from app.services.application_service import ApplicationService
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.db.session import SessionLocal
from app.models.analytics import Application, ApplicationImage


class TestApplicationServiceIntegration:
    """Integration test class for Application Service"""

    @classmethod
    def setup_class(cls):
        """Setup gRPC server for testing"""
        cls.server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        cls.application_service = ApplicationService()
        analytics_pb2_grpc.add_ApplicationServiceServicer_to_server(
            cls.application_service, cls.server
        )
        
        # Use a test port
        cls.port = 50060
        cls.server.add_insecure_port(f'[::]:{cls.port}')
        cls.server.start()
        
        # Give server time to start
        time.sleep(0.1)
        
        # Create gRPC channel and stub
        cls.channel = grpc.insecure_channel(f'localhost:{cls.port}')
        cls.stub = analytics_pb2_grpc.ApplicationServiceStub(cls.channel)

    @classmethod
    def teardown_class(cls):
        """Cleanup after tests"""
        cls.channel.close()
        cls.server.stop(0)

    def setup_method(self):
        """Setup method called before each test"""
        # Clean up any test data
        self.cleanup_test_data()

    def teardown_method(self):
        """Cleanup method called after each test"""
        self.cleanup_test_data()

    def cleanup_test_data(self):
        """Clean up test data from database"""
        db = SessionLocal()
        try:
            # Delete test applications and images
            db.query(ApplicationImage).filter(
                ApplicationImage.user_id == "test_user_integration"
            ).delete()
            db.query(Application).filter(
                Application.user_id == "test_user_integration"
            ).delete()
            db.commit()
        except Exception:
            db.rollback()
        finally:
            db.close()

    def test_create_application_integration(self):
        """Test creating an application through gRPC"""
        request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_integration",
            name="Integration Test App",
            description="Test application for integration testing",
            workflow_ids=["workflow1", "workflow2"],
            agent_ids=["agent1"]
        )
        
        response = self.stub.CreateApplication(request)
        
        assert response.success is True
        assert response.message == "Application created successfully"
        assert response.application.name == "Integration Test App"
        assert response.application.user_id == "test_user_integration"
        assert len(response.application.workflow_ids) == 2
        assert len(response.application.agent_ids) == 1
        
        # Store app_id for cleanup
        self.created_app_id = response.application.id

    def test_get_applications_integration(self):
        """Test retrieving applications through gRPC"""
        # First create an application
        create_request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_integration",
            name="Test App for Get",
            description="Test application"
        )
        
        create_response = self.stub.CreateApplication(create_request)
        assert create_response.success is True
        
        # Now get applications
        get_request = analytics_pb2.GetApplicationsRequest(
            user_id="test_user_integration",
            limit=10,
            offset=0
        )
        
        get_response = self.stub.GetApplications(get_request)
        
        assert get_response.success is True
        assert get_response.total_count >= 1
        assert len(get_response.applications) >= 1
        
        # Find our created application
        found_app = None
        for app in get_response.applications:
            if app.id == create_response.application.id:
                found_app = app
                break
        
        assert found_app is not None
        assert found_app.name == "Test App for Get"

    def test_update_application_integration(self):
        """Test updating an application through gRPC"""
        # First create an application
        create_request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_integration",
            name="App to Update",
            description="Original description"
        )
        
        create_response = self.stub.CreateApplication(create_request)
        assert create_response.success is True
        app_id = create_response.application.id
        
        # Now update the application
        update_request = analytics_pb2.UpdateApplicationRequest(
            application_id=app_id,
            user_id="test_user_integration",
            name="Updated App Name",
            description="Updated description",
            workflow_ids=["new_workflow"],
            agent_ids=["new_agent1", "new_agent2"]
        )
        
        update_response = self.stub.UpdateApplication(update_request)
        
        assert update_response.success is True
        assert update_response.message == "Application updated successfully"
        assert update_response.application.name == "Updated App Name"
        assert update_response.application.description == "Updated description"
        assert len(update_response.application.workflow_ids) == 1
        assert len(update_response.application.agent_ids) == 2

    def test_attach_image_integration(self):
        """Test attaching an image to an application through gRPC"""
        # First create an application
        create_request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_integration",
            name="App for Image",
            description="Test application for image attachment"
        )
        
        create_response = self.stub.CreateApplication(create_request)
        assert create_response.success is True
        app_id = create_response.application.id
        
        # Now attach an image
        image_request = analytics_pb2.AttachImageToApplicationRequest(
            application_id=app_id,
            user_id="test_user_integration",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"fake_image_data_for_testing",
            description="Test image attachment"
        )
        
        image_response = self.stub.AttachImageToApplication(image_request)
        
        assert image_response.success is True
        assert image_response.message == "Image attached successfully"
        assert image_response.image_id != ""
        assert "/api/images/" in image_response.image_url
        
        # Verify the application now has the image ID
        get_request = analytics_pb2.GetApplicationRequest(
            application_id=app_id,
            user_id="test_user_integration"
        )
        
        get_response = self.stub.GetApplication(get_request)
        assert get_response.success is True
        assert len(get_response.application.image_ids) == 1
        assert get_response.application.image_ids[0] == image_response.image_id

    def test_delete_application_integration(self):
        """Test deleting an application through gRPC"""
        # First create an application
        create_request = analytics_pb2.CreateApplicationRequest(
            user_id="test_user_integration",
            name="App to Delete",
            description="Test application for deletion"
        )
        
        create_response = self.stub.CreateApplication(create_request)
        assert create_response.success is True
        app_id = create_response.application.id
        
        # Now delete the application
        delete_request = analytics_pb2.DeleteApplicationRequest(
            application_id=app_id,
            user_id="test_user_integration"
        )
        
        delete_response = self.stub.DeleteApplication(delete_request)
        
        assert delete_response.success is True
        assert delete_response.message == "Application deleted successfully"
        
        # Verify the application is gone
        get_request = analytics_pb2.GetApplicationRequest(
            application_id=app_id,
            user_id="test_user_integration"
        )
        
        get_response = self.stub.GetApplication(get_request)
        assert get_response.success is False
        assert "Application not found" in get_response.message

    def test_application_not_found_integration(self):
        """Test handling of non-existent application"""
        get_request = analytics_pb2.GetApplicationRequest(
            application_id="nonexistent_app_id",
            user_id="test_user_integration"
        )
        
        get_response = self.stub.GetApplication(get_request)
        
        assert get_response.success is False
        assert "Application not found" in get_response.message


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
