# import schedule
# import time


# def job():
#     print("I'm working...")


# schedule.every(10).seconds.do(job)
# schedule.every(10).minutes.do(job)
# schedule.every().hour.do(job)
# schedule.every().day.at("10:30").do(job)
# schedule.every(5).to(10).minutes.do(job)
# schedule.every().monday.do(job)
# schedule.every().wednesday.at("13:15").do(job)
# schedule.every().day.at("12:42", "Europe/Amsterdam").do(job)
# schedule.every().minute.at(":17").do(job)


# def job_with_argument(name):
#     print(f"I am {name}")


# schedule.every(10).seconds.do(job_with_argument, name="<PERSON>")

# while True:
#     schedule.run_pending()
#     time.sleep(1)


import re


def get_first_sentence(paragraph):
    # Split the paragraph into sentences
    sentences = re.split(r"(?<=[.!?])\s+", paragraph)

    # Return the first sentence if there is one, otherwise return an empty string
    return sentences[0] if sentences else ""


# Example usage
paragraph = (
    "AI can help in predicting weather extremes, says WMO reportArtificial Intelligence (AI) and Machine Learning (ML) based weather forecasting can make predicting extreme weather possible, as opposed to the numerical weather prediction system presently in use, a multi-agency report coordinated by the World Meteorological Organisation has said.Some evaluations have shown that AI/ML models are surpassing physics-based models in predicting some weather variables and extreme or hazardous events, such as tropical cyclones, WMO said in the report titled “United in Science: Reboot Climate Action”, adding that studies have shown that AI/ML models can also predict the El Nino Southern Oscillation (ENSO) up to three years ahead.These recommendations come at a time when science is showing increased warming trends for the next five years which will disrupt weather patterns in several parts of the world including India.With existing policies and Nationally Determined Contributions, global temperature is expected to rise to a maximum of 3°C over pre-industrial levels. Only in the most optimistic scenario, when all conditional NDCs and net-zero pledges are fully achieved, global warming is projected to be limited to 2°C, with just a 14% chance of limitin"
)
first_sentence = get_first_sentence(paragraph)
print(first_sentence)
