"""
gRPC Service Implementation for Enhanced Dashboard Analytics
"""

import logging
from typing import Dict, Any, List
import json
from datetime import datetime

import grpc
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.services.dashboard_analytics_service import DashboardAnalyticsService
from app.models.dashboard_analytics import RequestType, RequestStatus, CreditCategory
from app.db.session import get_db

logger = logging.getLogger(__name__)


class EnhancedAnalyticsServicer(analytics_pb2_grpc.AnalyticsServiceServicer):
    """Enhanced Analytics gRPC Service Implementation"""

    def __init__(self):
        self.db = next(get_db())
        self.analytics_service = DashboardAnalyticsService(self.db)

    def GetDashboardMetrics(self, request, context):
        """Get dashboard metrics"""
        try:
            overview = self.analytics_service.get_dashboard_overview(
                user_id=request.user_id if request.user_id else None,
                days=request.days if request.days > 0 else 7,
            )

            metrics = analytics_pb2.DashboardMetrics(
                active_agents=overview["active_agents"],
                credit_usage=overview["credit_usage"],
                agent_requests=overview["agent_requests"],
                workflow_requests=overview["workflow_requests"],
                custom_mcps=overview["custom_mcps"],
                credit_usage_change=overview["credit_usage_change"],
                agent_requests_change_pct=overview["agent_requests_change_pct"],
                workflow_requests_change_pct=overview["workflow_requests_change_pct"],
                custom_mcps_change=overview["custom_mcps_change"],
                total_cost=overview["total_cost"],
            )

            return analytics_pb2.GetDashboardMetricsResponse(
                success=True, message="Dashboard metrics retrieved successfully", metrics=metrics
            )

        except Exception as e:
            logger.error(f"Error getting dashboard metrics: {e}")
            return analytics_pb2.GetDashboardMetricsResponse(
                success=False, message=f"Error retrieving dashboard metrics: {str(e)}"
            )

    def GetCreditUsageBreakdown(self, request, context):
        """Get credit usage breakdown"""
        try:
            breakdown_data = self.analytics_service.get_credit_usage_breakdown(
                user_id=request.user_id if request.user_id else None,
                days=request.days if request.days > 0 else 7,
            )

            breakdown_items = []
            for item in breakdown_data:
                breakdown_items.append(
                    analytics_pb2.CreditUsageBreakdownItem(
                        category=item["category"],
                        credits_used=item["credits_used"],
                        cost=item["cost"],
                        request_count=item["request_count"],
                    )
                )

            return analytics_pb2.GetCreditUsageBreakdownResponse(
                success=True,
                message="Credit usage breakdown retrieved successfully",
                breakdown=breakdown_items,
            )

        except Exception as e:
            logger.error(f"Error getting credit usage breakdown: {e}")
            return analytics_pb2.GetCreditUsageBreakdownResponse(
                success=False, message=f"Error retrieving credit usage breakdown: {str(e)}"
            )

    def GetAppCreditUsage(self, request, context):
        """Get app credit usage time series"""
        try:
            usage_data = self.analytics_service.get_app_credit_usage_timeseries(
                user_id=request.user_id if request.user_id else None,
                application_id=request.application_id if request.application_id else None,
                days=request.days if request.days > 0 else 7,
            )

            timeseries = []
            for point in usage_data["timeseries"]:
                timeseries.append(
                    analytics_pb2.AppCreditTimeSeriesPoint(
                        timestamp=point["timestamp"],
                        credits_used=point["credits_used"],
                        cost=point["cost"],
                        cumulative_credits=point["cumulative_credits"],
                        cumulative_cost=point["cumulative_cost"],
                    )
                )

            data = analytics_pb2.AppCreditUsageData(
                total_credits=usage_data["total_credits"],
                total_cost=usage_data["total_cost"],
                timeseries=timeseries,
            )

            return analytics_pb2.GetAppCreditUsageResponse(
                success=True, message="App credit usage retrieved successfully", data=data
            )

        except Exception as e:
            logger.error(f"Error getting app credit usage: {e}")
            return analytics_pb2.GetAppCreditUsageResponse(
                success=False, message=f"Error retrieving app credit usage: {str(e)}"
            )

    def GetLatestApiRequests(self, request, context):
        """Get latest API requests and events"""
        try:
            requests_data = self.analytics_service.get_latest_api_requests(
                user_id=request.user_id if request.user_id else None,
                limit=request.limit if request.limit > 0 else 50,
            )

            events = []
            for req in requests_data:
                events.append(
                    analytics_pb2.ApiRequestEventItem(
                        id=req["id"],
                        type=req["type"],
                        endpoint=req["endpoint"],
                        method=req.get("method", ""),
                        status=req["status"],
                        timestamp=req["timestamp"],
                        duration_ms=req.get("duration_ms", 0),
                        user_email=req.get("user_email", ""),
                        error_message=req.get("error_message", ""),
                        credits_used=req["credits_used"],
                        cost=req["cost"],
                    )
                )

            return analytics_pb2.GetLatestApiRequestsResponse(
                success=True, message="Latest API requests retrieved successfully", events=events
            )

        except Exception as e:
            logger.error(f"Error getting latest API requests: {e}")
            return analytics_pb2.GetLatestApiRequestsResponse(
                success=False, message=f"Error retrieving latest API requests: {str(e)}"
            )

    def GetAgentPerformance(self, request, context):
        """Get agent performance metrics"""
        try:
            performance_data = self.analytics_service.get_agent_performance_metrics(
                user_id=request.user_id if request.user_id else None,
                days=request.days if request.days > 0 else 7,
            )

            performance_items = []
            for item in performance_data:
                performance_items.append(
                    analytics_pb2.AgentPerformanceItem(
                        agent_id=item["agent_id"],
                        agent_name=item["agent_name"],
                        total_requests=item["total_requests"],
                        successful_requests=item["successful_requests"],
                        failed_requests=item["failed_requests"],
                        success_rate=item["success_rate"],
                        avg_response_time_ms=item["avg_response_time_ms"],
                        total_credits_used=item["total_credits_used"],
                        total_cost=item["total_cost"],
                        is_active=item["is_active"],
                    )
                )

            return analytics_pb2.GetAgentPerformanceResponse(
                success=True,
                message="Agent performance metrics retrieved successfully",
                performance=performance_items,
            )

        except Exception as e:
            logger.error(f"Error getting agent performance: {e}")
            return analytics_pb2.GetAgentPerformanceResponse(
                success=False, message=f"Error retrieving agent performance: {str(e)}"
            )

    def GetWorkflowUtilization(self, request, context):
        """Get workflow utilization metrics"""
        try:
            utilization_data = self.analytics_service.get_workflow_utilization_metrics(
                user_id=request.user_id if request.user_id else None,
                days=request.days if request.days > 0 else 7,
            )

            utilization_items = []
            for item in utilization_data:
                utilization_items.append(
                    analytics_pb2.WorkflowUtilizationItem(
                        workflow_id=item["workflow_id"],
                        workflow_name=item["workflow_name"],
                        total_executions=item["total_executions"],
                        successful_executions=item["successful_executions"],
                        failed_executions=item["failed_executions"],
                        success_rate=item["success_rate"],
                        avg_execution_time_ms=item["avg_execution_time_ms"],
                        completion_rate_pct=item["completion_rate_pct"],
                        total_credits_used=item["total_credits_used"],
                        total_cost=item["total_cost"],
                    )
                )

            return analytics_pb2.GetWorkflowUtilizationResponse(
                success=True,
                message="Workflow utilization metrics retrieved successfully",
                utilization=utilization_items,
            )

        except Exception as e:
            logger.error(f"Error getting workflow utilization: {e}")
            return analytics_pb2.GetWorkflowUtilizationResponse(
                success=False, message=f"Error retrieving workflow utilization: {str(e)}"
            )

    def GetSystemActivity(self, request, context):
        """Get system activity"""
        try:
            activity_data = self.analytics_service.get_system_activity(
                user_id=request.user_id if request.user_id else None,
                limit=request.limit if request.limit > 0 else 10,
            )

            activities = []
            for activity in activity_data:
                activities.append(
                    analytics_pb2.SystemActivityItem(
                        id=activity["id"],
                        activity_type=activity["activity_type"],
                        title=activity["title"],
                        description=activity["description"],
                        severity=activity["severity"],
                        status=activity["status"],
                        timestamp=activity["timestamp"],
                        user_id=activity["user_id"],
                        customer_id=activity["customer_id"],
                        metadata=json.dumps(activity["metadata"]) if activity["metadata"] else "",
                    )
                )

            return analytics_pb2.GetSystemActivityResponse(
                success=True,
                message="System activity retrieved successfully",
                activities=activities,
            )

        except Exception as e:
            logger.error(f"Error getting system activity: {e}")
            return analytics_pb2.GetSystemActivityResponse(
                success=False, message=f"Error retrieving system activity: {str(e)}"
            )

    def RecordApiRequest(self, request, context):
        """Record an API request event"""
        try:
            # Convert proto enums to Python enums
            request_type_map = {
                analytics_pb2.REQUEST_TYPE_API_REQUEST: RequestType.API_REQUEST,
                analytics_pb2.REQUEST_TYPE_WORKFLOW_EXEC: RequestType.WORKFLOW_EXEC,
                analytics_pb2.REQUEST_TYPE_AUTH_EVENT: RequestType.AUTH_EVENT,
                analytics_pb2.REQUEST_TYPE_AGENT_INVOKE: RequestType.AGENT_INVOKE,
                analytics_pb2.REQUEST_TYPE_MCP_REQUEST: RequestType.MCP_REQUEST,
            }

            status_map = {
                analytics_pb2.REQUEST_STATUS_SUCCESS: RequestStatus.SUCCESS,
                analytics_pb2.REQUEST_STATUS_ERROR: RequestStatus.ERROR,
                analytics_pb2.REQUEST_STATUS_PENDING: RequestStatus.PENDING,
                analytics_pb2.REQUEST_STATUS_TIMEOUT: RequestStatus.TIMEOUT,
            }

            request_type = request_type_map.get(request.request_type, RequestType.API_REQUEST)
            status = status_map.get(request.status, RequestStatus.SUCCESS)

            # Parse JSON data if provided
            request_data = None
            response_data = None
            if request.request_data:
                try:
                    request_data = json.loads(request.request_data)
                except json.JSONDecodeError:
                    request_data = {"raw": request.request_data}

            if request.response_data:
                try:
                    response_data = json.loads(request.response_data)
                except json.JSONDecodeError:
                    response_data = {"raw": request.response_data}

            event = self.analytics_service.record_api_request(
                request_type=request_type,
                endpoint=request.endpoint,
                status=status,
                duration_ms=request.duration_ms if request.duration_ms > 0 else None,
                user_id=request.user_id if request.user_id else None,
                user_email=request.user_email if request.user_email else None,
                method=request.method if request.method else None,
                ip_address=request.ip_address if request.ip_address else None,
                user_agent=request.user_agent if request.user_agent else None,
                request_data=request_data,
                response_data=response_data,
                error_message=request.error_message if request.error_message else None,
                agent_id=request.agent_id if request.agent_id else None,
                workflow_id=request.workflow_id if request.workflow_id else None,
                application_id=request.application_id if request.application_id else None,
                credits_used=request.credits_used,
                cost=request.cost,
            )

            return analytics_pb2.RecordApiRequestResponse(
                success=True, message="API request recorded successfully", event_id=str(event.id)
            )

        except Exception as e:
            logger.error(f"Error recording API request: {e}")
            return analytics_pb2.RecordApiRequestResponse(
                success=False, message=f"Error recording API request: {str(e)}"
            )

    def RecordSystemActivity(self, request, context):
        """Record system activity"""
        try:
            # Parse metadata if provided
            metadata = None
            if request.metadata:
                try:
                    metadata = json.loads(request.metadata)
                except json.JSONDecodeError:
                    metadata = {"raw": request.metadata}

            activity = self.analytics_service.record_system_activity(
                activity_type=request.activity_type,
                title=request.title,
                description=request.description if request.description else None,
                severity=request.severity if request.severity else None,
                status=request.status if request.status else None,
                user_id=request.user_id if request.user_id else None,
                customer_id=request.customer_id if request.customer_id else None,
                metadata=metadata,
            )

            return analytics_pb2.RecordSystemActivityResponse(
                success=True,
                message="System activity recorded successfully",
                activity_id=str(activity.id),
            )

        except Exception as e:
            logger.error(f"Error recording system activity: {e}")
            return analytics_pb2.RecordSystemActivityResponse(
                success=False, message=f"Error recording system activity: {str(e)}"
            )
