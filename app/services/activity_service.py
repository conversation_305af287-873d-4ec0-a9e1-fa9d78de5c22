import uuid
import structlog
from datetime import datetime, timezone
from typing import Optional, Any

from sqlalchemy.orm import Session
from sqlalchemy import select, and_, func

from app.models.analytics import (
    Activity,
    ActivityLog,
    ActivityEvent,
    ActivityType as SQLAlchemyActivityType,
    ActivityStatus as SQLAlchemyActivityStatus,
    LogType as SQLAlchemyLogType,
    LogStatus as SQLAlchemyLogStatus,
)
from app.db.session import SessionLocal

try:
    import grpc
    from app.grpc import analytics_pb2
    from app.grpc import analytics_pb2_grpc
    from google.protobuf.timestamp_pb2 import Timestamp
    from google.protobuf.struct_pb2 import Struct
    from google.protobuf import json_format
    GRPC_AVAILABLE = True
except ImportError:
    print("Warning: gRPC generated files not found. Service will not be fully functional.")
    GRPC_AVAILABLE = False

logger = structlog.get_logger(__name__)


def _datetime_to_timestamp(dt: datetime) -> Optional[Any]:
    """Convert datetime to protobuf Timestamp."""
    if dt and GRPC_AVAILABLE:
        ts = Timestamp()
        ts.FromDatetime(dt)
        return ts
    return None

def _timestamp_to_datetime(ts: Optional[Any]) -> Optional[datetime]:
    """Convert protobuf Timestamp to datetime."""
    if ts and hasattr(ts, 'ToDatetime'):
        dt_naive = ts.ToDatetime()
        return dt_naive.replace(tzinfo=timezone.utc) if dt_naive.tzinfo is None else dt_naive.astimezone(timezone.utc)
    return None

def _dict_to_struct(data: dict) -> Optional[Any]:
    """Convert dictionary to protobuf Struct."""
    if data and GRPC_AVAILABLE:
        s = Struct()
        json_format.ParseDict(data, s)
        return s
    return None

def _struct_to_dict(s: Optional[Any]) -> Optional[dict]:
    """Convert protobuf Struct to dictionary."""
    if s and hasattr(json_format, 'MessageToDict'):
        return json_format.MessageToDict(s)
    return None

# Mappings
PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE = {
    analytics_pb2.WORKFLOW: SQLAlchemyActivityType.WORKFLOW,
    analytics_pb2.AGENT: SQLAlchemyActivityType.AGENT,
}
SQLALCHEMY_TO_PROTO_ACTIVITY_TYPE = {v: k for k, v in PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE.items()}

PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS = {
    analytics_pb2.STARTED: SQLAlchemyActivityStatus.STARTED,
    analytics_pb2.IN_PROGRESS: SQLAlchemyActivityStatus.IN_PROGRESS,
    analytics_pb2.COMPLETED: SQLAlchemyActivityStatus.COMPLETED,
    analytics_pb2.FAILED: SQLAlchemyActivityStatus.FAILED,
}
SQLALCHEMY_TO_PROTO_ACTIVITY_STATUS = {v: k for k, v in PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS.items()}

PROTO_TO_SQLALCHEMY_LOG_TYPE = {
    analytics_pb2.TOOL_EXECUTION: SQLAlchemyLogType.TOOL_EXECUTION,
    analytics_pb2.NODE_EXECUTION: SQLAlchemyLogType.NODE_EXECUTION,
}
SQLALCHEMY_TO_PROTO_LOG_TYPE = {v: k for k, v in PROTO_TO_SQLALCHEMY_LOG_TYPE.items()}

PROTO_TO_SQLALCHEMY_LOG_STATUS = {
    analytics_pb2.SUCCESS: SQLAlchemyLogStatus.SUCCESS,
    analytics_pb2.FAILURE: SQLAlchemyLogStatus.FAILURE,
}
SQLALCHEMY_TO_PROTO_LOG_STATUS = {v: k for k, v in PROTO_TO_SQLALCHEMY_LOG_STATUS.items()}


class ActivityService(analytics_pb2_grpc.ActivityServiceServicer):
    def get_db(self) -> Session:
        return SessionLocal()

    def CreateActivity(self, request: analytics_pb2.CreateActivityRequest, context):
        db = self.get_db()
        try:
            # Validate activity type
            if request.type == analytics_pb2.ACTIVITY_TYPE_UNSPECIFIED:
                return analytics_pb2.CreateActivityResponse(
                    success=False,
                    message="Activity type must be specified",
                    activity=analytics_pb2.Activity()
                )
            
            if request.type not in PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE:
                return analytics_pb2.CreateActivityResponse(
                    success=False,
                    message=f"Invalid activity type: {request.type}",
                    activity=analytics_pb2.Activity()
                )
            
            # Validate activity status
            if request.status == analytics_pb2.ACTIVITY_STATUS_UNSPECIFIED:
                return analytics_pb2.CreateActivityResponse(
                    success=False,
                    message="Activity status must be specified",
                    activity=analytics_pb2.Activity()
                )
                
            if request.status not in PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS:
                return analytics_pb2.CreateActivityResponse(
                    success=False,
                    message=f"Invalid activity status: {request.status}",
                    activity=analytics_pb2.Activity()
                )

            now = datetime.now(timezone.utc)
            db_activity = Activity(
                id=str(uuid.uuid4()),
                resource_id=request.resource_id,
                type=PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE[request.type],
                user_id=request.user_id,
                status=PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS[request.status],
                activity_metadata=_struct_to_dict(request.user_metadata),
                created_at=now,
                updated_at=now,
            )
            db.add(db_activity)
            db.commit()
            db.refresh(db_activity)

            activity_proto = analytics_pb2.Activity(
                id=db_activity.id,
                resource_id=db_activity.resource_id,
                type=SQLALCHEMY_TO_PROTO_ACTIVITY_TYPE[db_activity.type],
                user_id=db_activity.user_id,
                status=SQLALCHEMY_TO_PROTO_ACTIVITY_STATUS[db_activity.status],
                user_metadata=_dict_to_struct(db_activity.activity_metadata),
                logs=[],  # Empty initially
                events=[],  # Empty initially
                created_at=_datetime_to_timestamp(db_activity.created_at),
                updated_at=_datetime_to_timestamp(db_activity.updated_at),
            )

            return analytics_pb2.CreateActivityResponse(
                success=True,
                message="Activity created successfully",
                activity=activity_proto
            )
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating activity: {str(e)}")
            return analytics_pb2.CreateActivityResponse(
                success=False,
                message=f"Internal error: {str(e)}",
                activity=analytics_pb2.Activity()
            )
        finally:
            db.close()

    def CreateActivityLog(self, request: analytics_pb2.CreateActivityLogRequest, context):
        db = self.get_db()
        try:
            # Validate log type
            if request.log_type == analytics_pb2.LOG_TYPE_UNSPECIFIED:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Log type must be specified")
                return analytics_pb2.ActivityLog()
            
            if request.log_type not in PROTO_TO_SQLALCHEMY_LOG_TYPE:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Invalid log type: {request.log_type}")
                return analytics_pb2.ActivityLog()
            
            # Validate log status
            if request.log_status == analytics_pb2.LOG_STATUS_UNSPECIFIED:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Log status must be specified")
                return analytics_pb2.ActivityLog()
                
            if request.log_status not in PROTO_TO_SQLALCHEMY_LOG_STATUS:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Invalid log status: {request.log_status}")
                return analytics_pb2.ActivityLog()

            db_log = ActivityLog(
                id=str(uuid.uuid4()),
                activity_id=request.activity_id,
                log_type=PROTO_TO_SQLALCHEMY_LOG_TYPE[request.log_type],
                log_status=PROTO_TO_SQLALCHEMY_LOG_STATUS[request.log_status],
                log_details=_struct_to_dict(request.log_details),
                created_at=datetime.now(timezone.utc),
            )
            db.add(db_log)
            db.commit()
            db.refresh(db_log)

            return analytics_pb2.ActivityLog(
                id=db_log.id,
                activity_id=db_log.activity_id,
                log_type=SQLALCHEMY_TO_PROTO_LOG_TYPE[db_log.log_type],
                log_status=SQLALCHEMY_TO_PROTO_LOG_STATUS[db_log.log_status],
                log_details=_dict_to_struct(db_log.log_details),
                created_at=_datetime_to_timestamp(db_log.created_at),
            )
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating activity log: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.ActivityLog()
        finally:
            db.close()

    def CreateActivityEvent(self, request: analytics_pb2.CreateActivityEventRequest, context):
        db = self.get_db()
        try:
            # Validate activity type
            if request.type == analytics_pb2.ACTIVITY_TYPE_UNSPECIFIED:
                return analytics_pb2.CreateActivityEventResponse(
                    success=False,
                    message="Activity type must be specified",
                    event=analytics_pb2.ActivityEvent()
                )
            
            if request.type not in PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE:
                return analytics_pb2.CreateActivityEventResponse(
                    success=False,
                    message=f"Invalid activity type: {request.type}",
                    event=analytics_pb2.ActivityEvent()
                )
            
            # Validate activity status
            if request.status == analytics_pb2.ACTIVITY_STATUS_UNSPECIFIED:
                return analytics_pb2.CreateActivityEventResponse(
                    success=False,
                    message="Activity status must be specified",
                    event=analytics_pb2.ActivityEvent()
                )
                
            if request.status not in PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS:
                return analytics_pb2.CreateActivityEventResponse(
                    success=False,
                    message=f"Invalid activity status: {request.status}",
                    event=analytics_pb2.ActivityEvent()
                )

            # First, create the activity
            now = datetime.now(timezone.utc)
            db_activity = Activity(
                id=str(uuid.uuid4()),
                resource_id=request.resource_id,
                type=PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE[request.type],
                user_id=request.user_id,
                status=PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS[request.status],
                activity_metadata=_struct_to_dict(request.user_metadata),
                created_at=now,
                updated_at=now,
            )
            db.add(db_activity)
            db.flush()  # Flush to get the ID

            # Then create the event using the generated activity ID
            db_event = ActivityEvent(
                id=str(uuid.uuid4()),
                activity_id=db_activity.id,
                event_name=request.event_name,
                event_details=_struct_to_dict(request.event_details),
                created_at=now,
            )
            db.add(db_event)
            db.commit()
            db.refresh(db_event)

            event_proto = analytics_pb2.ActivityEvent(
                id=db_event.id,
                activity_id=db_event.activity_id,
                event_name=db_event.event_name,
                event_details=_dict_to_struct(db_event.event_details),
                created_at=_datetime_to_timestamp(db_event.created_at),
            )

            return analytics_pb2.CreateActivityEventResponse(
                success=True,
                message="Activity and event created successfully",
                event=event_proto
            )
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating activity event: {str(e)}")
            return analytics_pb2.CreateActivityEventResponse(
                success=False,
                message=f"Internal error: {str(e)}",
                event=analytics_pb2.ActivityEvent()
            )
        finally:
            db.close()

    def GetActivity(self, request: analytics_pb2.GetActivityRequest, context):
        db = self.get_db()
        try:
            stmt = select(Activity).where(Activity.id == request.activity_id)
            db_activity = db.execute(stmt).scalar_one_or_none()

            if not db_activity:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Activity not found")
                return analytics_pb2.GetActivityResponse()

            log_stmt = select(ActivityLog).where(ActivityLog.activity_id == request.activity_id)
            db_logs = db.execute(log_stmt).scalars().all()

            event_stmt = select(ActivityEvent).where(ActivityEvent.activity_id == request.activity_id)
            db_events = db.execute(event_stmt).scalars().all()

            logs_proto = [
                analytics_pb2.ActivityLog(
                    id=log.id,
                    activity_id=log.activity_id,
                    log_type=SQLALCHEMY_TO_PROTO_LOG_TYPE[log.log_type],
                    log_status=SQLALCHEMY_TO_PROTO_LOG_STATUS[log.log_status],
                    log_details=_dict_to_struct(log.log_details),
                    created_at=_datetime_to_timestamp(log.created_at),
                ) for log in db_logs
            ]

            events_proto = [
                analytics_pb2.ActivityEvent(
                    id=event.id,
                    activity_id=event.activity_id,
                    event_name=event.event_name,
                    event_details=_dict_to_struct(event.event_details),
                    created_at=_datetime_to_timestamp(event.created_at),
                ) for event in db_events
            ]

            activity_proto = analytics_pb2.Activity(
                id=db_activity.id,
                resource_id=db_activity.resource_id,
                type=SQLALCHEMY_TO_PROTO_ACTIVITY_TYPE[db_activity.type],
                user_id=db_activity.user_id,
                status=SQLALCHEMY_TO_PROTO_ACTIVITY_STATUS[db_activity.status],
                user_metadata=_dict_to_struct(db_activity.activity_metadata),
                logs=logs_proto,
                events=events_proto,
                created_at=_datetime_to_timestamp(db_activity.created_at),
                updated_at=_datetime_to_timestamp(db_activity.updated_at),
            )

            return analytics_pb2.GetActivityResponse(
                activity=activity_proto,
                logs=[],  # Already included in activity_proto
                events=[],  # Already included in activity_proto
            )
        except Exception as e:
            logger.error(f"Error getting activity: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetActivityResponse()
        finally:
            db.close()

    def ListActivities(self, request: analytics_pb2.ListActivitiesRequest, context):
        db = self.get_db()
        try:
            # Build base query
            stmt = select(Activity)
            filters = []
            if request.user_id:
                filters.append(Activity.user_id == request.user_id)
            if request.type != analytics_pb2.ACTIVITY_TYPE_UNSPECIFIED:
                filters.append(Activity.type == PROTO_TO_SQLALCHEMY_ACTIVITY_TYPE[request.type])
            
            if filters:
                stmt = stmt.where(and_(*filters))

            # Get total count for pagination metadata
            count_stmt = select(func.count(Activity.id))
            if filters:
                count_stmt = count_stmt.where(and_(*filters))
            total_count = db.execute(count_stmt).scalar()

            # Pagination logic
            page_size = request.page_size if request.page_size > 0 else 10
            page_number = 1
            
            # Simple offset-based pagination instead of token-based
            offset = 0
            if request.page_token:
                try:
                    page_number = int(request.page_token)
                    offset = (page_number - 1) * page_size
                except ValueError:
                    page_number = 1
                    offset = 0

            stmt = stmt.order_by(Activity.created_at.desc()).offset(offset).limit(page_size)
            results = db.execute(stmt).scalars().all()

            # Get logs and events for each activity
            activities_proto = []
            for activity in results:
                # Get logs for this activity
                log_stmt = select(ActivityLog).where(ActivityLog.activity_id == activity.id)
                db_logs = db.execute(log_stmt).scalars().all()
                
                # Get events for this activity
                event_stmt = select(ActivityEvent).where(ActivityEvent.activity_id == activity.id)
                db_events = db.execute(event_stmt).scalars().all()

                logs_proto = [
                    analytics_pb2.ActivityLog(
                        id=log.id,
                        activity_id=log.activity_id,
                        log_type=SQLALCHEMY_TO_PROTO_LOG_TYPE[log.log_type],
                        log_status=SQLALCHEMY_TO_PROTO_LOG_STATUS[log.log_status],
                        log_details=_dict_to_struct(log.log_details),
                        created_at=_datetime_to_timestamp(log.created_at),
                    ) for log in db_logs
                ]

                events_proto = [
                    analytics_pb2.ActivityEvent(
                        id=event.id,
                        activity_id=event.activity_id,
                        event_name=event.event_name,
                        event_details=_dict_to_struct(event.event_details),
                        created_at=_datetime_to_timestamp(event.created_at),
                    ) for event in db_events
                ]

                activity_proto = analytics_pb2.Activity(
                    id=activity.id,
                    resource_id=activity.resource_id,
                    type=SQLALCHEMY_TO_PROTO_ACTIVITY_TYPE[activity.type],
                    user_id=activity.user_id,
                    status=SQLALCHEMY_TO_PROTO_ACTIVITY_STATUS[activity.status],
                    user_metadata=_dict_to_struct(activity.activity_metadata),
                    logs=logs_proto,
                    events=events_proto,
                    created_at=_datetime_to_timestamp(activity.created_at),
                    updated_at=_datetime_to_timestamp(activity.updated_at),
                )
                activities_proto.append(activity_proto)

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size
            has_next_page = page_number < total_pages
            has_previous_page = page_number > 1

            pagination_metadata = analytics_pb2.PaginationMetadata(
                total=total_count,
                total_pages=total_pages,
                current_page=page_number,
                page_size=page_size,
                has_next_page=has_next_page,
                has_previous_page=has_previous_page,
            )

            return analytics_pb2.ListActivitiesResponse(
                success=True,
                message="Activities retrieved successfully",
                activities=activities_proto,
                metadata=pagination_metadata,
            )
        except Exception as e:
            logger.error(f"Error listing activities: {str(e)}")
            return analytics_pb2.ListActivitiesResponse(
                success=False,
                message=f"Internal error: {str(e)}",
                activities=[],
                metadata=analytics_pb2.PaginationMetadata(),
            )
        finally:
            db.close()

    def ListActivityLogs(self, request: analytics_pb2.ListActivityLogsRequest, context):
        """List activity logs with pagination and filtering"""
        db = self.get_db()
        try:
            # Set default page size if not provided
            page_size = request.page_size if request.page_size > 0 else 10
            page_size = min(page_size, 100)  # Cap at 100
            
            # Calculate offset
            offset = request.offset if request.offset >= 0 else 0
            
            # Build query
            stmt = select(ActivityLog)
            filters = []
            
            # Apply filters
            if request.activity_id:
                filters.append(ActivityLog.activity_id == request.activity_id)
            
            if request.user_id:
                # Join with activities to filter by user_id
                stmt = stmt.join(Activity, ActivityLog.activity_id == Activity.id)
                filters.append(Activity.user_id == request.user_id)
            
            if request.log_type != analytics_pb2.LOG_TYPE_UNSPECIFIED:
                filters.append(ActivityLog.log_type == PROTO_TO_SQLALCHEMY_LOG_TYPE[request.log_type])
            
            if request.log_status != analytics_pb2.LOG_STATUS_UNSPECIFIED:
                filters.append(ActivityLog.log_status == PROTO_TO_SQLALCHEMY_LOG_STATUS[request.log_status])
            
            if filters:
                stmt = stmt.where(and_(*filters))
            
            # Order by created_at descending
            stmt = stmt.order_by(ActivityLog.created_at.desc())
            
            # Get total count for pagination
            count_stmt = select(func.count(ActivityLog.id))
            if request.user_id:
                count_stmt = count_stmt.select_from(ActivityLog).join(Activity, ActivityLog.activity_id == Activity.id)
            if filters:
                count_stmt = count_stmt.where(and_(*filters))
            total_count = db.execute(count_stmt).scalar()
            
            # Apply pagination and get results
            stmt = stmt.offset(offset).limit(page_size)
            logs = db.execute(stmt).scalars().all()
            
            # Convert to protobuf messages
            log_messages = []
            for log in logs:
                log_message = analytics_pb2.ActivityLog(
                    id=log.id,
                    activity_id=log.activity_id,
                    log_type=SQLALCHEMY_TO_PROTO_LOG_TYPE[log.log_type],
                    log_status=SQLALCHEMY_TO_PROTO_LOG_STATUS[log.log_status],
                    log_details=_dict_to_struct(log.log_details),
                    created_at=_datetime_to_timestamp(log.created_at),
                )
                log_messages.append(log_message)
            
            # Calculate pagination metadata
            current_page = (offset // page_size) + 1
            total_pages = (total_count + page_size - 1) // page_size
            has_next_page = offset + page_size < total_count
            has_previous_page = offset > 0
            
            pagination_metadata = analytics_pb2.PaginationMetadata(
                total=total_count,
                total_pages=total_pages,
                current_page=current_page,
                page_size=page_size,
                has_next_page=has_next_page,
                has_previous_page=has_previous_page
            )
            
            return analytics_pb2.ListActivityLogsResponse(
                success=True,
                message="Activity logs retrieved successfully",
                data=log_messages,
                metadata=pagination_metadata
            )
            
        except Exception as e:
            logger.error(f"Error listing activity logs: {str(e)}")
            return analytics_pb2.ListActivityLogsResponse(
                success=False,
                message=f"Failed to list activity logs: {str(e)}",
                data=[],
                metadata=analytics_pb2.PaginationMetadata()
            )
        finally:
            db.close()

    def ListActivityEvents(self, request: analytics_pb2.ListActivityEventsRequest, context):
        """List activity events with pagination and filtering"""
        db = self.get_db()
        try:
            # Set default page size if not provided
            page_size = request.page_size if request.page_size > 0 else 10
            page_size = min(page_size, 100)  # Cap at 100
            
            # Calculate offset
            offset = request.offset if request.offset >= 0 else 0
            
            # Build query
            stmt = select(ActivityEvent)
            filters = []
            
            # Apply filters
            if request.activity_id:
                filters.append(ActivityEvent.activity_id == request.activity_id)
            
            if request.user_id:
                # Join with activities to filter by user_id
                stmt = stmt.join(Activity, ActivityEvent.activity_id == Activity.id)
                filters.append(Activity.user_id == request.user_id)
            
            if request.event_name:
                filters.append(ActivityEvent.event_name == request.event_name)
            
            if filters:
                stmt = stmt.where(and_(*filters))
            
            # Order by created_at descending
            stmt = stmt.order_by(ActivityEvent.created_at.desc())
            
            # Get total count for pagination
            count_stmt = select(func.count(ActivityEvent.id))
            if request.user_id:
                count_stmt = count_stmt.select_from(ActivityEvent).join(Activity, ActivityEvent.activity_id == Activity.id)
            if filters:
                count_stmt = count_stmt.where(and_(*filters))
            total_count = db.execute(count_stmt).scalar()
            
            # Apply pagination and get results
            stmt = stmt.offset(offset).limit(page_size)
            events = db.execute(stmt).scalars().all()
            
            # Convert to protobuf messages
            event_messages = []
            for event in events:
                event_message = analytics_pb2.ActivityEvent(
                    id=event.id,
                    activity_id=event.activity_id,
                    event_name=event.event_name,
                    event_details=_dict_to_struct(event.event_details),
                    created_at=_datetime_to_timestamp(event.created_at),
                )
                event_messages.append(event_message)
            
            # Calculate pagination metadata
            current_page = (offset // page_size) + 1
            total_pages = (total_count + page_size - 1) // page_size
            has_next_page = offset + page_size < total_count
            has_previous_page = offset > 0
            
            pagination_metadata = analytics_pb2.PaginationMetadata(
                total=total_count,
                total_pages=total_pages,
                current_page=current_page,
                page_size=page_size,
                has_next_page=has_next_page,
                has_previous_page=has_previous_page
            )
            
            return analytics_pb2.ListActivityEventsResponse(
                success=True,
                message="Activity events retrieved successfully",
                data=event_messages,
                metadata=pagination_metadata
            )
            
        except Exception as e:
            logger.error(f"Error listing activity events: {str(e)}")
            return analytics_pb2.ListActivityEventsResponse(
                success=False,
                message=f"Failed to list activity events: {str(e)}",
                data=[],
                metadata=analytics_pb2.PaginationMetadata()
            )
        finally:
            db.close()

    def UpdateActivity(self, request: analytics_pb2.UpdateActivityRequest, context) -> analytics_pb2.UpdateActivityResponse:
        db = self.get_db()
        try:
            logger.info(
                "update_activity_request",
                activity_id=request.id,
                user_id=request.user_id,
            )

            activity = (
                db.query(Activity)
                .filter(Activity.id == request.id, Activity.user_id == request.user_id)
                .first()
            )

            if not activity:
                return analytics_pb2.UpdateActivityResponse(
                    success=False, message=f"Activity not found with ID {request.id}"
                )

            # Update fields if provided
            if request.status != analytics_pb2.ACTIVITY_STATUS_UNSPECIFIED:
                activity.status = PROTO_TO_SQLALCHEMY_ACTIVITY_STATUS[request.status]
            if request.HasField("user_metadata"):
                activity.activity_metadata = _struct_to_dict(request.user_metadata)

            activity.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(activity)

            return analytics_pb2.UpdateActivityResponse(
                success=True,
                message="Activity updated successfully",
                activity=analytics_pb2.Activity(
                    id=activity.id,
                    resource_id=activity.resource_id,
                    type=SQLALCHEMY_TO_PROTO_ACTIVITY_TYPE[activity.type],
                    user_id=activity.user_id,
                    status=SQLALCHEMY_TO_PROTO_ACTIVITY_STATUS[activity.status],
                    user_metadata=_dict_to_struct(activity.activity_metadata),
                    created_at=_datetime_to_timestamp(activity.created_at),
                    updated_at=_datetime_to_timestamp(activity.updated_at),
                ),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating activity: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.UpdateActivityResponse(
                success=False, message=f"Error updating activity: {str(e)}"
            )
        finally:
            db.close()

    def DeleteActivity(self, request: analytics_pb2.DeleteActivityRequest, context) -> analytics_pb2.DeleteActivityResponse:
        db = self.get_db()
        try:
            logger.info(
                "delete_activity_request",
                activity_id=request.id,
                user_id=request.user_id,
            )

            activity = (
                db.query(Activity)
                .filter(Activity.id == request.id, Activity.user_id == request.user_id)
                .first()
            )

            if not activity:
                return analytics_pb2.DeleteActivityResponse(
                    success=False, message=f"Activity not found with ID {request.id}"
                )

            # Delete associated logs and events first
            db.query(ActivityLog).filter(ActivityLog.activity_id == activity.id).delete()
            db.query(ActivityEvent).filter(ActivityEvent.activity_id == activity.id).delete()

            # Delete the activity
            db.delete(activity)
            db.commit()

            return analytics_pb2.DeleteActivityResponse(
                success=True, message="Activity and associated data deleted successfully"
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting activity: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.DeleteActivityResponse(
                success=False, message=f"Error deleting activity: {str(e)}"
            )
        finally:
            db.close()
