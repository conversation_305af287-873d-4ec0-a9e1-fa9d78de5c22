"""
Analytics Aggregation Service
Service for aggregating analytics data and calculating metrics
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from app.models.dashboard_analytics import (
    DashboardMetrics,
    CreditUsageBreakdown,
    ApiRequestEvent,
    AgentPerformanceMetrics,
    WorkflowUtilizationMetrics,
    AppCreditUsage,
    SystemActivity,
    RequestType,
    RequestStatus,
    CreditCategory,
)
from app.services.dashboard_analytics_service import DashboardAnalyticsService
import logging

logger = logging.getLogger(__name__)


class AnalyticsAggregationService:
    """Service for aggregating analytics data"""

    def __init__(self, db: Session):
        self.db = db
        self.analytics_service = DashboardAnalyticsService(db)

    def aggregate_daily_metrics(
        self, date: datetime, user_id: Optional[str] = None
    ) -> DashboardMetrics:
        """
        Aggregate daily metrics for dashboard overview

        Args:
            date: Date to aggregate metrics for
            user_id: Optional user ID for user-specific aggregation

        Returns:
            Aggregated DashboardMetrics instance
        """
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)

        # Base query filters
        base_filters = [
            ApiRequestEvent.timestamp >= start_date,
            ApiRequestEvent.timestamp < end_date,
        ]

        if user_id:
            base_filters.append(ApiRequestEvent.user_id == user_id)

        # Count active agents (agents that had requests)
        active_agents_query = self.db.query(
            func.count(func.distinct(ApiRequestEvent.agent_id))
        ).filter(and_(*base_filters), ApiRequestEvent.agent_id.isnot(None))
        active_agents = active_agents_query.scalar() or 0

        # Calculate credit usage and costs
        credit_query = self.db.query(
            func.coalesce(func.sum(ApiRequestEvent.credits_used), 0).label("total_credits"),
            func.coalesce(func.sum(ApiRequestEvent.cost), 0).label("total_cost"),
        ).filter(and_(*base_filters))

        credit_result = credit_query.first()
        credit_usage = float(credit_result.total_credits or 0)
        total_cost = float(credit_result.total_cost or 0)

        # Count agent requests
        agent_requests = (
            self.db.query(func.count(ApiRequestEvent.id))
            .filter(and_(*base_filters), ApiRequestEvent.request_type == RequestType.AGENT_INVOKE)
            .scalar()
            or 0
        )

        # Count workflow requests
        workflow_requests = (
            self.db.query(func.count(ApiRequestEvent.id))
            .filter(and_(*base_filters), ApiRequestEvent.request_type == RequestType.WORKFLOW_EXEC)
            .scalar()
            or 0
        )

        # Count custom MCPs (assuming MCP requests indicate custom MCPs)
        custom_mcps_query = self.db.query(
            func.count(func.distinct(ApiRequestEvent.application_id))
        ).filter(
            and_(*base_filters),
            ApiRequestEvent.request_type == RequestType.MCP_REQUEST,
            ApiRequestEvent.application_id.isnot(None),
        )
        custom_mcps = custom_mcps_query.scalar() or 0

        # Count app credits used
        app_credits_query = self.db.query(
            func.coalesce(func.sum(ApiRequestEvent.credits_used), 0).label("app_credits"),
            func.coalesce(func.sum(ApiRequestEvent.cost), 0).label("app_cost"),
        ).filter(and_(*base_filters), ApiRequestEvent.application_id.isnot(None))

        app_result = app_credits_query.first()
        app_credits_used = int(app_result.app_credits or 0)
        app_credits_cost = float(app_result.app_cost or 0)

        # Calculate growth percentages (compare with previous period)
        prev_date = date - timedelta(days=1)
        prev_metrics = self.db.query(DashboardMetrics).filter(
            DashboardMetrics.date == prev_date.date()
        )

        if user_id:
            prev_metrics = prev_metrics.filter(DashboardMetrics.user_id == user_id)

        prev_metrics = prev_metrics.first()

        # Calculate changes
        credit_usage_change = 0.0
        agent_requests_change_pct = 0.0
        workflow_requests_change_pct = 0.0
        custom_mcps_change = 0

        if prev_metrics:
            credit_usage_change = credit_usage - prev_metrics.credit_usage

            if prev_metrics.agent_requests > 0:
                agent_requests_change_pct = (
                    (agent_requests - prev_metrics.agent_requests)
                    / prev_metrics.agent_requests
                    * 100
                )

            if prev_metrics.workflow_requests > 0:
                workflow_requests_change_pct = (
                    (workflow_requests - prev_metrics.workflow_requests)
                    / prev_metrics.workflow_requests
                    * 100
                )

            custom_mcps_change = custom_mcps - prev_metrics.custom_mcps

        # Update or create dashboard metrics
        metrics = self.analytics_service.update_dashboard_metrics(
            date=date,
            user_id=user_id,
            active_agents=active_agents,
            credit_usage=credit_usage,
            agent_requests=agent_requests,
            workflow_requests=workflow_requests,
            custom_mcps=custom_mcps,
            total_cost=total_cost,
            app_credits_used=app_credits_used,
            app_credits_cost=app_credits_cost,
        )

        # Update change metrics separately
        metrics.credit_usage_change = credit_usage_change
        metrics.agent_requests_change_pct = agent_requests_change_pct
        metrics.workflow_requests_change_pct = workflow_requests_change_pct
        metrics.custom_mcps_change = custom_mcps_change

        self.db.commit()
        self.db.refresh(metrics)

        return metrics

    def aggregate_credit_usage_breakdown(
        self, date: datetime, user_id: Optional[str] = None
    ) -> List[CreditUsageBreakdown]:
        """
        Aggregate credit usage breakdown by category for a specific date

        Args:
            date: Date to aggregate for
            user_id: Optional user ID for user-specific aggregation

        Returns:
            List of CreditUsageBreakdown instances
        """
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)

        base_filters = [
            ApiRequestEvent.timestamp >= start_date,
            ApiRequestEvent.timestamp < end_date,
        ]

        if user_id:
            base_filters.append(ApiRequestEvent.user_id == user_id)

        # Define category mappings
        category_mappings = {
            CreditCategory.AGENTS: [RequestType.AGENT_INVOKE],
            CreditCategory.WORKFLOWS: [RequestType.WORKFLOW_EXEC],
            CreditCategory.CUSTOM_MCPS: [RequestType.MCP_REQUEST],
            CreditCategory.APP_CREDITS: [],  # Will be handled separately
            CreditCategory.OTHER: [RequestType.API_REQUEST, RequestType.AUTH_EVENT],
        }

        breakdowns = []

        for category, request_types in category_mappings.items():
            if category == CreditCategory.APP_CREDITS:
                # Special handling for app credits
                query = self.db.query(
                    func.coalesce(func.sum(ApiRequestEvent.credits_used), 0).label("credits"),
                    func.coalesce(func.sum(ApiRequestEvent.cost), 0).label("cost"),
                    func.count(ApiRequestEvent.id).label("count"),
                ).filter(and_(*base_filters), ApiRequestEvent.application_id.isnot(None))
            else:
                # Regular category handling
                query = self.db.query(
                    func.coalesce(func.sum(ApiRequestEvent.credits_used), 0).label("credits"),
                    func.coalesce(func.sum(ApiRequestEvent.cost), 0).label("cost"),
                    func.count(ApiRequestEvent.id).label("count"),
                ).filter(and_(*base_filters), ApiRequestEvent.request_type.in_(request_types))

            result = query.first()

            if result and (result.credits > 0 or result.cost > 0 or result.count > 0):
                breakdown = self.analytics_service.record_credit_usage(
                    date=date,
                    category=category,
                    credits_used=float(result.credits or 0),
                    cost=float(result.cost or 0),
                    request_count=int(result.count or 0),
                    user_id=user_id,
                )
                breakdowns.append(breakdown)

        return breakdowns

    def aggregate_agent_performance(
        self, date: datetime, user_id: Optional[str] = None
    ) -> List[AgentPerformanceMetrics]:
        """
        Aggregate agent performance metrics for a specific date

        Args:
            date: Date to aggregate for
            user_id: Optional user ID for user-specific aggregation

        Returns:
            List of AgentPerformanceMetrics instances
        """
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)

        base_filters = [
            ApiRequestEvent.timestamp >= start_date,
            ApiRequestEvent.timestamp < end_date,
            ApiRequestEvent.request_type == RequestType.AGENT_INVOKE,
            ApiRequestEvent.agent_id.isnot(None),
        ]

        if user_id:
            base_filters.append(ApiRequestEvent.user_id == user_id)

        # Aggregate by agent
        query = (
            self.db.query(
                ApiRequestEvent.agent_id,
                func.count(ApiRequestEvent.id).label("total_requests"),
                func.sum(
                    func.case([(ApiRequestEvent.status == RequestStatus.SUCCESS, 1)], else_=0)
                ).label("successful_requests"),
                func.sum(
                    func.case([(ApiRequestEvent.status == RequestStatus.ERROR, 1)], else_=0)
                ).label("failed_requests"),
                func.avg(ApiRequestEvent.duration_ms).label("avg_response_time"),
                func.sum(ApiRequestEvent.credits_used).label("total_credits"),
                func.sum(ApiRequestEvent.cost).label("total_cost"),
                ApiRequestEvent.user_id,
            )
            .filter(and_(*base_filters))
            .group_by(ApiRequestEvent.agent_id, ApiRequestEvent.user_id)
        )

        results = query.all()
        metrics = []

        for result in results:
            # Create or update agent performance metrics
            existing = (
                self.db.query(AgentPerformanceMetrics)
                .filter(
                    and_(
                        AgentPerformanceMetrics.date == date.date(),
                        AgentPerformanceMetrics.agent_id == result.agent_id,
                        AgentPerformanceMetrics.user_id == result.user_id,
                    )
                )
                .first()
            )

            if existing:
                # Update existing metrics
                existing.total_requests = int(result.total_requests or 0)
                existing.successful_requests = int(result.successful_requests or 0)
                existing.failed_requests = int(result.failed_requests or 0)
                existing.avg_response_time_ms = float(result.avg_response_time or 0)
                existing.total_credits_used = float(result.total_credits or 0)
                existing.total_cost = float(result.total_cost or 0)
                existing.is_active = True
                metric = existing
            else:
                # Create new metrics
                metric = AgentPerformanceMetrics(
                    date=date.date(),
                    agent_id=result.agent_id,
                    user_id=result.user_id,
                    total_requests=int(result.total_requests or 0),
                    successful_requests=int(result.successful_requests or 0),
                    failed_requests=int(result.failed_requests or 0),
                    avg_response_time_ms=float(result.avg_response_time or 0),
                    total_credits_used=float(result.total_credits or 0),
                    total_cost=float(result.total_cost or 0),
                    is_active=True,
                )
                self.db.add(metric)

            metrics.append(metric)

        self.db.commit()
        return metrics

    def aggregate_workflow_utilization(
        self, date: datetime, user_id: Optional[str] = None
    ) -> List[WorkflowUtilizationMetrics]:
        """
        Aggregate workflow utilization metrics for a specific date

        Args:
            date: Date to aggregate for
            user_id: Optional user ID for user-specific aggregation

        Returns:
            List of WorkflowUtilizationMetrics instances
        """
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)

        base_filters = [
            ApiRequestEvent.timestamp >= start_date,
            ApiRequestEvent.timestamp < end_date,
            ApiRequestEvent.request_type == RequestType.WORKFLOW_EXEC,
            ApiRequestEvent.workflow_id.isnot(None),
        ]

        if user_id:
            base_filters.append(ApiRequestEvent.user_id == user_id)

        # Aggregate by workflow
        query = (
            self.db.query(
                ApiRequestEvent.workflow_id,
                func.count(ApiRequestEvent.id).label("total_executions"),
                func.sum(
                    func.case([(ApiRequestEvent.status == RequestStatus.SUCCESS, 1)], else_=0)
                ).label("successful_executions"),
                func.sum(
                    func.case([(ApiRequestEvent.status == RequestStatus.ERROR, 1)], else_=0)
                ).label("failed_executions"),
                func.avg(ApiRequestEvent.duration_ms).label("avg_execution_time"),
                func.sum(ApiRequestEvent.credits_used).label("total_credits"),
                func.sum(ApiRequestEvent.cost).label("total_cost"),
                ApiRequestEvent.user_id,
            )
            .filter(and_(*base_filters))
            .group_by(ApiRequestEvent.workflow_id, ApiRequestEvent.user_id)
        )

        results = query.all()
        metrics = []

        for result in results:
            # Calculate completion rate
            total_execs = int(result.total_executions or 0)
            successful_execs = int(result.successful_executions or 0)
            completion_rate = (successful_execs / total_execs * 100) if total_execs > 0 else 0

            # Create or update workflow utilization metrics
            existing = (
                self.db.query(WorkflowUtilizationMetrics)
                .filter(
                    and_(
                        WorkflowUtilizationMetrics.date == date.date(),
                        WorkflowUtilizationMetrics.workflow_id == result.workflow_id,
                        WorkflowUtilizationMetrics.user_id == result.user_id,
                    )
                )
                .first()
            )

            if existing:
                # Update existing metrics
                existing.total_executions = total_execs
                existing.successful_executions = successful_execs
                existing.failed_executions = int(result.failed_executions or 0)
                existing.avg_execution_time_ms = float(result.avg_execution_time or 0)
                existing.completion_rate_pct = completion_rate
                existing.total_credits_used = float(result.total_credits or 0)
                existing.total_cost = float(result.total_cost or 0)
                metric = existing
            else:
                # Create new metrics
                metric = WorkflowUtilizationMetrics(
                    date=date.date(),
                    workflow_id=result.workflow_id,
                    user_id=result.user_id,
                    total_executions=total_execs,
                    successful_executions=successful_execs,
                    failed_executions=int(result.failed_executions or 0),
                    avg_execution_time_ms=float(result.avg_execution_time or 0),
                    completion_rate_pct=completion_rate,
                    total_credits_used=float(result.total_credits or 0),
                    total_cost=float(result.total_cost or 0),
                )
                self.db.add(metric)

            metrics.append(metric)

        self.db.commit()
        return metrics

    def run_daily_aggregation(
        self, date: Optional[datetime] = None, user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run complete daily aggregation for all metrics

        Args:
            date: Date to aggregate for (defaults to yesterday)
            user_id: Optional user ID for user-specific aggregation

        Returns:
            Summary of aggregation results
        """
        if date is None:
            date = datetime.utcnow() - timedelta(days=1)

        try:
            logger.info(f"Starting daily aggregation for {date.date()}")

            # Aggregate dashboard metrics
            dashboard_metrics = self.aggregate_daily_metrics(date, user_id)
            logger.info(f"Aggregated dashboard metrics: {dashboard_metrics.id}")

            # Aggregate credit usage breakdown
            credit_breakdowns = self.aggregate_credit_usage_breakdown(date, user_id)
            logger.info(f"Aggregated {len(credit_breakdowns)} credit usage breakdowns")

            # Aggregate agent performance
            agent_metrics = self.aggregate_agent_performance(date, user_id)
            logger.info(f"Aggregated {len(agent_metrics)} agent performance metrics")

            # Aggregate workflow utilization
            workflow_metrics = self.aggregate_workflow_utilization(date, user_id)
            logger.info(f"Aggregated {len(workflow_metrics)} workflow utilization metrics")

            return {
                "date": date.date().isoformat(),
                "user_id": user_id,
                "dashboard_metrics_id": str(dashboard_metrics.id),
                "credit_breakdowns_count": len(credit_breakdowns),
                "agent_metrics_count": len(agent_metrics),
                "workflow_metrics_count": len(workflow_metrics),
                "status": "success",
            }

        except Exception as e:
            logger.error(f"Error during daily aggregation: {str(e)}")
            self.db.rollback()
            return {
                "date": date.date().isoformat() if date else None,
                "user_id": user_id,
                "status": "error",
                "error": str(e),
            }

    def backfill_aggregations(
        self, start_date: datetime, end_date: datetime, user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Backfill aggregations for a date range

        Args:
            start_date: Start date for backfill
            end_date: End date for backfill
            user_id: Optional user ID for user-specific aggregation

        Returns:
            List of aggregation results for each date
        """
        results = []
        current_date = start_date

        while current_date <= end_date:
            result = self.run_daily_aggregation(current_date, user_id)
            results.append(result)
            current_date += timedelta(days=1)

        return results
