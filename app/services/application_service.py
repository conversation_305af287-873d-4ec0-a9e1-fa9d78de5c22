import grpc
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import desc
from app.db.session import SessionLocal
from app.models.analytics import Application, ApplicationImage
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.utils.constants.constants import ApplicationStatus

logger = structlog.get_logger(__name__)


class ApplicationService(analytics_pb2_grpc.ApplicationServiceServicer):
    """gRPC service for Application management functionality."""

    def get_db(self) -> Session:
        """Get database session."""
        return SessionLocal()

    def CreateApplication(
        self, request: analytics_pb2.CreateApplicationRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.CreateApplicationResponse:
        db = self.get_db()
        try:
            logger.info(
                "create_application_request",
                user_id=request.user_id,
                name=request.name,
                description=request.description,
            )

            # Create new application
            new_application = Application(
                user_id=request.user_id,
                name=request.name,
                description=request.description,
                workflow_ids=list(request.workflow_ids) if request.workflow_ids else [],
                agent_ids=list(request.agent_ids) if request.agent_ids else [],
                api_keys=list(request.api_keys) if request.api_keys else [],
                status=ApplicationStatus.ACTIVE,
                is_deleted=False,
            )
            db.add(new_application)
            db.commit()
            db.refresh(new_application)

            # Convert to protobuf message
            app_pb = analytics_pb2.Application(
                id=new_application.id,
                user_id=new_application.user_id,
                name=new_application.name,
                description=new_application.description or "",
                workflow_ids=new_application.workflow_ids or [],
                agent_ids=new_application.agent_ids or [],
                status=getattr(
                    analytics_pb2.ApplicationStatus,
                    f"APPLICATION_STATUS_{new_application.status.upper()}",
                ),
                created_at=new_application.created_at.isoformat(),
                updated_at=new_application.updated_at.isoformat(),
                api_keys=new_application.api_keys or [],
                is_deleted=new_application.is_deleted or False,
            )

            return analytics_pb2.CreateApplicationResponse(
                success=True, message="Application created successfully", application=app_pb
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating application: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.CreateApplicationResponse(
                success=False,
                message=f"Error creating application: {str(e)}",
                application=analytics_pb2.Application(),
            )

    def GetApplications(
        self, request: analytics_pb2.GetApplicationsRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetApplicationsResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_applications_request",
                user_id=request.user_id,
                status=(
                    analytics_pb2.ApplicationStatus.Name(request.status) if request.status else None
                ),
                limit=request.limit,
                offset=request.offset,
            )

            # Build query - exclude soft-deleted applications by default
            query = db.query(Application).filter(
                Application.user_id == request.user_id,
                Application.is_deleted.is_not(True),  # Include NULL and False values
            )

            if (
                request.status
                and request.status != analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED
            ):
                status_str = (
                    analytics_pb2.ApplicationStatus.Name(request.status)
                    .replace("APPLICATION_STATUS_", "")
                    .lower()
                )
                query = query.filter(Application.status == status_str)

            # Get total count
            total_count = query.count()

            # Order by creation date (newest first) - must be done before limit/offset
            query = query.order_by(desc(Application.created_at))

            # Apply pagination
            if request.limit > 0:
                query = query.limit(request.limit)
            if request.offset > 0:
                query = query.offset(request.offset)

            # Execute query
            applications = query.all()

            # Convert to protobuf messages
            app_list = []
            for app in applications:
                app_pb = analytics_pb2.Application(
                    id=app.id,
                    user_id=app.user_id,
                    name=app.name,
                    description=app.description or "",
                    workflow_ids=app.workflow_ids or [],
                    agent_ids=app.agent_ids or [],
                    status=getattr(
                        analytics_pb2.ApplicationStatus, f"APPLICATION_STATUS_{app.status.upper()}"
                    ),
                    created_at=app.created_at.isoformat(),
                    updated_at=app.updated_at.isoformat(),
                    api_keys=app.api_keys or [],
                    is_deleted=app.is_deleted or False,
                )
                app_list.append(app_pb)

            return analytics_pb2.GetApplicationsResponse(
                success=True,
                message="Applications retrieved successfully",
                applications=app_list,
                total_count=total_count,
            )

        except Exception as e:
            logger.error(f"Error getting applications: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetApplicationsResponse(
                success=False,
                message=f"Error getting applications: {str(e)}",
                applications=[],
                total_count=0,
            )

    def GetApplication(
        self, request: analytics_pb2.GetApplicationRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetApplicationResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_application_request",
                application_id=request.application_id,
                user_id=request.user_id,
            )

            # Get application by ID
            application = (
                db.query(Application)
                .filter(
                    Application.id == request.application_id,
                    Application.user_id == request.user_id,
                )
                .first()
            )

            if not application:
                return analytics_pb2.GetApplicationResponse(
                    success=False,
                    message=f"Application not found with ID {request.application_id}",
                    application=analytics_pb2.Application(),
                    metrics=analytics_pb2.ApplicationMetrics(),
                )

            # Get attached images count
            images_count = (
                db.query(ApplicationImage)
                .filter(ApplicationImage.application_id == application.id)
                .count()
            )

            # Get application metrics (mock data for now)
            metrics = analytics_pb2.ApplicationMetrics(
                application_id=application.id,
                total_requests=0,  # Would calculate from actual usage data
                successful_requests=0,
                failed_requests=0,
                credits_used=0.0,
                last_request_at="",
                usage_trend=[],
            )

            app_pb = analytics_pb2.Application(
                id=application.id,
                user_id=application.user_id,
                name=application.name,
                description=application.description or "",
                workflow_ids=application.workflow_ids or [],
                agent_ids=application.agent_ids or [],
                status=getattr(
                    analytics_pb2.ApplicationStatus,
                    f"APPLICATION_STATUS_{application.status.upper()}",
                ),
                created_at=application.created_at.isoformat(),
                updated_at=application.updated_at.isoformat(),
                api_keys=application.api_keys or [],
                is_deleted=application.is_deleted or False,
            )

            return analytics_pb2.GetApplicationResponse(
                success=True,
                message="Application retrieved successfully",
                application=app_pb,
                metrics=metrics,
            )

        except Exception as e:
            logger.error(f"Error getting application: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetApplicationResponse(
                success=False,
                message=f"Error getting application: {str(e)}",
                application=analytics_pb2.Application(),
                metrics=analytics_pb2.ApplicationMetrics(),
            )

    def UpdateApplication(
        self, request: analytics_pb2.UpdateApplicationRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.UpdateApplicationResponse:
        db = self.get_db()
        try:
            logger.info(
                "update_application_request",
                application_id=request.application_id,
                user_id=request.user_id,
                name=request.name,
            )

            # Get application
            application = (
                db.query(Application)
                .filter(
                    Application.id == request.application_id, Application.user_id == request.user_id
                )
                .first()
            )

            if not application:
                return analytics_pb2.UpdateApplicationResponse(
                    success=False,
                    message=f"Application not found with ID {request.application_id}",
                    application=analytics_pb2.Application(),
                )

            # Update application fields
            if request.name:
                application.name = request.name
            if request.description:
                application.description = request.description
            if request.workflow_ids:
                application.workflow_ids = list(request.workflow_ids)
            if request.agent_ids:
                application.agent_ids = list(request.agent_ids)
            if request.api_keys:
                application.api_keys = list(request.api_keys)
            if (
                request.status
                and request.status != analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED
            ):
                status_str = (
                    analytics_pb2.ApplicationStatus.Name(request.status)
                    .replace("APPLICATION_STATUS_", "")
                    .lower()
                )
                application.status = status_str
            # Note: is_deleted field is typically not updated via regular update operations
            # It's usually handled by dedicated soft delete operations

            application.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(application)

            app_pb = analytics_pb2.Application(
                id=application.id,
                user_id=application.user_id,
                name=application.name,
                description=application.description or "",
                workflow_ids=application.workflow_ids or [],
                agent_ids=application.agent_ids or [],
                status=getattr(
                    analytics_pb2.ApplicationStatus,
                    f"APPLICATION_STATUS_{application.status.upper()}",
                ),
                created_at=application.created_at.isoformat(),
                updated_at=application.updated_at.isoformat(),
                api_keys=application.api_keys or [],
                is_deleted=application.is_deleted or False,
            )

            return analytics_pb2.UpdateApplicationResponse(
                success=True, message="Application updated successfully", application=app_pb
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating application: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.UpdateApplicationResponse(
                success=False,
                message=f"Error updating application: {str(e)}",
                application=analytics_pb2.Application(),
            )

    def DeleteApplication(
        self, request: analytics_pb2.DeleteApplicationRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.DeleteApplicationResponse:
        db = self.get_db()
        try:
            logger.info(
                "delete_application_request",
                application_id=request.application_id,
                user_id=request.user_id,
            )

            # Get application
            application = (
                db.query(Application)
                .filter(
                    Application.id == request.application_id, Application.user_id == request.user_id
                )
                .first()
            )

            if not application:
                return analytics_pb2.DeleteApplicationResponse(
                    success=False, message=f"Application not found with ID {request.application_id}"
                )

            # Delete associated images first
            db.query(ApplicationImage).filter(
                ApplicationImage.application_id == request.application_id
            ).delete()

            # Delete application
            db.delete(application)
            db.commit()

            return analytics_pb2.DeleteApplicationResponse(
                success=True, message="Application deleted successfully"
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting application: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.DeleteApplicationResponse(
                success=False, message=f"Error deleting application: {str(e)}"
            )
