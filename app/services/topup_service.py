"""
Topup Service for handling topup plan operations.
Handles topup plan creation, checkout sessions, and credit additions.
"""

import grpc
import stripe
import structlog
import json
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
from decimal import Decimal

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.topup import TopupPlan
from app.models.payment_models import (
    PaymentCustomer,
    PaymentSubscription,
    PaymentTransaction,
    PaymentTransactionType,
    PaymentTransactionStatus,
)
from app.grpc import payment_pb2

stripe.api_key = settings.STRIPE_API_KEY
logger = structlog.get_logger(__name__)


class TopupService:
    """Service for handling topup plan operations."""

    def __init__(self):
        pass

    def _get_db_session(self) -> Session:
        """Get a new database session for each request."""
        return SessionLocal()

    def _to_topup_plan_proto(self, topup_plan: TopupPlan) -> payment_pb2.TopupPlan:
        """Convert TopupPlan model to protobuf message."""
        return payment_pb2.TopupPlan(
            id=topup_plan.id,
            plan_id_code=topup_plan.plan_id_code,
            name=topup_plan.name,
            credit_amount=float(topup_plan.credit_amount),
            price=float(topup_plan.price),
            stripe_price_id=topup_plan.stripe_price_id,
        )

    async def create_topup_checkout_session(self, request: payment_pb2.CreateTopupCheckoutSessionRequest, context):
        """Create a Stripe checkout session for topup purchase."""
        logger.info("RPC: CreateTopupCheckoutSession",
                   org_id=request.organisation_id,
                   topup_plan=request.topup_plan_id_code)

        db = self._get_db_session()
        try:
            # Find the topup plan
            topup_plan = db.query(TopupPlan).filter(
                TopupPlan.plan_id_code == request.topup_plan_id_code
            ).first()

            if not topup_plan or not topup_plan.stripe_price_id:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Topup plan or Stripe price ID not found.")
                return payment_pb2.CreateTopupCheckoutSessionResponse(
                    success=False,
                    error_message="Topup plan or Stripe price ID not found."
                )

            # Get or create customer
            customer = db.query(PaymentCustomer).filter_by(
                organisation_id=request.organisation_id
            ).first()

            if not customer:
                # Create customer in Stripe
                stripe_customer = stripe.Customer.create(
                    metadata={"organisation_id": request.organisation_id}
                )
                customer = PaymentCustomer(
                    organisation_id=request.organisation_id,
                    stripe_customer_id=stripe_customer.id,
                )
                db.add(customer)
                db.commit()
                db.refresh(customer)

            # Create checkout session for one-time payment (not subscription)
            session = stripe.checkout.Session.create(
                customer=customer.stripe_customer_id,
                payment_method_types=["card"],
                line_items=[{
                    "price": topup_plan.stripe_price_id,
                    "quantity": 1,
                }],
                mode="payment",  # One-time payment, not subscription
                success_url=request.success_url,
                cancel_url=request.cancel_url,
                metadata={
                    "organisation_id": request.organisation_id,
                    "topup_plan_id": str(topup_plan.id),
                    "type": "topup_purchase"
                }
            )

            logger.info("Successfully created topup checkout session",
                       org_id=request.organisation_id,
                       session_id=session.id,
                       topup_plan=topup_plan.name)

            return payment_pb2.CreateTopupCheckoutSessionResponse(
                checkout_url=session.url,
                session_id=session.id,
                success=True
            )

        except stripe.error.StripeError as e:
            logger.error("Stripe error while creating topup checkout session", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Stripe error: {str(e)}")
            return payment_pb2.CreateTopupCheckoutSessionResponse(
                success=False,
                error_message=f"Stripe error: {str(e)}"
            )
        except Exception as e:
            logger.error("Error creating topup checkout session", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.CreateTopupCheckoutSessionResponse(
                success=False,
                error_message="Internal server error"
            )
        finally:
            db.close()

    def list_topup_plans(self, request: payment_pb2.ListTopupPlansRequest, context):
        """List all available topup plans."""
        logger.info("RPC: ListTopupPlans")
        db = self._get_db_session()
        try:
            topup_plans = db.query(TopupPlan).all()
            return payment_pb2.ListTopupPlansResponse(
                topup_plans=[self._to_topup_plan_proto(p) for p in topup_plans],
                success=True
            )
        except Exception as e:
            logger.error("Error in ListTopupPlans", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.ListTopupPlansResponse(
                topup_plans=[],
                success=False,
                error_message=str(e)
            )
        finally:
            db.close()

    def create_topup_plan(self, request: payment_pb2.CreateTopupPlanRequest, context):
        """Create a new topup plan."""
        plan_id_code = request.name.lower().replace(" ", "-")
        logger.info("RPC: CreateTopupPlan",
                   plan_name=request.name,
                   generated_plan_code=plan_id_code)

        db = self._get_db_session()
        try:
            # Check if a topup plan with the same code already exists
            existing_plan = db.query(TopupPlan).filter(
                TopupPlan.plan_id_code == plan_id_code
            ).first()

            if existing_plan:
                logger.warn("Topup plan with this ID code already exists",
                           plan_id_code=plan_id_code)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"A topup plan with code '{plan_id_code}' already exists.")
                return payment_pb2.TopupPlan()

            try:
                # Create a product in Stripe for one-time payment
                product = stripe.Product.create(name=request.name)

                # Create a price in Stripe for one-time payment (no recurring)
                price = stripe.Price.create(
                    product=product.id,
                    unit_amount=int(request.price * 100),  # Stripe expects amount in cents
                    currency="usd",
                    # No recurring parameter for one-time payments
                )

                db_topup_plan = TopupPlan(
                    plan_id_code=plan_id_code,
                    name=request.name,
                    credit_amount=request.credit_amount,
                    price=request.price,
                    stripe_price_id=price.id,
                )
                db.add(db_topup_plan)
                db.commit()
                db.refresh(db_topup_plan)

                logger.info("Successfully created topup plan in DB and Stripe",
                           plan_id=db_topup_plan.id,
                           stripe_product_id=product.id,
                           stripe_price_id=price.id)

                return self._to_topup_plan_proto(db_topup_plan)

            except stripe.error.StripeError as e:
                logger.error("Stripe error while creating topup plan", error=str(e))
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Stripe error: {str(e)}")
                return payment_pb2.TopupPlan()

        except Exception as e:
            logger.error("Error creating topup plan", error=str(e))
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal error occurred.")
            return payment_pb2.TopupPlan()
        finally:
            db.close()

    async def handle_topup_purchase(self, session: Dict[str, Any], db_session: Session):
        """Handle completed topup purchase from checkout session."""
        org_id = session.get("metadata", {}).get("organisation_id")
        topup_plan_id = session.get("metadata", {}).get("topup_plan_id")
        stripe_customer_id = session.get("customer")

        if not org_id or not topup_plan_id:
            logger.error("Webhook 'topup_purchase' missing required metadata.",
                         metadata=session.get("metadata"))
            return

        logger.info(
            "Processing topup purchase webhook",
            event_id=session.get("id"),
            org_id=org_id,
            topup_plan_id=topup_plan_id,
            stripe_customer_id=stripe_customer_id
        )

        try:
            # Get the topup plan
            topup_plan = db_session.query(TopupPlan).filter_by(id=topup_plan_id).first()
            if not topup_plan:
                logger.error("Topup plan not found in DB from webhook.", topup_plan_id=topup_plan_id)
                return

            # Find the existing subscription to add credits to
            subscription = db_session.query(PaymentSubscription).filter_by(
                organisation_id=org_id
            ).first()
            if not subscription:
                logger.error("No subscription found for topup purchase.", org_id=org_id)
                return

            # Add topup credits to current balance
            old_balance = subscription.current_credits
            subscription.current_credits += topup_plan.credit_amount
            new_balance = subscription.current_credits

            logger.info("Adding topup credits to subscription",
                       org_id=org_id,
                       topup_plan=topup_plan.name,
                       credits_added=float(topup_plan.credit_amount),
                       old_balance=float(old_balance),
                       new_balance=float(new_balance))

            # Ensure customer ID is up-to-date
            customer = db_session.query(PaymentCustomer).filter_by(
                organisation_id=org_id
            ).first()
            if customer and customer.stripe_customer_id != stripe_customer_id:
                logger.info("Updating Stripe customer ID in DB.", org_id=org_id)
                customer.stripe_customer_id = stripe_customer_id

            db_session.commit()  # Commit subscription changes first
            db_session.refresh(subscription)

            # Create transaction record for topup purchase
            transaction = PaymentTransaction(
                organisation_id=org_id,
                transaction_type=PaymentTransactionType.CREDIT_PURCHASE,
                status=PaymentTransactionStatus.COMPLETED,
                amount_currency=session.get("amount_total", 0),  # amount_total is in cents
                currency=session.get("currency", "usd"),
                description=f"Topup purchase: {topup_plan.name} (+{topup_plan.credit_amount} credits)",
                stripe_invoice_id=session.get("invoice"),
                subscription_id=subscription.id
            )
            db_session.add(transaction)

            logger.info("Successfully processed topup purchase.",
                       org_id=org_id,
                       subscription_id=subscription.id,
                       credits_added=float(topup_plan.credit_amount),
                       new_balance=float(subscription.current_credits))

        except Exception as e:
            logger.error("Error processing topup purchase webhook", error=str(e), org_id=org_id)


def initialize_topup_plans():
    """Initialize default topup plans if they don't exist."""
    db = SessionLocal()
    try:
        # Check if topup plans already exist
        existing_plans = db.query(TopupPlan).count()
        if existing_plans > 0:
            logger.info("Topup plans already exist in database", count=existing_plans)
            return

        logger.info("Creating default topup plans...")

        # Define default topup plans
        default_topup_plans = [
            {
                "plan_id_code": "topup_small",
                "name": "Small Credit Topup",
                "credit_amount": 50,
                "price": 5.00
            },
            {
                "plan_id_code": "topup_large",
                "name": "Large Credit Topup",
                "credit_amount": 200,
                "price": 15.00
            }
        ]

        for plan_data in default_topup_plans:
            try:
                # Create product in Stripe
                product = stripe.Product.create(name=plan_data["name"])

                # Create price in Stripe (one-time payment, not recurring)
                price = stripe.Price.create(
                    product=product.id,
                    unit_amount=int(plan_data["price"] * 100),  # Convert to cents
                    currency="usd",
                    # No recurring parameter for one-time payments
                )

                # Create topup plan in database
                topup_plan = TopupPlan(
                    plan_id_code=plan_data["plan_id_code"],
                    name=plan_data["name"],
                    credit_amount=plan_data["credit_amount"],
                    price=plan_data["price"],
                    stripe_price_id=price.id,
                )
                db.add(topup_plan)

                logger.info("Created topup plan",
                           plan_name=plan_data["name"],
                           stripe_product_id=product.id,
                           stripe_price_id=price.id)

            except stripe.error.StripeError as e:
                logger.error("Failed to create topup plan in Stripe",
                           plan_name=plan_data["name"],
                           error=str(e))
                continue
            except Exception as e:
                logger.error("Failed to create topup plan",
                           plan_name=plan_data["name"],
                           error=str(e))
                continue

        db.commit()
        logger.info("Successfully initialized default topup plans")

    except Exception as e:
        logger.error("Error initializing topup plans", error=str(e))
        db.rollback()
    finally:
        db.close()


def get_topup_service() -> TopupService:
    """Get an instance of the TopupService."""
    return TopupService()