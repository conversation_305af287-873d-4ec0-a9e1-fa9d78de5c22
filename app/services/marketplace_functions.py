# app/services/marketplace_functions.py
import grpc
import json
import structlog
from datetime import datetime, timezone

from app.utils.constants.constants import (
    AgentStatusEnum,
    AgentVisibilityEnum,
    AgentOwnerTypeEnum,
    AgentCategoryEnum,
    CategoryEnum,
    AgentToneEnum,
)
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.models.agent import AgentCapabilities, AgentConfig, AgentMarketplaceListing, AgentConfigVersion, AgentModelConfig, AgentKnowledgeBase, AgentVariables
from app.grpc import agent_pb2, agent_pb2_grpc
from app.utils.kafka.kafka_service import KafkaProducer

logger = structlog.get_logger()


class AgentMarketplaceFunctionsService(agent_pb2_grpc.AgentServiceServicer):
    """
    Service for managing agent marketplace functions.

    This service handles marketplace operations like creating agents from templates,
    getting marketplace agent details, and using agents from the marketplace.
    """

    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        """
        Creates and returns a new database session.

        Returns:
            Session: SQLAlchemy database session
        """
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createAgentFromTemplate(
        self, request: agent_pb2.CreateAgentFromTemplateRequest, context: grpc.ServicerContext
    ) -> agent_pb2.CreateAgentFromTemplateResponse:
        """
        Creates a new agent from a marketplace template.
        """
        try:
            db = self.get_db()
            
            # Find the marketplace listing by template_id
            marketplace_listing = (
                db.query(AgentMarketplaceListing)
                .filter(AgentMarketplaceListing.id == request.template_id)
                .first()
            )
            
            if not marketplace_listing:
                return agent_pb2.CreateAgentFromTemplateResponse(
                    success=False,
                    message="Template not found in marketplace"
                )
            
            # Get the original agent config
            original_agent = (
                db.query(AgentConfig)
                .filter(AgentConfig.id == marketplace_listing.agent_config_id)
                .first()
            )
            
            if not original_agent:
                return agent_pb2.CreateAgentFromTemplateResponse(
                    success=False,
                    message="Original agent not found"
                )
            
            # Get the agent version
            agent_version = (
                db.query(AgentConfigVersion)
                .filter(AgentConfigVersion.id == marketplace_listing.agent_config_version_id)
                .first()
            )
            
            if not agent_version:
                return agent_pb2.CreateAgentFromTemplateResponse(
                    success=False,
                    message="Agent version not found"
                )
            
            # Create new agent from template
            new_agent = AgentConfig(
                name=f"{agent_version.name} (Copy)",
                description=agent_version.description,
                avatar=agent_version.avatar,
                owner_id=request.owner.id,
                user_ids=[request.owner.id],
                owner_type=AgentOwnerTypeEnum.USER,  # Default to USER for marketplace agents
                agent_template_id=marketplace_listing.id,
                template_owner_id=marketplace_listing.listed_by_user_id,
                is_imported=True,
                is_updated=False,
                agent_category=agent_version.agent_category,
                system_message=agent_version.system_message,
                workflow_ids=agent_version.workflow_ids or [],
                mcp_server_ids=agent_version.mcp_server_ids or [],
                agent_topic_type=agent_version.agent_topic_type,
                department=agent_version.department,
                organization_id=agent_version.organization_id,
                tone=agent_version.tone,
                is_bench_employee=agent_version.is_bench_employee,
                is_changes_marketplace=agent_version.is_changes_marketplace,
                is_a2a=agent_version.is_a2a,
                is_customizable=agent_version.is_customizable,
                capabilities_id=agent_version.capabilities_id,
                example_prompts=agent_version.example_prompts or [],
                category=agent_version.category,
                tags=agent_version.tags or [],
                status=AgentStatusEnum.ACTIVE,
                visibility=AgentVisibilityEnum.PRIVATE
            )
            
            db.add(new_agent)
            db.commit()
            
            return agent_pb2.CreateAgentFromTemplateResponse(
                success=True,
                message=f"Agent '{new_agent.name}' successfully created from template"
            )
            
        except Exception as e:
            logger.error(f"Failed to create agent from template: {str(e)}")
            if 'db' in locals():
                db.rollback()
            return agent_pb2.CreateAgentFromTemplateResponse(
                success=False,
                message="Failed to create agent from template"
            )
        finally:
            if 'db' in locals():
                db.close()

    def getMarketplaceAgentDetail(
        self, request: agent_pb2.GetMarketplaceAgentDetailRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetMarketplaceAgentDetailResponse:
        """
        Retrieves detailed information about a specific marketplace agent.
        """
        try:
            db = self.get_db()
            
            # Find the marketplace listing by agent_id
            marketplace_listing = (
                db.query(AgentMarketplaceListing)
                .filter(AgentMarketplaceListing.id == request.id)
                .first()
            )
            if not marketplace_listing:
                return agent_pb2.GetMarketplaceAgentDetailResponse(
                    success=False,
                    message="Agent not found in marketplace"
                )
            
            # Get the agent version for detailed information
            agent_version = (
                db.query(AgentConfigVersion)
                .filter(AgentConfigVersion.id == marketplace_listing.agent_config_version_id)
                .first()
            )
            
            # Get the original agent config for additional details
            original_agent = (
                db.query(AgentConfig)
                .filter(AgentConfig.id == marketplace_listing.agent_config_id)
                .first()
            )
            
            # Get agent capabilities if available
            agent_capabilities = None
            if agent_version and agent_version.capabilities_id:
                agent_capabilities = (
                    db.query(AgentCapabilities)
                    .filter(AgentCapabilities.id == agent_version.capabilities_id)
                    .first()
                )
            
            # Get model config if available
            model_config = None
            if agent_version and agent_version.model_config_id:
                model_config = (
                    db.query(AgentModelConfig)
                    .filter(AgentModelConfig.id == agent_version.model_config_id)
                    .first()
                )
            
            # Get knowledge base if available
            knowledge_base = None
            if agent_version and agent_version.knowledge_base_id:
                knowledge_base = (
                    db.query(AgentKnowledgeBase)
                    .filter(AgentKnowledgeBase.id == agent_version.knowledge_base_id)
                    .first()
                )
            
            # Get owner name (you might need to query a User table if available)
            # For now, we'll leave it as None since we don't have access to user service
            owner_name = None
            
            # Check if user already has this agent (is_added field)
            is_added = False
            if request.user_id:
                existing_agent = (
                    db.query(AgentConfig)
                    .filter(
                        AgentConfig.agent_template_id == marketplace_listing.id,
                        AgentConfig.owner_id == request.user_id,
                        AgentConfig.is_imported == True
                    )
                    .first()
                )
                is_added = existing_agent is not None
            
            # Create marketplace agent protobuf with all available data
            marketplace_agent = agent_pb2.MarketplaceAgent()
            marketplace_agent.id = marketplace_listing.id
            marketplace_agent.name = marketplace_listing.title
            marketplace_agent.description = marketplace_listing.description or ""
            marketplace_agent.avatar = marketplace_listing.image_url or ""
            marketplace_agent.owner_id = marketplace_listing.listed_by_user_id
            marketplace_agent.use_count = marketplace_listing.use_count or 0
            marketplace_agent.average_rating = marketplace_listing.average_rating or 0.0
            marketplace_agent.tags.extend(marketplace_listing.tags or [])
            marketplace_agent.status = marketplace_listing.status.value
            marketplace_agent.created_at = marketplace_listing.created_at.isoformat()
            marketplace_agent.updated_at = marketplace_listing.updated_at.isoformat()
            marketplace_agent.visibility = marketplace_listing.visibility.value
            marketplace_agent.is_added = is_added
            marketplace_agent.source_agent_id = marketplace_listing.agent_config_id
            marketplace_agent.version = marketplace_listing.version_number
            print("marketplace_listing.agent_config_id", marketplace_listing.agent_config_id)
            # Add detailed information from agent version if available
            if agent_version:
                # Basic agent information
                marketplace_agent.agent_category = agent_version.agent_category.value if agent_version.agent_category else ""
                marketplace_agent.system_message = agent_version.system_message or ""
                
                # Department
                marketplace_agent.department = agent_version.department or ""
                
                # Category - convert enum to string if available
                if agent_version.category:
                    marketplace_agent.category = agent_version.category.value if hasattr(agent_version.category, 'value') else str(agent_version.category)
                else:
                    marketplace_agent.category = ""
                
                # Workflow IDs
                marketplace_agent.workflow_ids.extend(agent_version.workflow_ids or [])
                
                # MCP Server IDs
                marketplace_agent.mcp_server_ids.extend(agent_version.mcp_server_ids or [])
                
                # Agent topic type
                marketplace_agent.agent_topic_type = agent_version.agent_topic_type or ""
                
                # Subscriptions (not stored in version, using empty string)
                marketplace_agent.subscriptions = ""
                
                # Tone
                marketplace_agent.tone = agent_version.tone.value if agent_version.tone else ""
                
                # Boolean flags
                marketplace_agent.is_a2a = agent_version.is_a2a or False
                marketplace_agent.is_customizable = agent_version.is_customizable or False
                
                # Example prompts
                marketplace_agent.example_prompts.extend(agent_version.example_prompts or [])
                
                # Capabilities ID
                marketplace_agent.capabilities_id = agent_version.capabilities_id or ""
                
                marketplace_agent.source_agent_id = agent_version.agent_config_id
                
                marketplace_agent.version = agent_version.version_number
                
                print("agent_version.agent_config_id", agent_version.agent_config_id)
            else:
                # Set default values if agent version is not available
                marketplace_agent.agent_category = ""
                marketplace_agent.system_message = ""
                marketplace_agent.department = ""
                marketplace_agent.category = ""
                marketplace_agent.agent_topic_type = ""
                marketplace_agent.subscriptions = ""
                marketplace_agent.tone = ""
                marketplace_agent.is_a2a = False
                marketplace_agent.is_customizable = False
                marketplace_agent.capabilities_id = ""
            
            # Add model configuration if available
            if model_config:
                marketplace_agent.model_provider = model_config.model_provider or ""
                marketplace_agent.model_name = model_config.model_name or ""
                marketplace_agent.temperature = model_config.temperature or 0.0
                marketplace_agent.max_tokens = model_config.max_tokens or 0
            else:
                marketplace_agent.model_provider = ""
                marketplace_agent.model_name = ""
                marketplace_agent.temperature = 0.0
                marketplace_agent.max_tokens = 0

            # Add knowledge base information if available
            if knowledge_base:
                marketplace_agent.files.extend(knowledge_base.files or [])
                marketplace_agent.urls.extend(knowledge_base.urls or [])
            
            if agent_capabilities:
                capabilities_msg = agent_pb2.AgentCapabilities()
                capabilities_msg.id = str(agent_capabilities.id)

                # Serialize capabilities
                capabilities = agent_capabilities.capabilities
                if capabilities:
                    if isinstance(capabilities, str):
                        try:
                            capabilities_msg.capabilities = json.dumps(json.loads(capabilities))
                        except json.JSONDecodeError:
                            capabilities_msg.capabilities = capabilities
                    else:
                        capabilities_msg.capabilities = json.dumps(capabilities)
                else:
                    capabilities_msg.capabilities = ""

                capabilities_msg.input_modes.extend(agent_capabilities.input_modes or [])
                capabilities_msg.output_modes.extend(agent_capabilities.output_modes or [])
                capabilities_msg.response_model.extend(agent_capabilities.response_model or [])
                capabilities_msg.created_at = (
                    agent_capabilities.created_at.isoformat() if agent_capabilities.created_at else ""
                )
                capabilities_msg.updated_at = (
                    agent_capabilities.updated_at.isoformat() if agent_capabilities.updated_at else ""
                )
                
                marketplace_agent.agent_capabilities.CopyFrom(capabilities_msg)

            
            logger.info(
                "marketplace_agent_detail_retrieved",
                agent_id=request.id,
                user_id=request.user_id,
                is_added=is_added,
                has_version=agent_version is not None,
                has_capabilities=agent_capabilities is not None
            )
            
            return agent_pb2.GetMarketplaceAgentDetailResponse(
                success=True,
                message="Agent details retrieved successfully",
                agent=marketplace_agent
            )
            
        except Exception as e:
            logger.error(f"Failed to get marketplace agent detail: {str(e)}", exc_info=True)
            return agent_pb2.GetMarketplaceAgentDetailResponse(
                success=False,
                message="Failed to retrieve agent details"
            )
        finally:
            if 'db' in locals():
                db.close()

    def useAgent(
        self, request: agent_pb2.UseAgentRequest, context: grpc.ServicerContext
    ) -> agent_pb2.UseAgentResponse:
        """
        Create a copy of a marketplace agent for a user, create its first version,
        clone associated configurations, and increment the listing's use count.
        """
        db: Session = self.get_db() # Make sure self.get_db() provides a valid session
        try:
            logger.info(
                "use_agent_request", extra={"listing_id": request.agent_id, "user_id": request.user_id}
            )

            workflow_ids = request.workflow_ids if request.workflow_ids else None
            mcp_ids = request.mcp_ids if request.mcp_ids else None
            
            
            # 1. Find the marketplace listing
            marketplace_listing = (
                db.query(AgentMarketplaceListing)
                .filter(
                    AgentMarketplaceListing.id == request.agent_id,
                    AgentMarketplaceListing.visibility == AgentVisibilityEnum.PUBLIC,
                    AgentMarketplaceListing.status == AgentStatusEnum.ACTIVE,
                )
                .first()
            )

            if not marketplace_listing:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                details = f"Public and active marketplace listing with ID {request.agent_id} not found."
                context.set_details(details)
                return agent_pb2.UseAgentResponse(
                    success=False, message=details, use_count=0
                )

            # 2. Check if user already has this agent cloned
            existing_agent_clone = (
                db.query(AgentConfig)
                .filter(
                    AgentConfig.agent_template_id == marketplace_listing.id,
                    AgentConfig.owner_id == request.user_id,
                    AgentConfig.is_imported == True,
                )
                .first()
            )

            if existing_agent_clone:
                details = f"You have already added agent from marketplace listing ID {request.agent_id} (Agent ID: {existing_agent_clone.id})."
                return agent_pb2.UseAgentResponse(
                    success=False, # Or True, if "already exists" is not an error
                    message=details,
                    use_count=marketplace_listing.use_count,
                )

            # 3. Get the source AgentConfigVersion associated with this listing
            source_agent_config_version = (
                db.query(AgentConfigVersion)
                .filter(AgentConfigVersion.id == marketplace_listing.agent_config_version_id)
                .first()
            )

            if not source_agent_config_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                details = f"Agent config version for marketplace listing {request.agent_id} not found."
                context.set_details(details)
                return agent_pb2.UseAgentResponse(
                    success=False, message=details, use_count=marketplace_listing.use_count
                )

            # 4. Get the source AgentConfig (parent of the version)
            source_agent_config_parent = ( # Renamed to avoid confusion with new_agent_config
                db.query(AgentConfig)
                .filter(AgentConfig.id == source_agent_config_version.agent_config_id)
                .first()
            )

            if not source_agent_config_parent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                details = f"Original agent config (parent) for marketplace listing {request.agent_id} not found."
                context.set_details(details)
                return agent_pb2.UseAgentResponse(
                    success=False, message=details, use_count=marketplace_listing.use_count
                )

            # 5. Determine customizability for the clone
            is_customizable_for_clone = source_agent_config_parent.is_customizable

            # --- Start Deep Cloning of Associated Entities ---
            new_capabilities_id = None
            if source_agent_config_version.capabilities_id:
                source_capabilities = db.get(AgentCapabilities, source_agent_config_version.capabilities_id)
                if source_capabilities:
                    # CRITICAL: Determine the structure of 'capabilities' JSON.
                    # If your `createAgent` (when `agent_capabilities.capabilities_json` is a JSON object string)
                    # results in `AgentCapabilities.capabilities` being a Python dict, then clone as a dict.
                    # If it's always a list, then clone as a list.
                    # Let's assume for consistency with potential direct dict storage from createAgent:
                    cloned_capabilities_json_data = None
                    if source_capabilities.capabilities is not None:
                        # Simple deep copy. If source_capabilities.capabilities is already a dict or list,
                        # this will create a new dict/list with copied values.
                        cloned_capabilities_json_data = json.loads(json.dumps(source_capabilities.capabilities))


                    new_capabilities = AgentCapabilities(
                        capabilities=cloned_capabilities_json_data, # Store as dict or list, matching source
                        input_modes=list(source_capabilities.input_modes or []),
                        output_modes=list(source_capabilities.output_modes or []),
                        response_model=list(source_capabilities.response_model or [])
                    )
                    db.add(new_capabilities)
                    db.flush()
                    new_capabilities_id = new_capabilities.id
                    logger.info(f"Cloned AgentCapabilities, new ID: {new_capabilities_id}")


            new_model_config_id = None
            if source_agent_config_version.model_config_id:
                source_model_config = db.get(AgentModelConfig, source_agent_config_version.model_config_id)
                if source_model_config:
                    new_model_config = AgentModelConfig(
                        model_provider=source_model_config.model_provider,
                        model_name=source_model_config.model_name,
                        temperature=source_model_config.temperature,
                        max_tokens=source_model_config.max_tokens
                    )
                    db.add(new_model_config)
                    db.flush()
                    new_model_config_id = new_model_config.id
                    logger.info(f"Cloned AgentModelConfig, new ID: {new_model_config_id}")


            new_knowledge_base_id = None
            if source_agent_config_version.knowledge_base_id:
                source_knowledge_base = db.get(AgentKnowledgeBase, source_agent_config_version.knowledge_base_id)
                if source_knowledge_base:
                    new_knowledge_base = AgentKnowledgeBase(
                        files=list(source_knowledge_base.files or []),
                        urls=list(source_knowledge_base.urls or [])
                    )
                    db.add(new_knowledge_base)
                    db.flush()
                    new_knowledge_base_id = new_knowledge_base.id
                    logger.info(f"Cloned AgentKnowledgeBase, new ID: {new_knowledge_base_id}")

            # --- End Deep Cloning of Associated Entities ---

            # 6. Create the new AgentConfig (the user's own copy)
            # This acts as the "current" or "draft" state for the user's agent.
            # Its configuration is based on the source *version* they are cloning.
            new_agent_config = AgentConfig(
                name=source_agent_config_version.name, # Or marketplace_listing.title
                description=source_agent_config_version.description, # Or marketplace_listing.description
                avatar=source_agent_config_version.avatar, # Or marketplace_listing.image_url

                owner_id=request.user_id,
                user_ids=[request.user_id],
                owner_type=AgentOwnerTypeEnum.USER, # Cloned agents are owned by users

                agent_template_id=marketplace_listing.id,
                template_owner_id=marketplace_listing.listed_by_user_id,
                is_imported=True,
                is_updated=False,
                is_customizable=is_customizable_for_clone,

                # Fields mirrored from AgentConfigVersion to represent the "draft" state
                agent_category=source_agent_config_version.agent_category,
                system_message=source_agent_config_version.system_message,
                workflow_ids=workflow_ids,
                mcp_server_ids=mcp_ids,
                agent_topic_type=source_agent_config_version.agent_topic_type,
                department=source_agent_config_version.department, # Or user's current department?
                organization_id=source_agent_config_version.organization_id, # Or user's current org?
                tone=source_agent_config_version.tone,
                is_bench_employee=source_agent_config_version.is_bench_employee, # This might need review for cloned context
                is_changes_marketplace=False, # New clone, no changes for marketplace yet
                is_a2a=source_agent_config_version.is_a2a,

                capabilities_id=new_capabilities_id, # Link to the *cloned* capabilities
                example_prompts=list(source_agent_config_version.example_prompts or []),
                category=source_agent_config_version.category,
                tags=list(source_agent_config_version.tags or []), # Tags from the specific version being cloned

                visibility=AgentVisibilityEnum.PRIVATE, # User's copy is private by default
                status=AgentStatusEnum.ACTIVE, # Cloned agent is active by default
                # use_count and average_rating default to 0 for the new AgentConfig
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.add(new_agent_config)
            db.flush() # Get new_agent_config.id for variables and version
            logger.info(f"Created new AgentConfig (clone), ID: {new_agent_config.id}")


            # 7. Clone AgentVariables associated with the *source_agent_config_parent*
            # Variables are tied to the AgentConfig, not the version.
            source_variables = (
                db.query(AgentVariables)
                .filter(AgentVariables.agent_config_id == source_agent_config_parent.id)
                .all()
            )
            cloned_variables_for_new_agent = []
            for svar in source_variables:
                new_var = AgentVariables(
                    name=svar.name,
                    description=svar.description,
                    type=svar.type,
                    default_value=svar.default_value,
                    agent_config_id=new_agent_config.id # Link to the new agent config
                )
                cloned_variables_for_new_agent.append(new_var)
            
            if cloned_variables_for_new_agent:
                db.add_all(cloned_variables_for_new_agent)
                # No need to flush here specifically unless IDs are needed.
                # The relationship `new_agent_config.variables` will be populated after commit/refresh
                # or if you manually assign: new_agent_config.variables = cloned_variables_for_new_agent
            logger.info(f"Cloned {len(cloned_variables_for_new_agent)} variables for new AgentConfig ID: {new_agent_config.id}")


            # 8. Create the first AgentConfigVersion for the new_agent_config
            # This version reflects the state of the `source_agent_config_version` it was cloned from.
            v1_agent_config_version = AgentConfigVersion(
                agent_config_id=new_agent_config.id,
                version_number="1.0.0",
                name=new_agent_config.name, # Consistent with parent AgentConfig's initial state
                description=new_agent_config.description,
                avatar=new_agent_config.avatar,

                model_config_id=new_model_config_id, # Link to cloned model config
                knowledge_base_id=new_knowledge_base_id, # Link to cloned KB
                capabilities_id=new_capabilities_id, # Link to cloned capabilities

                # Copy other version-specific fields from the source_agent_config_version
                # as this new version *is* a clone of that state.
                agent_category=source_agent_config_version.agent_category,
                system_message=source_agent_config_version.system_message,
                workflow_ids=workflow_ids,
                mcp_server_ids=mcp_ids,
                agent_topic_type=source_agent_config_version.agent_topic_type,
                department=source_agent_config_version.department,
                organization_id=source_agent_config_version.organization_id,
                tone=source_agent_config_version.tone,
                is_bench_employee=source_agent_config_version.is_bench_employee,
                is_changes_marketplace=False, # First version of a clone
                is_a2a=source_agent_config_version.is_a2a,
                is_customizable=is_customizable_for_clone, # Inherited customizability
                example_prompts=list(source_agent_config_version.example_prompts or []),
                category=source_agent_config_version.category,
                tags=list(source_agent_config_version.tags or []),
                status=AgentStatusEnum.ACTIVE,
                version_notes="Initial version created from marketplace listing.",
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.add(v1_agent_config_version)
            db.flush() # Get v1_agent_config_version.id
            logger.info(f"Created new AgentConfigVersion v1.0.0, ID: {v1_agent_config_version.id} for AgentConfig ID: {new_agent_config.id}")


            # 9. Link new_agent_config to its first version (current_version_id)
            new_agent_config.current_version_id = v1_agent_config_version.id
            # db.add(new_agent_config) # SQLAlchemy tracks changes to managed objects

            # 10. Increment marketplace listing's use_count
            marketplace_listing.use_count += 1
            marketplace_listing.updated_at = datetime.now(timezone.utc)
            # db.add(marketplace_listing) # SQLAlchemy tracks changes

            # 11. Commit transaction
            db.commit()

            # 12. Refresh objects to get DB-generated values and reflect committed state (optional but good practice)
            db.refresh(new_agent_config)
            db.refresh(marketplace_listing)
            db.refresh(v1_agent_config_version)
            # To populate the new_agent_config.variables relationship after commit:
            # db.refresh(new_agent_config, attribute_names=['variables'])

            logger.info(
                "agent_cloned_from_marketplace_successfully",
                extra={
                    "new_agent_config_id": new_agent_config.id,
                    "new_agent_version_id": v1_agent_config_version.id,
                    "marketplace_listing_id": marketplace_listing.id,
                    "user_id": request.user_id,
                    "new_use_count": marketplace_listing.use_count,
                }
            )

            return agent_pb2.UseAgentResponse(
                success=True,
                message=f"New agent '{new_agent_config.name}' (ID: {new_agent_config.id}) created from marketplace.",
                use_count=marketplace_listing.use_count
            )

        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error using marketplace agent (listing ID: {request.agent_id}): {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while processing agent from marketplace: {str(e)}")
            
            original_use_count = 0
            if 'marketplace_listing' in locals() and marketplace_listing and hasattr(marketplace_listing, 'use_count'):
                original_use_count = marketplace_listing.use_count
            
            return agent_pb2.UseAgentResponse(
                success=False,
                message="Failed to create agent from marketplace listing due to an internal error.",
                use_count=original_use_count,
            )
        finally:
            if db:
                db.close()
                
                
    def getMarketplaceAgents(
        self, request: agent_pb2.GetMarketplaceAgentsRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetMarketplaceAgentsResponse:
        """
        Retrieves a paginated list of public agent marketplace listings.
        
        Args:
            request: The request containing pagination, search, and filter parameters
            context: The gRPC context for handling errors
            
        Returns:
            Response containing the list of marketplace agents and pagination metadata
        """
        db = self.get_db()
        logger.info(
            "get_marketplace_agents_request",
            request=request,
        )
        
        try:
            # Start with a base query for public marketplace listings
            query = db.query(AgentMarketplaceListing).filter(
                AgentMarketplaceListing.visibility == AgentVisibilityEnum.PUBLIC,
                AgentMarketplaceListing.status == AgentStatusEnum.ACTIVE,
            )
            
            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    (AgentMarketplaceListing.title.ilike(search_term))
                    | (AgentMarketplaceListing.description.ilike(search_term))
                )
            
            # Apply category filter if provided
            if request.HasField("category") and request.category:
                # Convert protobuf enum to string value
                category_value = agent_pb2.AgentCategory.Name(request.category).lower()
                query = query.filter(AgentMarketplaceListing.category == category_value)
            
            # Apply tags filter if provided
            if request.tags:
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(AgentMarketplaceListing.tags.contains(request.tags))
            
            # Apply sorting
            if request.HasField("sort_by") and request.sort_by:
                sort_by = request.sort_by
                if sort_by == "NEWEST":
                    query = query.order_by(AgentMarketplaceListing.created_at.desc())
                elif sort_by == "OLDEST":
                    query = query.order_by(AgentMarketplaceListing.created_at.asc())
                elif sort_by == "MOST_POPULAR":
                    query = query.order_by(AgentMarketplaceListing.use_count.desc())
                elif sort_by == "HIGHEST_RATED":
                    query = query.order_by(AgentMarketplaceListing.average_rating.desc())
                else:
                    # Default to newest
                    query = query.order_by(AgentMarketplaceListing.created_at.desc())
            else:
                # Default sorting by newest
                query = query.order_by(AgentMarketplaceListing.created_at.desc())
            
            # Get total count with filters applied
            total = query.count()
            
            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = min(request.page_size, 100) if request.page_size > 0 else 10
            
            listings = query.offset((page - 1) * page_size).limit(page_size).all()
            
            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size if total > 0 else 1
            has_next = page < total_pages
            has_prev = page > 1
            
            # Convert marketplace listings to protobuf format
            agent_list = []
            for listing in listings:
                marketplace_agent = agent_pb2.MarketplaceAgent()
                marketplace_agent.id = listing.id
                marketplace_agent.name = listing.title
                marketplace_agent.description = listing.description or ""
                marketplace_agent.avatar = listing.image_url or ""
                marketplace_agent.owner_id = listing.listed_by_user_id
                marketplace_agent.use_count = listing.use_count
                marketplace_agent.average_rating = listing.average_rating
                marketplace_agent.tags.extend(listing.tags or [])
                marketplace_agent.status = listing.status.value
                marketplace_agent.created_at = listing.created_at.isoformat()
                marketplace_agent.updated_at = listing.updated_at.isoformat()
                marketplace_agent.visibility = listing.visibility.value
                marketplace_agent.is_added = False  # Default to False, can be set based on user context
                marketplace_agent.source_agent_id = listing.agent_config_id
                marketplace_agent.version = listing.version_number
                print("listing.agent_config_id", listing.agent_config_id)
                agent_list.append(marketplace_agent)
            
            logger.info(
                "marketplace_agents_retrieved",
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )
            
            return agent_pb2.GetMarketplaceAgentsResponse(
                success=True,
                message="Marketplace agents retrieved successfully",
                agents=agent_list,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=page + 1 if has_next else 0,
                prev_page=page - 1 if has_prev else 0,
            )
            
        except Exception as e:
            logger.error("get_marketplace_agents_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve marketplace agents: {str(e)}")
            return agent_pb2.GetMarketplaceAgentsResponse(
                success=False,
                message=f"Failed to retrieve marketplace agents: {str(e)}",
                agents=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                total_pages=0,
                has_next=False,
                has_prev=False,
            )
        finally:
            db.close()

    def getTemplate(
        self, request: agent_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> agent_pb2.GetTemplateResponse:
        """
        Retrieves a template by ID.
        NOTE: This function is kept for backward compatibility but not actively used.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Template functionality not implemented yet")
        return agent_pb2.GetTemplateResponse(success=False, message="Template functionality not implemented yet")

    def listTemplates(
        self, request: agent_pb2.ListTemplatesRequest, context: grpc.ServicerContext
    ) -> agent_pb2.ListTemplatesResponse:
        """
        Retrieves a paginated list of templates.
        NOTE: This function is kept for backward compatibility but not actively used.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Template functionality not implemented yet")
        return agent_pb2.ListTemplatesResponse()
