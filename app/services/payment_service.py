"""
Consolidated Payment Service for handling all payment-related logic,
including gRPC, Stripe interactions, and credit management.
"""

import grpc
import stripe
import structlog
import json
from typing import Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from datetime import datetime
from decimal import Decimal, getcontext

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.payment_models import (
    PaymentPlan,
    PaymentCustomer,
    PaymentSubscription,
    PaymentTransaction,
    CreditDeduction,
    TokenUsageLog,
    PaymentWebhookEvent,
    PaymentTransactionType,
    PaymentSubscriptionStatus,
    PaymentTransactionStatus,
)
from app.grpc import payment_pb2, payment_pb2_grpc
from app.schemas.payment import TokenUsageEvent

# Set precision for Decimal operations
getcontext().prec = 18

stripe.api_key = settings.STRIPE_API_KEY
logger = structlog.get_logger(__name__)


class TokenConverter:
    """A simple utility to convert tokens to credits."""
    DOLLAR_PER_RUH_CREDIT = Decimal(str(settings.DOLLAR_TO_CREDIT_RATE))
    TOKENS_PER_MILLION = Decimal(settings.TOKENS_PER_MILLION)

    @staticmethod
    def calculate_credits(
        input_tokens: int,
        output_tokens: int,
        input_price_per_token: float,
        output_price_per_token: float
    ) -> Decimal:
        """
        Calculates the total RUH credits to be deducted based on token counts and model costs.
        This is a pure calculation function. It first calculates the cost in US Dollars ($)
        and then converts that cost into RUH credits.
        Args:
            input_tokens: The number of tokens in the input/prompt.
            output_tokens: The number of tokens in the generated output/completion.
            input_cost_per_million: The cost in USD for 1 million input tokens for the model.
            output_cost_per_million: The cost in USD for 1 million output tokens for the model.
        Returns:
            The total number of RUH credits to be deducted for the operation.
        """
        # --- Step 1: Calculate the cost in US Dollars ---
        input_cost_usd = (Decimal(input_tokens)) * Decimal(str(input_price_per_token))
        output_cost_usd = (Decimal(output_tokens)) * Decimal(str(output_price_per_token))
        total_cost_usd = input_cost_usd + output_cost_usd

        # --- Step 2: Convert the dollar cost to RUH credits ---
        total_credits = total_cost_usd / TokenConverter.DOLLAR_PER_RUH_CREDIT
        return total_credits , total_cost_usd


class PaymentService(payment_pb2_grpc.PaymentServiceServicer):
    """The core service for all payment operations."""

    def __init__(self):
        self.webhook_secret = settings.STRIPE_WEBHOOK_SECRET

    def _get_db_session(self) -> Session:
        """Get a new database session for each request."""
        return SessionLocal()

    def has_processed_event(self, event_id: UUID, db: Session) -> bool:
        """Checks if a webhook event has already been processed."""
        return db.query(PaymentWebhookEvent).filter_by(id=str(event_id)).first() is not None

    def deduct_credits_for_token_usage(self, event: TokenUsageEvent, db: Session):
        """
        Deducts credits from an organization based on a TokenUsageEvent from Kafka.
        This method mirrors the logic of the gRPC DeductCredits method.
        """
        # --- Step 1: Use Consumed Credits Directly from the event ---
        credits_to_deduct = Decimal(str(event.consumed_credits))

        logger.info(
            "Kafka: DeductCredits",
            org_id=event.organisation_id,
            user_id=event.user_id,
            input_tokens=event.input_tokens,
            output_tokens=event.output_tokens,
            credits_to_deduct=credits_to_deduct,
        )

        # --- Step 2: Check & Update Organisation's Credit Balance ---
        subscription = db.query(PaymentSubscription).filter_by(organisation_id=event.organisation_id).first()
        if not subscription:
            logger.error("Subscription not found for organisation", org_id=event.organisation_id)
            raise ValueError("Subscription not found")

        current_balance = subscription.current_credits
        if current_balance < credits_to_deduct:
            logger.warn(
                "Insufficient credits for deduction",
                org_id=event.organisation_id,
                needed=credits_to_deduct,
                has=current_balance,
            )
            raise ValueError("Insufficient credits")

        subscription.current_credits -= credits_to_deduct
        new_balance = subscription.current_credits

        # --- Step 3: Log the Granular Credit Deduction ---
        credit_deduction = CreditDeduction(
            organisation_id=event.organisation_id,
            user_id=event.user_id,
            agent_id=event.agent_id,
            credits_deducted=credits_to_deduct,
            remaining_credits=new_balance,
        )
        db.add(credit_deduction)

        # --- Step 4: Aggregate Daily Token Usage ---
        today = datetime.utcnow().date()
        token_usage_log = (
            db.query(TokenUsageLog)
            .filter_by(
                user_id=event.user_id,
                organisation_id=event.organisation_id,
                date=today,
            )
            .first()
        )

        if token_usage_log:
            token_usage_log.input_tokens += event.input_tokens
            token_usage_log.output_tokens += event.output_tokens
            token_usage_log.total_credits += credits_to_deduct
        else:
            token_usage_log = TokenUsageLog(
                user_id=event.user_id,
                organisation_id=event.organisation_id,
                date=today,
                input_tokens=event.input_tokens,
                output_tokens=event.output_tokens,
                total_credits=credits_to_deduct,
            )
            db.add(token_usage_log)

        # --- Step 5: Log the event to prevent reprocessing ---
        # webhook_event = PaymentWebhookEvent(id=str(event.event_id), event_type="token_usage", event_data=event.model_dump_json(), processed=True)
        # db.add(webhook_event)

        # --- Step 6: Commit all changes atomically ---
        db.commit()
        logger.info(
            "Successfully processed credit deduction from Kafka event",
            org_id=event.organisation_id,
            new_balance=new_balance,
        )

    async def ActivateDefaultPlan(self, request: payment_pb2.ActivateDefaultPlanRequest, context):
        logger.info("RPC: ActivateDefaultPlan", org_id=request.organisation_id)
        print(f"RPC: ActivateDefaultPlan org_id={request.organisation_id}")

        db = self._get_db_session()
        try:
            default_plan = db.query(PaymentPlan).filter(PaymentPlan.is_default == True).first()
            if not default_plan:
                logger.error("No default plan is configured in the database.")
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("No default plan configured.")
                return payment_pb2.SubscriptionDetails()

            # Check if a subscription already exists
            existing_subscription = db.query(PaymentSubscription).filter_by(organisation_id=request.organisation_id).first()
            if existing_subscription:
                logger.warn("Subscription already exists for this organisation", org_id=request.organisation_id)
                sub_details = db.query(PaymentSubscription).filter_by(id=existing_subscription.id).first()
                plan = db.query(PaymentPlan).filter_by(id=sub_details.plan_id).first()
            else:
                customer = db.query(PaymentCustomer).filter_by(organisation_id=request.organisation_id).first()
                if not customer:
                    if default_plan.price > 0:
                        try:
                            stripe_customer = stripe.Customer.create(
                                description=f"Organisation: {request.organisation_id}",
                                metadata={"organisation_id": request.organisation_id},
                            )
                            logger.info("Created Stripe Customer", stripe_customer_id=stripe_customer.id)
                            customer = PaymentCustomer(
                                organisation_id=request.organisation_id,
                                stripe_customer_id=stripe_customer.id,
                            )
                            db.add(customer)
                        except stripe.error.StripeError as e:
                            logger.error("Stripe error creating customer", error=str(e))
                            context.set_code(grpc.StatusCode.INTERNAL)
                            context.set_details(f"Stripe error: {str(e)}")
                            return payment_pb2.SubscriptionDetails()
                    else:
                        # For free plan, create a dummy customer record (optional)
                        customer = PaymentCustomer(
                            organisation_id=request.organisation_id,
                            stripe_customer_id=f"free_{request.organisation_id}",
                        )
                        db.add(customer)

                subscription = PaymentSubscription(
                    organisation_id=request.organisation_id,
                    plan_id=default_plan.id,
                    stripe_subscription_id=f"free_{request.organisation_id}",  # No real Stripe subscription for free plans
                    status=PaymentSubscriptionStatus.ACTIVE,
                    current_period_start=datetime.utcnow(),
                    current_period_end=None,  # Or a far-future date
                    subscription_credits=default_plan.credit_amount,
                    current_credits=default_plan.credit_amount,
                )
                db.add(subscription)
                db.commit()
                db.refresh(subscription)
                logger.info("Successfully created free subscription", org_id=request.organisation_id, plan=default_plan.name)
                sub_details = subscription
                plan = default_plan

            from google.protobuf.timestamp_pb2 import Timestamp
            start_ts = Timestamp()
            if sub_details.current_period_start:
                start_ts.FromDatetime(sub_details.current_period_start)

            end_ts = Timestamp()
            if sub_details.current_period_end:
                end_ts.FromDatetime(sub_details.current_period_end)

            return payment_pb2.SubscriptionDetails(
                id=str(sub_details.id),
                organisation_id=sub_details.organisation_id,
                plan_id_code=plan.plan_id_code,
                status=sub_details.status.value.upper(),
                current_period_start=start_ts,
                current_period_end=end_ts,
                subscription_credits=float(sub_details.subscription_credits),
                current_credits=float(sub_details.current_credits),
            )
        except Exception as e:
            logger.error("Error in ActivateDefaultPlan", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.SubscriptionDetails()
        finally:
            db.close()

    async def DeductCredits(self, request: payment_pb2.DeductCreditsRequest, context):
        # --- Step 1: Use Consumed Credits Directly ---
        credits_to_deduct = Decimal(str(request.consumed_credits))

        logger.info(
            "RPC: DeductCredits",
            org_id=request.organisation_id,
            user_id=request.user_id,
            input_tokens=request.input_tokens,
            output_tokens=request.output_tokens,
            credits_to_deduct=credits_to_deduct,
        )

        db = self._get_db_session()
        try:
            # --- Step 2: Check & Update Organisation's Credit Balance ---
            subscription = db.query(PaymentSubscription).filter_by(organisation_id=request.organisation_id).first()
            if not subscription:
                return payment_pb2.DeductCreditsResponse(
                    success=False,
                    error_message="Subscription not found.",
                    new_balance_after_deduction=0,
                )

            current_balance = subscription.current_credits

            if current_balance < credits_to_deduct:
                logger.warn(
                    "Insufficient credits for deduction",
                    org_id=request.organisation_id,
                    needed=credits_to_deduct,
                    has=current_balance,
                )
                return payment_pb2.DeductCreditsResponse(
                    success=False,
                    error_message="insufficient_credits",
                    new_balance_after_deduction=float(current_balance),
                )

            subscription.current_credits -= credits_to_deduct
            new_balance = subscription.current_credits

            # --- Step 3: Log the Granular Credit Deduction ---
            credit_deduction = CreditDeduction(
                organisation_id=request.organisation_id,
                user_id=request.user_id,
                agent_id=request.agent_id,
                credits_deducted=credits_to_deduct,
                remaining_credits=new_balance,
            )
            db.add(credit_deduction)

            # --- Step 4: Aggregate Daily Token Usage ---
            today = datetime.utcnow().date()
            token_usage_log = (
                db.query(TokenUsageLog)
                .filter_by(
                    user_id=request.user_id,
                    organisation_id=request.organisation_id,
                    date=today,
                )
                .first()
            )

            if token_usage_log:
                # Update existing log for the day
                token_usage_log.input_tokens += request.input_tokens
                token_usage_log.output_tokens += request.output_tokens
                token_usage_log.total_credits += credits_to_deduct
                logger.debug(
                    "Updating existing token usage log for today",
                    user_id=request.user_id,
                    date=today,
                )
            else:
                # Create a new log for the day
                token_usage_log = TokenUsageLog(
                    user_id=request.user_id,
                    organisation_id=request.organisation_id,
                    date=today,
                    input_tokens=request.input_tokens,
                    output_tokens=request.output_tokens,
                    total_credits=credits_to_deduct,
                )
                db.add(token_usage_log)
                logger.debug(
                    "Creating new token usage log for today",
                    user_id=request.user_id,
                    date=today,
                )

            # --- Step 5: Commit and Respond ---
            db.commit()

            logger.info(
                "Successfully processed credit deduction and token logs",
                org_id=request.organisation_id,
                new_balance=new_balance,
            )
            return payment_pb2.DeductCreditsResponse(
                success=True, new_balance_after_deduction=float(new_balance)
            )
        except Exception as e:
            logger.error("Error in DeductCredits", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.DeductCreditsResponse(
                success=False,
                error_message="Internal server error",
                new_balance_after_deduction=0,
            )
        finally:
            db.close()

    async def GetTokenUsage(self, request: payment_pb2.GetTokenUsageRequest, context):
        db = self._get_db_session()
        try:
            query = db.query(TokenUsageLog).filter(
                TokenUsageLog.organisation_id == request.organisation_id
            )
            if request.user_id:
                query = query.filter(TokenUsageLog.user_id == request.user_id)



            # agent_id is no longer in TokenUsageLog, so we cannot filter by it here.
            # If filtering by agent is still needed, it would have to be done
            # by joining with the credit_deductions table.
            # For now, removing the filter.
            # if request.agent_id:
            #     query = query.filter(TokenUsageLog.agent_id == request.agent_id)

            if request.start_date.seconds > 0:
                query = query.filter(TokenUsageLog.date >= request.start_date.ToDatetime().date())
            if request.end_date.seconds > 0:
                query = query.filter(TokenUsageLog.date <= request.end_date.ToDatetime().date())

            logs = query.all()
            log_entries = [
                payment_pb2.TokenUsageLogEntry(
                    # Note: The gRPC message might need to be updated to reflect the new schema
                    id=str(log.id),
                    organisation_id=log.organisation_id,
                    user_id=log.user_id,
                    input_tokens=log.input_tokens,
                    output_tokens=log.output_tokens,
                    total_cost=float(log.total_cost),
                    total_credits=float(log.total_credits),
                    date=log.date.isoformat(),
                )
                for log in logs
            ]
            return payment_pb2.GetTokenUsageResponse(token_usage_logs=log_entries, success=True)
        except Exception as e:
            logger.error("Error in GetTokenUsage", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.GetTokenUsageResponse(token_usage_logs=[], success=False)
        finally:
            db.close()

    async def CreateCheckoutSession(self, request: payment_pb2.CreateCheckoutSessionRequest, context):
        logger.info("RPC: CreateCheckoutSession", org_id=request.organisation_id, plan=request.plan_id_code)
        db = self._get_db_session()
        try:
            plan = db.query(PaymentPlan).filter(PaymentPlan.plan_id_code == request.plan_id_code).first()
            if not plan or not plan.stripe_price_id:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Plan or Stripe price ID not found.")
                return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message="Plan or Stripe price ID not found.")

            # Get or create customer
            customer = db.query(PaymentCustomer).filter_by(organisation_id=request.organisation_id).first()
            if not customer:
                # Create customer in Stripe and database
                logger.info("No customer found, creating new Stripe customer", org_id=request.organisation_id)
                try:
                    stripe_customer = stripe.Customer.create(
                        description=f"Organisation: {request.organisation_id}",
                        metadata={"organisation_id": request.organisation_id}
                    )
                    customer = PaymentCustomer(
                        organisation_id=request.organisation_id,
                        stripe_customer_id=stripe_customer.id,
                    )
                    db.add(customer)
                    db.commit()
                    db.refresh(customer)
                    logger.info("Created new Stripe customer for plan checkout",
                               org_id=request.organisation_id,
                               customer_id=stripe_customer.id)
                except stripe.error.StripeError as e:
                    logger.error("Failed to create Stripe customer", error=str(e), org_id=request.organisation_id)
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Failed to create Stripe customer: {str(e)}")
                    return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message=f"Failed to create customer: {str(e)}")
            else:
                # Check if this is a real Stripe customer or a fake one (for free plans)
                stripe_customer_id = customer.stripe_customer_id
                if stripe_customer_id.startswith("free_"):
                    # This is a fake customer ID for free plan users, create a real Stripe customer
                    logger.info("Converting fake customer to real Stripe customer",
                               org_id=request.organisation_id,
                               fake_customer_id=stripe_customer_id)
                    try:
                        stripe_customer = stripe.Customer.create(
                            description=f"Organisation: {request.organisation_id}",
                            metadata={"organisation_id": request.organisation_id},
                        )

                        # Update the customer record with real Stripe customer ID
                        customer.stripe_customer_id = stripe_customer.id
                        db.commit()
                        db.refresh(customer)

                        logger.info("Successfully converted to real Stripe customer",
                                   org_id=request.organisation_id,
                                   old_customer_id=stripe_customer_id,
                                   new_customer_id=stripe_customer.id)

                    except stripe.error.StripeError as e:
                        logger.error("Failed to create real Stripe customer", error=str(e), org_id=request.organisation_id)
                        context.set_code(grpc.StatusCode.INTERNAL)
                        context.set_details(f"Failed to create Stripe customer: {str(e)}")
                        return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message=f"Failed to create customer: {str(e)}")
                else:
                    # Verify the existing Stripe customer is valid
                    try:
                        stripe.Customer.retrieve(stripe_customer_id)
                        logger.info("Using existing valid Stripe customer",
                                   org_id=request.organisation_id,
                                   customer_id=stripe_customer_id)
                    except stripe.error.InvalidRequestError:
                        # Customer doesn't exist in Stripe, create a new one
                        logger.warning("Stripe customer not found, creating replacement",
                                     org_id=request.organisation_id,
                                     old_customer_id=stripe_customer_id)
                        try:
                            stripe_customer = stripe.Customer.create(
                                description=f"Organisation: {request.organisation_id}",
                                metadata={"organisation_id": request.organisation_id},
                            )

                            # Update the customer record with new Stripe customer ID
                            customer.stripe_customer_id = stripe_customer.id
                            db.commit()
                            db.refresh(customer)

                            logger.info("Successfully created replacement Stripe customer",
                                       org_id=request.organisation_id,
                                       old_customer_id=stripe_customer_id,
                                       new_customer_id=stripe_customer.id)

                        except stripe.error.StripeError as e:
                            logger.error("Failed to create replacement Stripe customer", error=str(e), org_id=request.organisation_id)
                            context.set_code(grpc.StatusCode.INTERNAL)
                            context.set_details(f"Failed to create Stripe customer: {str(e)}")
                            return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message=f"Failed to create customer: {str(e)}")

            try:
                session = stripe.checkout.Session.create(
                    customer=customer.stripe_customer_id,
                    payment_method_types=['card'],
                    line_items=[{'price': plan.stripe_price_id, 'quantity': 1}],
                    mode='subscription',
                    success_url=request.success_url,
                    cancel_url=request.cancel_url,
                    metadata={
                        'organisation_id': request.organisation_id,
                        'plan_id': str(plan.id)
                    }
                )
                return payment_pb2.CreateCheckoutSessionResponse(success=True, session_id=session.id, checkout_url=session.url)
            except stripe.error.StripeError as e:
                logger.error("Stripe error creating checkout session", error=str(e), org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Stripe error: {str(e)}")
                return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message=str(e))
        except Exception as e:
            logger.error("Error in CreateCheckoutSession", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.CreateCheckoutSessionResponse(success=False, error_message="Internal server error")
        finally:
            db.close()

    async def CreateCustomerPortalSession(self, request: payment_pb2.CreateCustomerPortalSessionRequest, context):
        logger.info("RPC: CreateCustomerPortalSession", org_id=request.organisation_id)
        db = self._get_db_session()
        try:
            customer = db.query(PaymentCustomer).filter_by(organisation_id=request.organisation_id).first()
            if not customer:
                return payment_pb2.CreateCustomerPortalSessionResponse(success=False, error_message="Customer not found.")
            try:
                portal_session = stripe.billing_portal.Session.create(
                    customer=customer.stripe_customer_id,
                    return_url=request.return_url,
                )
                return payment_pb2.CreateCustomerPortalSessionResponse(success=True, portal_url=portal_session.url)
            except stripe.error.StripeError as e:
                logger.error("Stripe error creating customer portal session", error=str(e))
                return payment_pb2.CreateCustomerPortalSessionResponse(success=False, error_message=str(e))
        except Exception as e:
            logger.error("Error in CreateCustomerPortalSession", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.CreateCustomerPortalSessionResponse(success=False, error_message="Internal server error")
        finally:
            db.close()

    async def HandleStripeWebhook(self, request: payment_pb2.StripeWebhookRequest, context):
        try:
            # The payload from gRPC is now bytes, which is what construct_event expects.
            # logger.info("Request receive:", payload=request.payload, signature=request.signature)
            event = stripe.Webhook.construct_event(
                payload=request.payload, sig_header=request.signature, secret=self.webhook_secret
            )

            # Log the event type for debugging
            logger.info("Webhook event received", event_type=event.type, event_id=event.id)

        except (ValueError, stripe.error.SignatureVerificationError) as e:
            logger.error("Webhook signature verification failed", error=str(e))
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(f"Webhook signature verification failed: {e}")
            return payment_pb2.GenericResponse(success=False, error_message=str(e))

        # Use a nested session for webhook processing to ensure atomicity
        with SessionLocal() as db_session:
            if db_session.query(PaymentWebhookEvent).filter_by(id=event.id).first():
                logger.info("Event already processed", event_id=event.id, event_type=event.type)
                return payment_pb2.GenericResponse(success=True)

            # Store the raw event data as a string
            webhook_event = PaymentWebhookEvent(id=event.id, event_type=event.type, event_data=json.dumps(event.to_dict()))
            db_session.add(webhook_event)

            try:
                handler_method_name = f"handle_{event.type.replace('.', '_')}"
                handler = getattr(self, handler_method_name, self.handle_unhandled_event)

                logger.info("Processing webhook event",
                           event_type=event.type,
                           event_id=event.id,
                           handler_method=handler_method_name)

                # Pass the db_session to the handler
                await handler(event.data.object, db_session)
                webhook_event.processed = True

                logger.info("Successfully processed webhook event",
                           event_type=event.type,
                           event_id=event.id)

            except Exception as e:
                logger.error("Error processing webhook:",
                           error=str(e),
                           event_type=event.type,
                           event_id=event.id,
                           exc_info=True)
                webhook_event.error_message = str(e)
                db_session.rollback()
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Error processing webhook: {e}")
                return payment_pb2.GenericResponse(success=False, error_message=str(e))

            db_session.commit()

        return payment_pb2.GenericResponse(success=True)

    async def GetSubscription(self, request: payment_pb2.GetSubscriptionRequest, context):
        print(f"Debug - request: {request}")
        db = self._get_db_session()
        try:
            sub = db.query(PaymentSubscription).filter_by(organisation_id=request.organisation_id).first()
            print(f"Debug - sub: {sub}")
            if not sub:
                return payment_pb2.GetSubscriptionResponse(success=False, error_message="Subscription not found.")

            plan = db.query(PaymentPlan).filter_by(id=sub.plan_id).first()
            print(f"Debug - plan: {plan}")
            from google.protobuf.timestamp_pb2 import Timestamp
            start_ts = Timestamp()
            if sub.current_period_start:
                start_ts.FromDatetime(sub.current_period_start)

            end_ts = Timestamp()
            if sub.current_period_end:
                end_ts.FromDatetime(sub.current_period_end)

            details = payment_pb2.SubscriptionDetails(
                id=str(sub.id),
                organisation_id=sub.organisation_id,
                plan_id_code=plan.plan_id_code,
                status=sub.status.value.upper(),
                current_period_start=start_ts,
                current_period_end=end_ts,
                subscription_credits=float(sub.subscription_credits),
                current_credits=float(sub.current_credits),
            )
            print(f"Debug - details: {details}")
            return payment_pb2.GetSubscriptionResponse(success=True, subscription=details)
        except Exception as e:
            logger.error("Error in GetSubscription", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.GetSubscriptionResponse(success=False, error_message="Internal server error")
        finally:
            db.close()

    async def CancelSubscription(self, request: payment_pb2.CancelSubscriptionRequest, context):
        db = self._get_db_session()
        try:
            sub = db.query(PaymentSubscription).filter_by(organisation_id=request.organisation_id).first()
            if not sub:
                return payment_pb2.GenericResponse(success=False, error_message="Subscription not found.")
            try:
                stripe.Subscription.delete(sub.stripe_subscription_id)
                sub.status = PaymentSubscriptionStatus.CANCELED
                db.commit()
                return payment_pb2.GenericResponse(success=True)
            except stripe.error.StripeError as e:
                logger.error("Stripe error canceling subscription", error=str(e))
                return payment_pb2.GenericResponse(success=False, error_message=str(e))
        except Exception as e:
            logger.error("Error in CancelSubscription", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.GenericResponse(success=False, error_message="Internal server error")
        finally:
            db.close()

    async def GetCreditBalance(self, request: payment_pb2.GetCreditBalanceRequest, context):
        db = self._get_db_session()
        try:
            subscription = db.query(PaymentSubscription).filter_by(organisation_id=request.organisation_id).first()
            if not subscription:
                return payment_pb2.GetCreditBalanceResponse(success=False, error_message="Subscription not found.")
            return payment_pb2.GetCreditBalanceResponse(success=True, credit_balance=float(subscription.current_credits))
        except Exception as e:
            logger.error("Error in GetCreditBalance", error=str(e), org_id=request.organisation_id)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.GetCreditBalanceResponse(success=False, error_message="Internal server error")
        finally:
            db.close()

    async def handle_checkout_session_completed(self, session: Dict[str, Any], db_session: Session):
        org_id = session.get("metadata", {}).get("organisation_id")
        session_type = session.get("metadata", {}).get("type", "subscription")  # Default to subscription for backward compatibility
        stripe_customer_id = session.get("customer")

        if not org_id:
            logger.error("Webhook 'checkout.session.completed' missing organisation_id in metadata.",
                         metadata=session.get("metadata"))
            return

        # Handle topup purchases
        if session_type == "topup_purchase":
            from app.services.topup_service import get_topup_service
            topup_service = get_topup_service()
            await topup_service.handle_topup_purchase(session, db_session)
            return

        # Handle subscription plans (existing logic)
        plan_id = session.get("metadata", {}).get("plan_id")
        stripe_sub_id = session.get("subscription")

        if not stripe_sub_id or not plan_id:
            logger.error("Webhook 'checkout.session.completed' missing required metadata for subscription.",
                         metadata=session.get("metadata"))
            return

        logger.info(
            "Processing checkout.session.completed webhook",
            event_id=session.get("id"),
            org_id=org_id,
            plan_id=plan_id,
            stripe_sub_id=stripe_sub_id,
            stripe_customer_id=stripe_customer_id
        )

        try:
            sub_details = stripe.Subscription.retrieve(stripe_sub_id)
            plan = db_session.query(PaymentPlan).filter_by(id=plan_id).first()
            if not plan:
                logger.error("Plan not found in DB from webhook.", plan_id=plan_id)
                return

            # Check for an existing subscription for the organization
            subscription = db_session.query(PaymentSubscription).filter_by(organisation_id=org_id).first()

            if subscription:
                # Upgrading or changing plan
                logger.info("Upgrading existing subscription.", org_id=org_id, new_plan_id=plan.id)
                # Add new credits to existing balance
                subscription.current_credits += plan.credit_amount
                subscription.plan_id = plan.id
                subscription.stripe_subscription_id = stripe_sub_id
                subscription.status = PaymentSubscriptionStatus.ACTIVE
                subscription.current_period_start = datetime.fromtimestamp(sub_details.current_period_start)
                subscription.current_period_end = datetime.fromtimestamp(sub_details.current_period_end)
                subscription.subscription_credits = plan.credit_amount # Update to new plan's credits
            else:
                # New subscription
                logger.info("Creating new subscription.", org_id=org_id, plan_id=plan.id)
                subscription = PaymentSubscription(
                    organisation_id=org_id,
                    plan_id=plan.id,
                    stripe_subscription_id=stripe_sub_id,
                    status=PaymentSubscriptionStatus.ACTIVE,
                    current_period_start=datetime.fromtimestamp(sub_details.current_period_start),
                    current_period_end=datetime.fromtimestamp(sub_details.current_period_end),
                    subscription_credits=plan.credit_amount,
                    current_credits=plan.credit_amount, # Start with the plan's credits
                )
                db_session.add(subscription)

            # Update invoice information if available
            invoice_id = session.get("invoice")
            if invoice_id:
                try:
                    # Retrieve the invoice to get the hosted URL
                    invoice = stripe.Invoice.retrieve(invoice_id)
                    subscription.latest_invoice_id = invoice_id
                    subscription.invoice_url = invoice.hosted_invoice_url
                    logger.info("Updated subscription with invoice URL",
                               org_id=org_id,
                               invoice_id=invoice_id,
                               invoice_url=invoice.hosted_invoice_url)
                except stripe.error.StripeError as e:
                    logger.warning("Could not retrieve invoice details",
                                 invoice_id=invoice_id,
                                 error=str(e))

            # Ensure customer ID is up-to-date
            customer = db_session.query(PaymentCustomer).filter_by(organisation_id=org_id).first()
            if customer and customer.stripe_customer_id != stripe_customer_id:
                logger.info("Updating Stripe customer ID in DB.", org_id=org_id)
                customer.stripe_customer_id = stripe_customer_id

            db_session.commit() # Commit subscription changes first to get subscription.id
            db_session.refresh(subscription)
            logger.info(
                "Subscription (creation/update) committed to DB",
                subscription_id=str(subscription.id),
                organisation_id=subscription.organisation_id,
                current_credits=float(subscription.current_credits)
            )

            # Get additional transaction details
            stripe_charge_id = None
            invoice_url = None

            try:
                # Get payment intent and charge details
                payment_intent_id = session.get("payment_intent")
                if payment_intent_id:
                    payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
                    if payment_intent.charges and payment_intent.charges.data:
                        stripe_charge_id = payment_intent.charges.data[0].id

                # Get invoice URL if available
                invoice_id = session.get("invoice")
                if invoice_id:
                    invoice = stripe.Invoice.retrieve(invoice_id)
                    invoice_url = invoice.hosted_invoice_url

                logger.info("Retrieved payment details for plan purchase",
                           payment_intent_id=payment_intent_id,
                           stripe_charge_id=stripe_charge_id,
                           invoice_id=invoice_id,
                           invoice_url=invoice_url)

            except stripe.error.StripeError as e:
                logger.warning("Could not retrieve payment details", error=str(e))

            transaction = PaymentTransaction(
                organisation_id=org_id,
                transaction_type=PaymentTransactionType.PLAN_PURCHASE,
                status=PaymentTransactionStatus.COMPLETED,
                amount_currency=session.get("amount_total", 0), # amount_total is in cents
                currency=session.get("currency", "usd"),
                description=f"Purchase of {plan.name} plan.",
                stripe_charge_id=stripe_charge_id,
                stripe_invoice_id=session.get("invoice"),
                invoice_url=invoice_url,
                subscription_id=subscription.id
            )
            db_session.add(transaction)
            logger.info("Successfully processed checkout session.", org_id=org_id, subscription_id=subscription.id)

        except stripe.error.StripeError as e:
            logger.error("Stripe API error in webhook handler", error=str(e))
        except Exception as e:
            logger.error("Generic error in webhook handler", error=str(e))

    async def handle_invoice_paid(self, invoice: Dict[str, Any], db_session: Session):
        stripe_sub_id = invoice.get("subscription")
        if not stripe_sub_id:
            logger.warning("Invoice paid without subscription ID", invoice_id=invoice.get("id"))
            return

        sub = db_session.query(PaymentSubscription).filter_by(stripe_subscription_id=stripe_sub_id).first()
        if not sub:
            logger.warning("Subscription not found for invoice.paid event", stripe_sub_id=stripe_sub_id)
            return

        try:
            sub_details = stripe.Subscription.retrieve(stripe_sub_id)
            sub.current_period_start = datetime.fromtimestamp(sub_details.current_period_start)
            sub.current_period_end = datetime.fromtimestamp(sub_details.current_period_end)
            sub.status = PaymentSubscriptionStatus.ACTIVE

            # Update invoice information
            sub.latest_invoice_id = invoice.get("id")
            sub.invoice_url = invoice.get("hosted_invoice_url")

            plan = db_session.query(PaymentPlan).filter_by(id=sub.plan_id).first()

            transaction = PaymentTransaction(
                organisation_id=sub.organisation_id,
                transaction_type=PaymentTransactionType.PLAN_PURCHASE,
                status=PaymentTransactionStatus.COMPLETED,
                amount_currency=invoice.get("amount_paid", 0),
                currency=invoice.get("currency", "usd"),
                description=f"Monthly renewal for {plan.name} plan.",
                stripe_charge_id=invoice.get("charge"),  # Invoice has charge ID
                stripe_invoice_id=invoice.get("id"),
                invoice_url=invoice.get("hosted_invoice_url"),
                subscription_id=sub.id
            )
            db_session.add(transaction)
            logger.info("Successfully processed invoice.paid event",
                       org_id=sub.organisation_id,
                       invoice_id=invoice.get("id"),
                       invoice_url=invoice.get("hosted_invoice_url"))
        except Exception as e:
            logger.error("Error processing invoice.paid event", error=str(e), stripe_sub_id=stripe_sub_id)

    async def handle_customer_subscription_deleted(self, subscription: Dict[str, Any], db_session: Session):
        stripe_sub_id = subscription.get("id")
        sub = db_session.query(PaymentSubscription).filter_by(stripe_subscription_id=stripe_sub_id).first()
        if sub:
            # Revert to free plan instead of just marking as canceled
            await self._revert_to_free_plan(sub, db_session)
            logger.info("Successfully processed customer.subscription.deleted event - reverted to free plan",
                       org_id=sub.organisation_id,
                       stripe_sub_id=stripe_sub_id)
        else:
            logger.warning("Subscription not found for customer.subscription.deleted event", stripe_sub_id=stripe_sub_id)



    async def handle_unhandled_event(self, data, db_session):
        logger.info("Unhandled webhook event type", event_data=data)

    async def _revert_to_free_plan(self, subscription: PaymentSubscription, db_session: Session):
        """
        Revert a failed subscription back to the free/default plan.
        This cancels the paid subscription and restores the free plan benefits.
        """
        try:
            # Get the default/free plan
            free_plan = db_session.query(PaymentPlan).filter_by(is_default=True).first()
            if not free_plan:
                logger.error("No default/free plan found to revert to",
                           org_id=subscription.organisation_id)
                # Fallback: just cancel the subscription
                subscription.status = PaymentSubscriptionStatus.CANCELED
                return

            # Store original plan info for logging
            original_plan = db_session.query(PaymentPlan).filter_by(id=subscription.plan_id).first()
            original_plan_name = original_plan.name if original_plan else "Unknown"

            # Cancel the Stripe subscription to stop future billing attempts
            try:
                if subscription.stripe_subscription_id:
                    stripe.Subscription.modify(
                        subscription.stripe_subscription_id,
                        cancel_at_period_end=True
                    )
                    logger.info("Cancelled Stripe subscription",
                               stripe_sub_id=subscription.stripe_subscription_id)
            except stripe.error.StripeError as e:
                logger.warning("Could not cancel Stripe subscription",
                             stripe_sub_id=subscription.stripe_subscription_id,
                             error=str(e))

            # Revert to free plan
            subscription.plan_id = free_plan.id
            subscription.status = PaymentSubscriptionStatus.ACTIVE
            subscription.stripe_subscription_id = None  # Remove Stripe subscription link
            subscription.subscription_credits = free_plan.credit_amount
            subscription.current_credits = free_plan.credit_amount  # Reset to free plan credits
            subscription.current_period_start = None
            subscription.current_period_end = None
            subscription.invoice_url = None
            subscription.latest_invoice_id = None

            logger.info("Successfully reverted subscription to free plan",
                       org_id=subscription.organisation_id,
                       original_plan=original_plan_name,
                       free_plan=free_plan.name,
                       new_credits=free_plan.credit_amount)

        except Exception as e:
            logger.error("Error reverting subscription to free plan",
                        error=str(e),
                        org_id=subscription.organisation_id)
            # Fallback: just cancel the subscription if revert fails
            subscription.status = PaymentSubscriptionStatus.CANCELED

    async def handle_invoice_payment_failed(self, invoice: Dict[str, Any], db_session: Session):
        """Handle invoice.payment_failed webhook event."""
        try:
            stripe_sub_id = invoice.get("subscription")
            if not stripe_sub_id:
                logger.warning("Invoice payment failed without subscription ID", invoice_id=invoice.get("id"))
                return

            # Find the subscription in our database
            subscription = db_session.query(PaymentSubscription).filter_by(
                stripe_subscription_id=stripe_sub_id
            ).first()

            if not subscription:
                logger.warning("Subscription not found for invoice.payment_failed event",
                             stripe_sub_id=stripe_sub_id,
                             invoice_id=invoice.get("id"))
                return

            # Cancel the failed subscription and revert to free plan
            await self._revert_to_free_plan(subscription, db_session)

            # Still save the failed invoice information for reference
            subscription.latest_invoice_id = invoice.get("id")
            subscription.invoice_url = invoice.get("hosted_invoice_url")

            # Create a failed transaction record
            plan = db_session.query(PaymentPlan).filter_by(id=subscription.plan_id).first()
            if plan:
                transaction = PaymentTransaction(
                    organisation_id=subscription.organisation_id,
                    transaction_type=PaymentTransactionType.PLAN_PURCHASE,
                    status=PaymentTransactionStatus.FAILED,
                    amount_currency=invoice.get("amount_due", 0),
                    currency=invoice.get("currency", "usd"),
                    description=f"Failed payment for {plan.name} plan.",
                    stripe_charge_id=invoice.get("charge"),  # May be None for failed payments
                    stripe_invoice_id=invoice.get("id"),
                    invoice_url=invoice.get("hosted_invoice_url"),
                    subscription_id=subscription.id
                )
                db_session.add(transaction)

            logger.warning("Successfully processed invoice.payment_failed event",
                          org_id=subscription.organisation_id,
                          invoice_id=invoice.get("id"),
                          new_status=subscription.status.value)

        except Exception as e:
            logger.error("Error processing invoice.payment_failed event",
                        error=str(e),
                        invoice_id=invoice.get("id"))

    async def handle_payment_intent_payment_failed(self, payment_intent: Dict[str, Any], db_session: Session):
        """Handle payment_intent.payment_failed webhook event."""
        try:
            # Get invoice ID from payment intent if available
            invoice_id = payment_intent.get("invoice")
            if not invoice_id:
                logger.info("Payment intent failed without associated invoice",
                           payment_intent_id=payment_intent.get("id"))
                return

            # Retrieve the invoice to get subscription information
            try:
                invoice = stripe.Invoice.retrieve(invoice_id)
                stripe_sub_id = invoice.subscription

                if not stripe_sub_id:
                    logger.warning("Payment intent failed for invoice without subscription",
                                 invoice_id=invoice_id)
                    return

                # Find the subscription in our database
                subscription = db_session.query(PaymentSubscription).filter_by(
                    stripe_subscription_id=stripe_sub_id
                ).first()

                if not subscription:
                    logger.warning("Subscription not found for payment_intent.payment_failed event",
                                 stripe_sub_id=stripe_sub_id,
                                 invoice_id=invoice_id)
                    return

                # Cancel the failed subscription and revert to free plan
                await self._revert_to_free_plan(subscription, db_session)

                # Create a failed transaction record
                plan = db_session.query(PaymentPlan).filter_by(id=subscription.plan_id).first()
                if plan:
                    # Get invoice URL if available
                    invoice_url = None
                    if invoice_id:
                        try:
                            invoice = stripe.Invoice.retrieve(invoice_id)
                            invoice_url = invoice.hosted_invoice_url
                        except stripe.error.StripeError:
                            pass  # Invoice might not exist for failed payments

                    transaction = PaymentTransaction(
                        organisation_id=subscription.organisation_id,
                        transaction_type=PaymentTransactionType.PLAN_PURCHASE,
                        status=PaymentTransactionStatus.FAILED,
                        amount_currency=payment_intent.get("amount", 0),
                        currency=payment_intent.get("currency", "usd"),
                        description=f"Failed payment intent for {plan.name} plan.",
                        stripe_charge_id=None,  # No charge for failed payment intent
                        stripe_invoice_id=invoice_id,
                        invoice_url=invoice_url,
                        subscription_id=subscription.id
                    )
                    db_session.add(transaction)

                logger.warning("Successfully processed payment_intent.payment_failed event",
                              org_id=subscription.organisation_id,
                              payment_intent_id=payment_intent.get("id"),
                              invoice_id=invoice_id)

            except stripe.error.StripeError as e:
                logger.error("Error retrieving invoice for payment_intent.payment_failed event",
                           error=str(e),
                           invoice_id=invoice_id)

        except Exception as e:
            logger.error("Error processing payment_intent.payment_failed event",
                        error=str(e),
                        payment_intent_id=payment_intent.get("id"))

    async def handle_customer_subscription_updated(self, subscription: Dict[str, Any], db_session: Session):
        """Handle customer.subscription.updated webhook event."""
        try:
            stripe_sub_id = subscription.get("id")
            if not stripe_sub_id:
                logger.warning("Subscription updated without ID")
                return

            # Find the subscription in our database
            db_subscription = db_session.query(PaymentSubscription).filter_by(
                stripe_subscription_id=stripe_sub_id
            ).first()

            if not db_subscription:
                logger.warning("Subscription not found for customer.subscription.updated event",
                             stripe_sub_id=stripe_sub_id)
                return

            # Update subscription status based on Stripe status
            stripe_status = subscription.get("status")
            if stripe_status == "past_due":
                # Revert to free plan instead of keeping past_due status
                await self._revert_to_free_plan(db_subscription, db_session)
                logger.info("Reverted past_due subscription to free plan",
                           org_id=db_subscription.organisation_id,
                           stripe_sub_id=stripe_sub_id)
            elif stripe_status == "canceled":
                # Revert to free plan when subscription is canceled
                await self._revert_to_free_plan(db_subscription, db_session)
                logger.info("Reverted canceled subscription to free plan",
                           org_id=db_subscription.organisation_id,
                           stripe_sub_id=stripe_sub_id)
            elif stripe_status == "active":
                db_subscription.status = PaymentSubscriptionStatus.ACTIVE
            elif stripe_status == "incomplete":
                # Revert incomplete subscriptions to free plan as well
                await self._revert_to_free_plan(db_subscription, db_session)
                logger.info("Reverted incomplete subscription to free plan",
                           org_id=db_subscription.organisation_id,
                           stripe_sub_id=stripe_sub_id)
            elif stripe_status == "trialing":
                db_subscription.status = PaymentSubscriptionStatus.TRIALING

            # Update period information
            current_period_start = subscription.get("current_period_start")
            current_period_end = subscription.get("current_period_end")

            if current_period_start:
                db_subscription.current_period_start = datetime.fromtimestamp(current_period_start)
            if current_period_end:
                db_subscription.current_period_end = datetime.fromtimestamp(current_period_end)

            logger.info("Successfully processed customer.subscription.updated event",
                       org_id=db_subscription.organisation_id,
                       stripe_sub_id=stripe_sub_id,
                       new_status=db_subscription.status.value)

        except Exception as e:
            logger.error("Error processing customer.subscription.updated event",
                        error=str(e),
                        stripe_sub_id=subscription.get("id"))

    async def handle_invoice_created(self, invoice: Dict[str, Any], db_session: Session):
        """Handle invoice.created webhook event to save invoice URL."""
        try:
            stripe_sub_id = invoice.get("subscription")
            if not stripe_sub_id:
                logger.warning("Invoice created without subscription ID", invoice_id=invoice.get("id"))
                return

            # Find the subscription in our database
            subscription = db_session.query(PaymentSubscription).filter_by(
                stripe_subscription_id=stripe_sub_id
            ).first()

            if not subscription:
                logger.warning("Subscription not found for invoice.created event",
                             stripe_sub_id=stripe_sub_id,
                             invoice_id=invoice.get("id"))
                return

            # Update subscription with invoice information
            subscription.latest_invoice_id = invoice.get("id")
            subscription.invoice_url = invoice.get("hosted_invoice_url")

            logger.info("Successfully processed invoice.created event",
                       org_id=subscription.organisation_id,
                       invoice_id=invoice.get("id"),
                       invoice_url=invoice.get("hosted_invoice_url"))

        except Exception as e:
            logger.error("Error processing invoice.created event",
                        error=str(e),
                        invoice_id=invoice.get("id"))

    async def handle_invoice_updated(self, invoice: Dict[str, Any], db_session: Session):
        """Handle invoice.updated webhook event to update invoice URL."""
        try:
            stripe_sub_id = invoice.get("subscription")
            if not stripe_sub_id:
                logger.warning("Invoice updated without subscription ID", invoice_id=invoice.get("id"))
                return

            # Find the subscription in our database
            subscription = db_session.query(PaymentSubscription).filter_by(
                stripe_subscription_id=stripe_sub_id
            ).first()

            if not subscription:
                logger.warning("Subscription not found for invoice.updated event",
                             stripe_sub_id=stripe_sub_id,
                             invoice_id=invoice.get("id"))
                return

            # Update subscription with latest invoice information
            subscription.latest_invoice_id = invoice.get("id")
            subscription.invoice_url = invoice.get("hosted_invoice_url")

            logger.info("Successfully processed invoice.updated event",
                       org_id=subscription.organisation_id,
                       invoice_id=invoice.get("id"),
                       invoice_url=invoice.get("hosted_invoice_url"))

        except Exception as e:
            logger.error("Error processing invoice.updated event",
                        error=str(e),
                        invoice_id=invoice.get("id"))

    async def handle_invoice_finalized(self, invoice: Dict[str, Any], db_session: Session):
        """Handle invoice.finalized webhook event to update invoice URL."""
        try:
            stripe_sub_id = invoice.get("subscription")
            if not stripe_sub_id:
                logger.warning("Invoice finalized without subscription ID", invoice_id=invoice.get("id"))
                return

            # Find the subscription in our database
            subscription = db_session.query(PaymentSubscription).filter_by(
                stripe_subscription_id=stripe_sub_id
            ).first()

            if not subscription:
                logger.warning("Subscription not found for invoice.finalized event",
                             stripe_sub_id=stripe_sub_id,
                             invoice_id=invoice.get("id"))
                return

            # Update subscription with finalized invoice information
            subscription.latest_invoice_id = invoice.get("id")
            subscription.invoice_url = invoice.get("hosted_invoice_url")

            logger.info("Successfully processed invoice.finalized event",
                       org_id=subscription.organisation_id,
                       invoice_id=invoice.get("id"),
                       invoice_url=invoice.get("hosted_invoice_url"))

        except Exception as e:
            logger.error("Error processing invoice.finalized event",
                        error=str(e),
                        invoice_id=invoice.get("id"))

    def CreatePlan(self, request: payment_pb2.CreatePlanRequest, context):
        plan_id_code = request.name.lower().replace(" ", "-")
        logger.info("RPC: CreatePlan", plan_name=request.name, generated_plan_code=plan_id_code)

        db = self._get_db_session()
        try:
            # Check if a plan with the same code already exists
            existing_plan = db.query(PaymentPlan).filter(PaymentPlan.plan_id_code == plan_id_code).first()
            if existing_plan:
                logger.warn("Plan with this ID code already exists", plan_id_code=plan_id_code)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"A plan with code '{plan_id_code}' already exists.")
                return payment_pb2.Plan()

            try:
                # Create a product in Stripe
                product = stripe.Product.create(name=request.name)

                # Create a price in Stripe
                price = stripe.Price.create(
                    product=product.id,
                    unit_amount=int(request.price * 100),  # Stripe expects amount in cents
                    currency="usd",
                    recurring={"interval": "month"},
                )

                db_plan = PaymentPlan(
                    plan_id_code=plan_id_code,
                    name=request.name,
                    credit_amount=request.credit_amount,
                    price=request.price,
                    stripe_price_id=price.id,
                    is_default=request.is_default,
                )
                db.add(db_plan)
                db.commit()
                db.refresh(db_plan)

                logger.info("Successfully created plan in DB and Stripe", plan_id=db_plan.id, stripe_product_id=product.id, stripe_price_id=price.id)

                return self._to_plan_proto(db_plan)
            except stripe.error.StripeError as e:
                logger.error("Stripe error while creating plan", error=str(e))
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Stripe error: {str(e)}")
                return payment_pb2.Plan()
        except Exception as e:
            logger.error("Error creating plan", error=str(e))
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal error occurred.")
            return payment_pb2.Plan()
        finally:
            db.close()


    def ListPlans(self, request: payment_pb2.ListPlansRequest, context):
        logger.info("RPC: ListPlans")
        db = self._get_db_session()
        try:
            plans = db.query(PaymentPlan).all()
            return payment_pb2.ListPlansResponse(plans=[self._to_plan_proto(p) for p in plans])
        except Exception as e:
            logger.error("Error in ListPlans", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return payment_pb2.ListPlansResponse(plans=[])
        finally:
            db.close()


    async def CalculateCredits(self, request: payment_pb2.CalculateCreditsRequest, context):
        """
        Calculate credits based on provided pricing information.
        """
        logger.info(
            "RPC: CalculateCredits",
            input_tokens=request.input_tokens,
            output_tokens=request.output_tokens,
            input_price_per_token=request.input_price_per_token,
            output_price_per_token=request.output_price_per_token,
        )

        try:
            # Calculate credits using the existing TokenConverter logic
            credits, total_cost_usd = TokenConverter.calculate_credits(
                input_tokens=request.input_tokens,
                output_tokens=request.output_tokens,
                input_price_per_token=request.input_price_per_token,
                output_price_per_token=request.output_price_per_token,
            )

            return payment_pb2.CalculateCreditsResponse(
                success=True,
                error_message="",
                credits=float(credits),
                cost_in_usd=float(total_cost_usd),
                input_price_per_token=request.input_price_per_token,
                output_price_per_token=request.output_price_per_token,
            )

        except Exception as e:
            logger.error("Error calculating credits", error=str(e))
            return payment_pb2.CalculateCreditsResponse(
                success=False,
                error_message=f"Internal error: {str(e)}",
                credits=0.0,
                cost_in_usd=0.0,
                input_price_per_token=0.0,
                output_price_per_token=0.0,
            )

    # --- Topup Plan RPC Methods (delegated to TopupService) ---

    async def CreateTopupCheckoutSession(self, request: payment_pb2.CreateTopupCheckoutSessionRequest, context):
        """Delegate to TopupService."""
        from app.services.topup_service import get_topup_service
        topup_service = get_topup_service()
        return await topup_service.create_topup_checkout_session(request, context)

    def ListTopupPlans(self, request: payment_pb2.ListTopupPlansRequest, context):
        """Delegate to TopupService."""
        from app.services.topup_service import get_topup_service
        topup_service = get_topup_service()
        return topup_service.list_topup_plans(request, context)

    def CreateTopupPlan(self, request: payment_pb2.CreateTopupPlanRequest, context):
        """Delegate to TopupService."""
        from app.services.topup_service import get_topup_service
        topup_service = get_topup_service()
        return topup_service.create_topup_plan(request, context)



    def _to_plan_proto(self, plan: PaymentPlan) -> payment_pb2.Plan:
        return payment_pb2.Plan(
            id=plan.id,
            plan_id_code=plan.plan_id_code,
            name=plan.name,
            credit_amount=float(plan.credit_amount),
            price=float(plan.price),
            stripe_price_id=plan.stripe_price_id,
            is_default=plan.is_default,
        )




def get_payment_service() -> PaymentService:
    return PaymentService()