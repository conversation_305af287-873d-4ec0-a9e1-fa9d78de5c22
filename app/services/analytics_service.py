import grpc
import json
import structlog
import os
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_, distinct
from app.db.session import SessionLocal
from app.models.analytics import (
    AnalyticsEvent,
    Application,
    ServiceMetrics,
    UserActivity,
    Webhook,
    WebhookLog,
    ActivationEvent,
    UsageEvent,
    Activity,
    ActivityLog,
    ActivityEvent,
)
from app.grpc import analytics_pb2, analytics_pb2_grpc
from app.utils.constants.constants import (
    EventType,
    AnalyticsStatus,
    ApplicationStatus,
    WebhookStatus,
    WebhookEventType,
    ActivationEventType,
)

logger = structlog.get_logger(__name__)


class AnalyticsService(analytics_pb2_grpc.AnalyticsServiceServicer):
    def __init__(self):
        pass

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def TrackEvent(
        self, request: analytics_pb2.TrackEventRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.TrackEventResponse:
        db = self.get_db()
        try:
            logger.info(
                "track_event_request",
                event_type=analytics_pb2.EventType.Name(request.event_type),
                service_type=analytics_pb2.ServiceType.Name(request.service_type),
                entity_id=request.entity_id,
                user_id=request.user_id,
            )

            # Create new analytics event
            new_event = AnalyticsEvent(
                event_type=analytics_pb2.EventType.Name(request.event_type).lower(),
                service_type=analytics_pb2.ServiceType.Name(request.service_type).lower(),
                entity_id=request.entity_id,
                user_id=request.user_id,
                event_metadata=json.loads(request.metadata) if request.metadata else {},
            )
            db.add(new_event)

            # Update service metrics
            service_metrics = (
                db.query(ServiceMetrics)
                .filter(
                    ServiceMetrics.service_type
                    == analytics_pb2.ServiceType.Name(request.service_type).lower(),
                    ServiceMetrics.entity_id == request.entity_id,
                )
                .first()
            )

            if not service_metrics:
                service_metrics = ServiceMetrics(
                    service_type=analytics_pb2.ServiceType.Name(request.service_type).lower(),
                    entity_id=request.entity_id,
                )
                db.add(service_metrics)

            # Update metrics based on event type
            if request.event_type == analytics_pb2.EventType.EVENT_TYPE_USAGE:
                service_metrics.usage_count += 1
            elif request.event_type == analytics_pb2.EventType.EVENT_TYPE_RATING:
                # Extract rating from metadata
                if request.metadata:
                    event_metadata = json.loads(request.metadata)
                    if "rating" in event_metadata:
                        rating = float(event_metadata["rating"])
                        # Update average rating
                        current_total = (
                            service_metrics.average_rating * service_metrics.rating_count
                        )
                        service_metrics.rating_count += 1
                        service_metrics.average_rating = (
                            current_total + rating
                        ) / service_metrics.rating_count

            # Update user activity
            user_activity = (
                db.query(UserActivity).filter(UserActivity.user_id == request.user_id).first()
            )

            if not user_activity:
                user_activity = UserActivity(user_id=request.user_id)
                db.add(user_activity)

            # Update user activity metrics based on event type and service type
            user_activity.last_activity_date = datetime.now(timezone.utc)

            if request.event_type == analytics_pb2.EventType.EVENT_TYPE_USAGE:
                if request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_MCP:
                    user_activity.mcp_usage_count += 1
                elif request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_WORKFLOW:
                    user_activity.workflow_usage_count += 1
                elif request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_AGENT:
                    user_activity.agent_usage_count += 1

            elif request.event_type == analytics_pb2.EventType.EVENT_TYPE_CREATION:
                if request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_MCP:
                    user_activity.mcp_creation_count += 1
                elif request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_WORKFLOW:
                    user_activity.workflow_creation_count += 1
                elif request.service_type == analytics_pb2.ServiceType.SERVICE_TYPE_AGENT:
                    user_activity.agent_creation_count += 1

            db.commit()
            db.refresh(new_event)

            return analytics_pb2.TrackEventResponse(
                success=True,
                message="Event tracked successfully",
                event_id=new_event.id,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error tracking event: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.TrackEventResponse(
                success=False,
                message=f"Error tracking event: {str(e)}",
                event_id="",
            )

    def GetServiceMetrics(
        self, request: analytics_pb2.GetServiceMetricsRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetServiceMetricsResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_service_metrics_request",
                service_type=analytics_pb2.ServiceType.Name(request.service_type),
                entity_id=request.entity_id,
                time_period_days=request.time_period_days,
            )

            # Get service metrics
            service_metrics = (
                db.query(ServiceMetrics)
                .filter(
                    ServiceMetrics.service_type
                    == analytics_pb2.ServiceType.Name(request.service_type).lower(),
                    ServiceMetrics.entity_id == request.entity_id,
                )
                .first()
            )

            if not service_metrics:
                return analytics_pb2.GetServiceMetricsResponse(
                    success=False,
                    message=f"No metrics found for {analytics_pb2.ServiceType.Name(request.service_type)} with ID {request.entity_id}",
                    metrics=analytics_pb2.ServiceMetrics(
                        entity_id=request.entity_id,
                        service_type=request.service_type,
                        usage_count=0,
                        average_rating=0.0,
                        rating_count=0,
                    ),
                )

            # Get time series data for usage
            time_series_data = []
            if request.time_period_days > 0:
                # Filter events by time period
                start_date = datetime.now(timezone.utc) - timedelta(days=request.time_period_days)

                # Group events by day and count
                usage_events = (
                    db.query(
                        func.date_trunc("day", AnalyticsEvent.created_at).label("day"),
                        func.count().label("count"),
                    )
                    .filter(
                        AnalyticsEvent.service_type
                        == analytics_pb2.ServiceType.Name(request.service_type).lower(),
                        AnalyticsEvent.entity_id == request.entity_id,
                        AnalyticsEvent.event_type == EventType.USAGE.value,
                        AnalyticsEvent.created_at >= start_date,
                    )
                    .group_by(func.date_trunc("day", AnalyticsEvent.created_at))
                    .order_by(func.date_trunc("day", AnalyticsEvent.created_at))
                    .all()
                )

                for day, count in usage_events:
                    time_series_data.append(
                        analytics_pb2.TimeSeriesDataPoint(
                            date=day.isoformat(),
                            count=count,
                        )
                    )

            return analytics_pb2.GetServiceMetricsResponse(
                success=True,
                message="Service metrics retrieved successfully",
                metrics=analytics_pb2.ServiceMetrics(
                    entity_id=service_metrics.entity_id,
                    service_type=getattr(
                        analytics_pb2.ServiceType,
                        f"SERVICE_TYPE_{service_metrics.service_type.upper()}",
                    ),
                    usage_count=service_metrics.usage_count,
                    average_rating=service_metrics.average_rating,
                    rating_count=service_metrics.rating_count,
                    usage_time_series=time_series_data,
                ),
            )

        except Exception as e:
            logger.error(f"Error getting service metrics: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetServiceMetricsResponse(
                success=False,
                message=f"Error getting service metrics: {str(e)}",
                metrics=analytics_pb2.ServiceMetrics(
                    entity_id=request.entity_id,
                    service_type=request.service_type,
                    usage_count=0,
                    average_rating=0.0,
                    rating_count=0,
                ),
            )

    def GetUserActivity(
        self, request: analytics_pb2.GetUserActivityRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetUserActivityResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_user_activity_request",
                user_id=request.user_id,
                time_period_days=request.time_period_days,
            )

            # Get user activity
            user_activity = (
                db.query(UserActivity).filter(UserActivity.user_id == request.user_id).first()
            )

            if not user_activity:
                return analytics_pb2.GetUserActivityResponse(
                    success=False,
                    message=f"No activity found for user with ID {request.user_id}",
                    activity=analytics_pb2.UserActivity(
                        user_id=request.user_id,
                        mcp_usage_count=0,
                        workflow_usage_count=0,
                        agent_usage_count=0,
                        mcp_creation_count=0,
                        workflow_creation_count=0,
                        agent_creation_count=0,
                        last_activity_date="",
                    ),
                )

            return analytics_pb2.GetUserActivityResponse(
                success=True,
                message="User activity retrieved successfully",
                activity=analytics_pb2.UserActivity(
                    user_id=user_activity.user_id,
                    mcp_usage_count=user_activity.mcp_usage_count,
                    workflow_usage_count=user_activity.workflow_usage_count,
                    agent_usage_count=user_activity.agent_usage_count,
                    mcp_creation_count=user_activity.mcp_creation_count,
                    workflow_creation_count=user_activity.workflow_creation_count,
                    agent_creation_count=user_activity.agent_creation_count,
                    last_activity_date=(
                        user_activity.last_activity_date.isoformat()
                        if user_activity.last_activity_date
                        else ""
                    ),
                ),
            )

        except Exception as e:
            logger.error(f"Error getting user activity: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetUserActivityResponse(
                success=False,
                message=f"Error getting user activity: {str(e)}",
                activity=analytics_pb2.UserActivity(
                    user_id=request.user_id,
                    mcp_usage_count=0,
                    workflow_usage_count=0,
                    agent_usage_count=0,
                    mcp_creation_count=0,
                    workflow_creation_count=0,
                    agent_creation_count=0,
                    last_activity_date="",
                ),
            )

    def GetRatingAnalytics(
        self, request: analytics_pb2.GetRatingAnalyticsRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetRatingAnalyticsResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_rating_analytics_request",
                service_type=analytics_pb2.ServiceType.Name(request.service_type),
                entity_id=request.entity_id,
                time_period_days=request.time_period_days,
            )

            # Build query filters
            filters = [
                AnalyticsEvent.service_type
                == analytics_pb2.ServiceType.Name(request.service_type).lower(),
                AnalyticsEvent.event_type == EventType.RATING.value,
            ]

            if request.entity_id:
                filters.append(AnalyticsEvent.entity_id == request.entity_id)

            if request.time_period_days > 0:
                start_date = datetime.now(timezone.utc) - timedelta(days=request.time_period_days)
                filters.append(AnalyticsEvent.created_at >= start_date)

            # Get rating events
            rating_events = db.query(AnalyticsEvent).filter(and_(*filters)).all()

            if not rating_events:
                return analytics_pb2.GetRatingAnalyticsResponse(
                    success=False,
                    message=f"No rating data found for the specified criteria",
                    analytics=analytics_pb2.RatingAnalytics(
                        service_type=request.service_type,
                        entity_id=request.entity_id,
                        average_rating=0.0,
                        rating_count=0,
                    ),
                )

            # Calculate rating analytics
            total_rating = 0.0
            rating_distribution = {}

            for event in rating_events:
                if event.event_metadata and "rating" in event.event_metadata:
                    rating = float(event.event_metadata["rating"])
                    total_rating += rating

                    # Update rating distribution
                    rating_str = str(int(rating))
                    if rating_str in rating_distribution:
                        rating_distribution[rating_str] += 1
                    else:
                        rating_distribution[rating_str] = 1

            average_rating = total_rating / len(rating_events) if rating_events else 0.0

            return analytics_pb2.GetRatingAnalyticsResponse(
                success=True,
                message="Rating analytics retrieved successfully",
                analytics=analytics_pb2.RatingAnalytics(
                    service_type=request.service_type,
                    entity_id=request.entity_id,
                    average_rating=average_rating,
                    rating_count=len(rating_events),
                    rating_distribution=rating_distribution,
                ),
            )

        except Exception as e:
            logger.error(f"Error getting rating analytics: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetRatingAnalyticsResponse(
                success=False,
                message=f"Error getting rating analytics: {str(e)}",
                analytics=analytics_pb2.RatingAnalytics(
                    service_type=request.service_type,
                    entity_id=request.entity_id,
                    average_rating=0.0,
                    rating_count=0,
                ),
            )

    def GetOverviewAnalytics(
        self, request: analytics_pb2.GetOverviewAnalyticsRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetOverviewAnalyticsResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_overview_analytics_request",
                user_id=request.user_id,
                time_period_days=request.time_period_days,
                application_id=request.application_id,
            )

            # Calculate time range
            if request.time_period_days > 0:
                start_date = datetime.now(timezone.utc) - timedelta(days=request.time_period_days)
            else:
                start_date = None

            # Get entity counts
            active_agents_count = (
                db.query(ServiceMetrics)
                .filter(
                    ServiceMetrics.service_type == "agent",
                    ServiceMetrics.status == AnalyticsStatus.ACTIVE,
                )
                .count()
            )

            active_workflows_count = (
                db.query(ServiceMetrics)
                .filter(
                    ServiceMetrics.service_type == "workflow",
                    ServiceMetrics.status == AnalyticsStatus.ACTIVE,
                )
                .count()
            )

            active_mcps_count = (
                db.query(ServiceMetrics)
                .filter(
                    ServiceMetrics.service_type == "mcp",
                    ServiceMetrics.status == AnalyticsStatus.ACTIVE,
                )
                .count()
            )

            applications_count = (
                db.query(Application)
                .filter(
                    Application.user_id == request.user_id,
                    Application.status == ApplicationStatus.ACTIVE,
                )
                .count()
            )

            # Get request metrics
            request_filters = [AnalyticsEvent.user_id == request.user_id]
            if start_date:
                request_filters.append(AnalyticsEvent.created_at >= start_date)
            if request.application_id:
                # Filter by application if specified
                pass  # Would need to add application_id to AnalyticsEvent

            total_requests = db.query(AnalyticsEvent).filter(and_(*request_filters)).count()

            successful_requests = (
                db.query(AnalyticsEvent)
                .filter(and_(*request_filters), AnalyticsEvent.event_type != EventType.ERROR.value)
                .count()
            )

            failed_requests = total_requests - successful_requests

            # Get recent activities (last 10)
            recent_activities = []
            recent_events = (
                db.query(AnalyticsEvent)
                .filter(AnalyticsEvent.user_id == request.user_id)
                .order_by(AnalyticsEvent.created_at.desc())
                .limit(10)
                .all()
            )

            for event in recent_events:
                recent_activities.append(
                    analytics_pb2.RecentActivity(
                        activity_id=event.id,
                        activity_type=event.event_type,
                        entity_id=event.entity_id,
                        entity_name=f"{event.service_type}_{event.entity_id}",
                        status="success" if event.event_type != EventType.ERROR.value else "failed",
                        timestamp=event.created_at.isoformat(),
                        metadata=json.dumps(event.event_metadata) if event.event_metadata else "{}",
                    )
                )

            # Create mock credit usage (would be calculated from actual usage)
            credit_usage = analytics_pb2.CreditUsage(
                total_credits_used=100.0,
                credits_remaining=900.0,
                credits_limit=1000.0,
                daily_average=10.0,
                projected_monthly=300.0,
            )

            # Create request metrics
            request_metrics = analytics_pb2.RequestMetrics(
                total_requests=total_requests,
                successful_requests=successful_requests,
                failed_requests=failed_requests,
                average_response_time=250.0,  # Mock value
                request_trend=[],  # Would calculate actual trend
            )

            # Create overview analytics response
            overview_analytics = analytics_pb2.OverviewAnalytics(
                credit_usage=credit_usage,
                active_agents_count=active_agents_count,
                active_workflows_count=active_workflows_count,
                active_mcps_count=active_mcps_count,
                applications_count=applications_count,
                request_metrics=request_metrics,
                recent_activities=recent_activities,
                usage_trend=[],  # Would calculate actual trend
                credit_trend=[],  # Would calculate actual trend
            )

            return analytics_pb2.GetOverviewAnalyticsResponse(
                success=True,
                message="Overview analytics retrieved successfully",
                analytics=overview_analytics,
            )

        except Exception as e:
            logger.error(f"Error getting overview analytics: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetOverviewAnalyticsResponse(
                success=False,
                message=f"Error getting overview analytics: {str(e)}",
                analytics=analytics_pb2.OverviewAnalytics(),
            )

    def TrackActivation(
        self, request: analytics_pb2.TrackActivationRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.TrackActivationResponse:
        db = self.get_db()
        try:
            logger.info(
                "track_activation_request",
                user_id=request.user_id,
                event_type=analytics_pb2.ActivationEventType.Name(request.event_type),
                entity_id=request.entity_id,
            )

            # Create new activation event
            new_activation = ActivationEvent(
                user_id=request.user_id,
                event_type=analytics_pb2.ActivationEventType.Name(request.event_type)
                .replace("ACTIVATION_EVENT_TYPE_", "")
                .lower(),
                entity_id=request.entity_id,
                event_metadata=json.loads(request.metadata) if request.metadata else {},
            )
            db.add(new_activation)
            db.commit()
            db.refresh(new_activation)

            return analytics_pb2.TrackActivationResponse(
                success=True,
                message="Activation event tracked successfully",
                activation_id=new_activation.id,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error tracking activation: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.TrackActivationResponse(
                success=False, message=f"Error tracking activation: {str(e)}", activation_id=""
            )

    def GetActivationMetrics(
        self, request: analytics_pb2.GetActivationMetricsRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetActivationMetricsResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_activation_metrics_request",
                user_id=request.user_id,
                time_period_days=request.time_period_days,
            )

            # Calculate time range
            if request.time_period_days > 0:
                start_date = datetime.now(timezone.utc) - timedelta(days=request.time_period_days)
            else:
                start_date = None

            # Build base query
            base_query = db.query(ActivationEvent)
            if request.user_id:
                base_query = base_query.filter(ActivationEvent.user_id == request.user_id)
            if start_date:
                base_query = base_query.filter(ActivationEvent.created_at >= start_date)

            # Get activation funnel data
            activation_funnel = {}
            for event_type in ActivationEventType:
                count = base_query.filter(ActivationEvent.event_type == event_type.value).count()
                activation_funnel[event_type.value] = count

            # Get total signups and activated users
            total_signups = activation_funnel.get("user_signup", 0)
            activated_users = len(
                set(
                    [
                        event.user_id
                        for event in base_query.all()
                        if event.event_type != "user_signup"
                    ]
                )
            )

            activation_rate = (activated_users / total_signups * 100) if total_signups > 0 else 0.0

            # Get activation trend (mock data for now)
            activation_trend = []

            metrics = analytics_pb2.ActivationMetrics(
                user_id=request.user_id,
                total_signups=total_signups,
                activated_users=activated_users,
                activation_rate=activation_rate,
                activation_funnel=activation_funnel,
                activation_trend=activation_trend,
            )

            return analytics_pb2.GetActivationMetricsResponse(
                success=True, message="Activation metrics retrieved successfully", metrics=metrics
            )

        except Exception as e:
            logger.error(f"Error getting activation metrics: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetActivationMetricsResponse(
                success=False,
                message=f"Error getting activation metrics: {str(e)}",
                metrics=analytics_pb2.ActivationMetrics(),
            )

    def CreateWebhook(
        self, request: analytics_pb2.CreateWebhookRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.CreateWebhookResponse:
        db = self.get_db()
        try:
            logger.info(
                "create_webhook_request",
                user_id=request.user_id,
                application_id=request.application_id,
                url=request.url,
            )

            # Convert event types from protobuf to strings
            event_types = [
                analytics_pb2.WebhookEventType.Name(event_type)
                .replace("WEBHOOK_EVENT_TYPE_", "")
                .lower()
                for event_type in request.event_types
            ]

            # Create new webhook
            new_webhook = Webhook(
                user_id=request.user_id,
                application_id=request.application_id if request.application_id else None,
                url=request.url,
                event_types=event_types,
                secret=request.secret if request.secret else None,
                description=request.description,
                is_active=request.is_active,
                status=WebhookStatus.ACTIVE,
            )
            db.add(new_webhook)
            db.commit()
            db.refresh(new_webhook)

            # Convert back to protobuf
            webhook_pb = analytics_pb2.Webhook(
                id=new_webhook.id,
                user_id=new_webhook.user_id,
                application_id=new_webhook.application_id or "",
                url=new_webhook.url,
                event_types=[
                    getattr(
                        analytics_pb2.WebhookEventType, f"WEBHOOK_EVENT_TYPE_{event_type.upper()}"
                    )
                    for event_type in new_webhook.event_types
                ],
                description=new_webhook.description or "",
                is_active=new_webhook.is_active,
                status=getattr(
                    analytics_pb2.WebhookStatus, f"WEBHOOK_STATUS_{new_webhook.status.upper()}"
                ),
                created_at=new_webhook.created_at.isoformat(),
                updated_at=new_webhook.updated_at.isoformat(),
                delivery_count=0,
                failure_count=0,
                last_delivery_at="",
            )

            return analytics_pb2.CreateWebhookResponse(
                success=True, message="Webhook created successfully", webhook=webhook_pb
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating webhook: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.CreateWebhookResponse(
                success=False,
                message=f"Error creating webhook: {str(e)}",
                webhook=analytics_pb2.Webhook(),
            )

    def GetWebhooks(
        self, request: analytics_pb2.GetWebhooksRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetWebhooksResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_webhooks_request",
                user_id=request.user_id,
                application_id=request.application_id,
            )

            # Build query
            query = db.query(Webhook).filter(Webhook.user_id == request.user_id)
            if request.application_id:
                query = query.filter(Webhook.application_id == request.application_id)

            webhooks = query.all()

            # Convert to protobuf messages
            webhook_list = []
            for webhook in webhooks:
                webhook_pb = analytics_pb2.Webhook(
                    id=webhook.id,
                    user_id=webhook.user_id,
                    application_id=webhook.application_id or "",
                    url=webhook.url,
                    event_types=[
                        getattr(
                            analytics_pb2.WebhookEventType,
                            f"WEBHOOK_EVENT_TYPE_{event_type.upper()}",
                        )
                        for event_type in webhook.event_types
                    ],
                    description=webhook.description or "",
                    is_active=webhook.is_active,
                    status=getattr(
                        analytics_pb2.WebhookStatus, f"WEBHOOK_STATUS_{webhook.status.upper()}"
                    ),
                    created_at=webhook.created_at.isoformat(),
                    updated_at=webhook.updated_at.isoformat(),
                    delivery_count=webhook.delivery_count,
                    failure_count=webhook.failure_count,
                    last_delivery_at=(
                        webhook.last_delivery_at.isoformat() if webhook.last_delivery_at else ""
                    ),
                )
                webhook_list.append(webhook_pb)

            return analytics_pb2.GetWebhooksResponse(
                success=True, message="Webhooks retrieved successfully", webhooks=webhook_list
            )

        except Exception as e:
            logger.error(f"Error getting webhooks: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetWebhooksResponse(
                success=False, message=f"Error getting webhooks: {str(e)}", webhooks=[]
            )

    def UpdateWebhook(
        self, request: analytics_pb2.UpdateWebhookRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.UpdateWebhookResponse:
        db = self.get_db()
        try:
            logger.info(
                "update_webhook_request",
                webhook_id=request.webhook_id,
                user_id=request.user_id,
            )

            # Get webhook
            webhook = (
                db.query(Webhook)
                .filter(Webhook.id == request.webhook_id, Webhook.user_id == request.user_id)
                .first()
            )

            if not webhook:
                return analytics_pb2.UpdateWebhookResponse(
                    success=False, message=f"Webhook not found with ID {request.webhook_id}"
                )

            # Update fields
            if request.url:
                webhook.url = request.url
            if request.description:
                webhook.description = request.description
            if request.event_types:
                webhook.event_types = [
                    analytics_pb2.WebhookEventType.Name(event_type)
                    .replace("WEBHOOK_EVENT_TYPE_", "")
                    .lower()
                    for event_type in request.event_types
                ]
            if request.secret:
                webhook.secret = request.secret

            webhook.is_active = request.is_active
            webhook.updated_at = datetime.now(timezone.utc)
            db.commit()

            return analytics_pb2.UpdateWebhookResponse(
                success=True, message="Webhook updated successfully"
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating webhook: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.UpdateWebhookResponse(
                success=False, message=f"Error updating webhook: {str(e)}"
            )

    def DeleteWebhook(
        self, request: analytics_pb2.DeleteWebhookRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.DeleteWebhookResponse:
        db = self.get_db()
        try:
            logger.info(
                "delete_webhook_request",
                webhook_id=request.webhook_id,
                user_id=request.user_id,
            )

            # Get webhook
            webhook = (
                db.query(Webhook)
                .filter(Webhook.id == request.webhook_id, Webhook.user_id == request.user_id)
                .first()
            )

            if not webhook:
                return analytics_pb2.DeleteWebhookResponse(
                    success=False, message=f"Webhook not found with ID {request.webhook_id}"
                )

            # Delete webhook
            db.delete(webhook)
            db.commit()

            return analytics_pb2.DeleteWebhookResponse(
                success=True, message="Webhook deleted successfully"
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting webhook: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.DeleteWebhookResponse(
                success=False, message=f"Error deleting webhook: {str(e)}"
            )

    def GetDashboardOverview(
        self, request: analytics_pb2.GetDashboardOverviewRequest, context: grpc.ServicerContext
    ) -> analytics_pb2.GetDashboardOverviewResponse:
        db = self.get_db()
        try:
            logger.info(
                "get_dashboard_overview_request",
                user_id=request.user_id,
                time_period_days=request.time_period_days,
            )

            # Calculate time range
            if request.time_period_days > 0:
                start_date = datetime.now(timezone.utc) - timedelta(days=request.time_period_days)
            else:
                start_date = datetime.now(timezone.utc) - timedelta(days=7)  # Default to 7 days

            # Get active agents count
            active_agents = (
                db.query(func.count(distinct(UsageEvent.entity_id)))
                .filter(
                    UsageEvent.user_id == request.user_id,
                    UsageEvent.entity_type == "agent",
                    UsageEvent.created_at >= start_date,
                )
                .scalar()
                or 0
            )

            # Get credit usage
            credit_usage_result = (
                db.query(func.sum(UsageEvent.credits_used))
                .filter(UsageEvent.user_id == request.user_id, UsageEvent.created_at >= start_date)
                .scalar()
                or 0
            )

            # Get agent requests count
            agent_requests = (
                db.query(func.count(UsageEvent.id))
                .filter(
                    UsageEvent.user_id == request.user_id,
                    UsageEvent.entity_type == "agent",
                    UsageEvent.created_at >= start_date,
                )
                .scalar()
                or 0
            )

            # Get workflow requests count
            workflow_requests = (
                db.query(func.count(UsageEvent.id))
                .filter(
                    UsageEvent.user_id == request.user_id,
                    UsageEvent.entity_type == "workflow",
                    UsageEvent.created_at >= start_date,
                )
                .scalar()
                or 0
            )

            # Get custom MCPs count
            custom_mcps = (
                db.query(func.count(distinct(UsageEvent.entity_id)))
                .filter(
                    UsageEvent.user_id == request.user_id,
                    UsageEvent.entity_type == "mcp",
                    UsageEvent.created_at >= start_date,
                )
                .scalar()
                or 0
            )

            # Get credit usage breakdown by category
            credit_breakdown = {}
            for entity_type in ["agent", "workflow", "mcp", "app_credit", "other"]:
                usage = (
                    db.query(func.sum(UsageEvent.credits_used))
                    .filter(
                        UsageEvent.user_id == request.user_id,
                        UsageEvent.entity_type == entity_type,
                        UsageEvent.created_at >= start_date,
                    )
                    .scalar()
                    or 0
                )
                credit_breakdown[entity_type] = float(usage)

            # Get app credit usage over time (daily breakdown)
            app_credit_usage = []
            for i in range(request.time_period_days or 7):
                day_start = start_date + timedelta(days=i)
                day_end = day_start + timedelta(days=1)

                daily_usage = (
                    db.query(func.sum(UsageEvent.credits_used))
                    .filter(
                        UsageEvent.user_id == request.user_id,
                        UsageEvent.entity_type == "app_credit",
                        UsageEvent.created_at >= day_start,
                        UsageEvent.created_at < day_end,
                    )
                    .scalar()
                    or 0
                )

                app_credit_usage.append(
                    analytics_pb2.DashboardTimeSeriesPoint(
                        timestamp=day_start.isoformat(), value=float(daily_usage)
                    )
                )

            # Get agent performance data
            agent_performance = []
            for i in range(request.time_period_days or 7):
                day_start = start_date + timedelta(days=i)
                day_end = day_start + timedelta(days=1)

                daily_requests = (
                    db.query(func.count(UsageEvent.id))
                    .filter(
                        UsageEvent.user_id == request.user_id,
                        UsageEvent.entity_type == "agent",
                        UsageEvent.created_at >= day_start,
                        UsageEvent.created_at < day_end,
                    )
                    .scalar()
                    or 0
                )

                # Mock completion rate for now
                completion_rate = 95.0 if daily_requests > 0 else 0.0

                agent_performance.append(
                    analytics_pb2.AgentPerformanceDataPoint(
                        timestamp=day_start.isoformat(),
                        requests=daily_requests,
                        completion_rate=completion_rate,
                    )
                )

            # Get recent API requests and events
            recent_events = []
            recent_usage_events = (
                db.query(UsageEvent)
                .filter(UsageEvent.user_id == request.user_id, UsageEvent.created_at >= start_date)
                .order_by(UsageEvent.created_at.desc())
                .limit(10)
                .all()
            )

            for event in recent_usage_events:
                # Determine status based on success
                status = (
                    "success"
                    if event.event_metadata and event.event_metadata.get("success", True)
                    else "failed"
                )

                recent_events.append(
                    analytics_pb2.RecentEvent(
                        type=event.entity_type,
                        endpoint=f"/api/{event.entity_type}s/{event.action}",
                        status=status,
                        timestamp=event.created_at.isoformat(),
                        duration=(
                            event.event_metadata.get("duration", "0ms")
                            if event.event_metadata
                            else "0ms"
                        ),
                        user=event.user_id,
                    )
                )

            # Create dashboard overview
            overview = analytics_pb2.DashboardOverview(
                user_id=request.user_id,
                active_agents=active_agents,
                credit_usage=float(credit_usage_result),
                agent_requests=agent_requests,
                workflow_requests=workflow_requests,
                custom_mcps=custom_mcps,
                credit_breakdown=credit_breakdown,
                app_credit_usage=app_credit_usage,
                agent_performance=agent_performance,
                recent_events=recent_events,
            )

            return analytics_pb2.GetDashboardOverviewResponse(
                success=True, message="Dashboard overview retrieved successfully", overview=overview
            )

        except Exception as e:
            logger.error(f"Error getting dashboard overview: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return analytics_pb2.GetDashboardOverviewResponse(
                success=False,
                message=f"Error getting dashboard overview: {str(e)}",
                overview=analytics_pb2.DashboardOverview(),
            )
