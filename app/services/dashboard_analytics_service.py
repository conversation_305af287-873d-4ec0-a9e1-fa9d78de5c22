"""
Dashboard Analytics Service
Service layer for dashboard analytics functionality
"""

from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, asc
from app.models.dashboard_analytics import (
    DashboardMetrics,
    CreditUsageBreakdown,
    ApiRequestEvent,
    AgentPerformanceMetrics,
    WorkflowUtilizationMetrics,
    AppCreditUsage,
    SystemActivity,
    RequestType,
    RequestStatus,
    CreditCategory,
)
from app.db.session import get_db
import logging

logger = logging.getLogger(__name__)


class DashboardAnalyticsService:
    """Service for dashboard analytics operations"""

    def __init__(self, db: Session):
        self.db = db

    def get_dashboard_overview(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get dashboard overview metrics

        Args:
            user_id: Optional user ID for user-specific metrics
            days: Number of days to look back for data

        Returns:
            Dictionary containing dashboard overview data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Get latest metrics
        query = self.db.query(DashboardMetrics).filter(DashboardMetrics.date >= start_date)

        if user_id:
            query = query.filter(DashboardMetrics.user_id == user_id)

        latest_metrics = query.order_by(desc(DashboardMetrics.date)).first()

        if not latest_metrics:
            # Return default values if no data
            return {
                "active_agents": 0,
                "credit_usage": 0.0,
                "agent_requests": 0,
                "workflow_requests": 0,
                "custom_mcps": 0,
                "credit_usage_change": 0.0,
                "agent_requests_change_pct": 0.0,
                "workflow_requests_change_pct": 0.0,
                "custom_mcps_change": 0,
                "total_cost": 0.0,
            }

        return {
            "active_agents": latest_metrics.active_agents,
            "credit_usage": latest_metrics.credit_usage,
            "agent_requests": latest_metrics.agent_requests,
            "workflow_requests": latest_metrics.workflow_requests,
            "custom_mcps": latest_metrics.custom_mcps,
            "credit_usage_change": latest_metrics.credit_usage_change,
            "agent_requests_change_pct": latest_metrics.agent_requests_change_pct,
            "workflow_requests_change_pct": latest_metrics.workflow_requests_change_pct,
            "custom_mcps_change": latest_metrics.custom_mcps_change,
            "total_cost": latest_metrics.total_cost,
        }

    def get_credit_usage_breakdown(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        Get credit usage breakdown by category for bar chart

        Args:
            user_id: Optional user ID for user-specific metrics
            days: Number of days to aggregate data

        Returns:
            List of credit usage by category
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        query = (
            self.db.query(
                CreditUsageBreakdown.category,
                func.sum(CreditUsageBreakdown.credits_used).label("total_credits"),
                func.sum(CreditUsageBreakdown.cost).label("total_cost"),
                func.sum(CreditUsageBreakdown.request_count).label("total_requests"),
            )
            .filter(CreditUsageBreakdown.date >= start_date)
            .group_by(CreditUsageBreakdown.category)
        )

        if user_id:
            query = query.filter(CreditUsageBreakdown.user_id == user_id)

        results = query.all()

        return [
            {
                "category": result.category.value,
                "credits_used": float(result.total_credits or 0),
                "cost": float(result.total_cost or 0),
                "request_count": int(result.total_requests or 0),
            }
            for result in results
        ]

    def get_app_credit_usage_timeseries(
        self, user_id: Optional[str] = None, application_id: Optional[str] = None, days: int = 7
    ) -> Dict[str, Any]:
        """
        Get app credit usage over time for time series chart

        Args:
            user_id: Optional user ID filter
            application_id: Optional application ID filter
            days: Number of days of data

        Returns:
            Time series data for app credit usage
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        query = (
            self.db.query(AppCreditUsage)
            .filter(AppCreditUsage.timestamp >= start_date)
            .order_by(AppCreditUsage.timestamp)
        )

        if user_id:
            query = query.filter(AppCreditUsage.user_id == user_id)

        if application_id:
            query = query.filter(AppCreditUsage.application_id == application_id)

        results = query.all()

        # Calculate totals
        total_credits = sum(r.credits_used for r in results)
        total_cost = sum(r.cost for r in results)

        return {
            "total_credits": total_credits,
            "total_cost": total_cost,
            "timeseries": [
                {
                    "timestamp": result.timestamp.isoformat(),
                    "credits_used": result.credits_used,
                    "cost": result.cost,
                    "cumulative_credits": result.cumulative_credits,
                    "cumulative_cost": result.cumulative_cost,
                }
                for result in results
            ],
        }

    def get_latest_api_requests(
        self, user_id: Optional[str] = None, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get latest API requests and events

        Args:
            user_id: Optional user ID filter
            limit: Maximum number of records to return

        Returns:
            List of recent API requests and events
        """
        query = (
            self.db.query(ApiRequestEvent).order_by(desc(ApiRequestEvent.timestamp)).limit(limit)
        )

        if user_id:
            query = query.filter(ApiRequestEvent.user_id == user_id)

        results = query.all()

        return [
            {
                "id": str(result.id),
                "type": result.request_type.value,
                "endpoint": result.endpoint,
                "method": result.method,
                "status": result.status.value,
                "timestamp": result.timestamp.isoformat(),
                "duration_ms": result.duration_ms,
                "user_email": result.user_email,
                "error_message": result.error_message,
                "credits_used": result.credits_used,
                "cost": result.cost,
            }
            for result in results
        ]

    def get_agent_performance_metrics(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        Get agent performance metrics for platform analytics

        Args:
            user_id: Optional user ID filter
            days: Number of days of data

        Returns:
            Agent performance data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        query = (
            self.db.query(
                AgentPerformanceMetrics.agent_id,
                AgentPerformanceMetrics.agent_name,
                func.sum(AgentPerformanceMetrics.total_requests).label("total_requests"),
                func.sum(AgentPerformanceMetrics.successful_requests).label("successful_requests"),
                func.sum(AgentPerformanceMetrics.failed_requests).label("failed_requests"),
                func.avg(AgentPerformanceMetrics.avg_response_time_ms).label("avg_response_time"),
                func.sum(AgentPerformanceMetrics.total_credits_used).label("total_credits"),
                func.sum(AgentPerformanceMetrics.total_cost).label("total_cost"),
                func.bool_and(AgentPerformanceMetrics.is_active).label("is_active"),
            )
            .filter(AgentPerformanceMetrics.date >= start_date)
            .group_by(AgentPerformanceMetrics.agent_id, AgentPerformanceMetrics.agent_name)
        )

        if user_id:
            query = query.filter(AgentPerformanceMetrics.user_id == user_id)

        results = query.all()

        return [
            {
                "agent_id": result.agent_id,
                "agent_name": result.agent_name,
                "total_requests": int(result.total_requests or 0),
                "successful_requests": int(result.successful_requests or 0),
                "failed_requests": int(result.failed_requests or 0),
                "success_rate": (
                    (result.successful_requests / result.total_requests * 100)
                    if result.total_requests > 0
                    else 0
                ),
                "avg_response_time_ms": float(result.avg_response_time or 0),
                "total_credits_used": float(result.total_credits or 0),
                "total_cost": float(result.total_cost or 0),
                "is_active": bool(result.is_active),
            }
            for result in results
        ]

    def get_workflow_utilization_metrics(
        self, user_id: Optional[str] = None, days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        Get workflow utilization metrics

        Args:
            user_id: Optional user ID filter
            days: Number of days of data

        Returns:
            Workflow utilization data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        query = (
            self.db.query(
                WorkflowUtilizationMetrics.workflow_id,
                WorkflowUtilizationMetrics.workflow_name,
                func.sum(WorkflowUtilizationMetrics.total_executions).label("total_executions"),
                func.sum(WorkflowUtilizationMetrics.successful_executions).label(
                    "successful_executions"
                ),
                func.sum(WorkflowUtilizationMetrics.failed_executions).label("failed_executions"),
                func.avg(WorkflowUtilizationMetrics.avg_execution_time_ms).label(
                    "avg_execution_time"
                ),
                func.avg(WorkflowUtilizationMetrics.completion_rate_pct).label("completion_rate"),
                func.sum(WorkflowUtilizationMetrics.total_credits_used).label("total_credits"),
                func.sum(WorkflowUtilizationMetrics.total_cost).label("total_cost"),
            )
            .filter(WorkflowUtilizationMetrics.date >= start_date)
            .group_by(
                WorkflowUtilizationMetrics.workflow_id, WorkflowUtilizationMetrics.workflow_name
            )
        )

        if user_id:
            query = query.filter(WorkflowUtilizationMetrics.user_id == user_id)

        results = query.all()

        return [
            {
                "workflow_id": result.workflow_id,
                "workflow_name": result.workflow_name,
                "total_executions": int(result.total_executions or 0),
                "successful_executions": int(result.successful_executions or 0),
                "failed_executions": int(result.failed_executions or 0),
                "success_rate": (
                    (result.successful_executions / result.total_executions * 100)
                    if result.total_executions > 0
                    else 0
                ),
                "avg_execution_time_ms": float(result.avg_execution_time or 0),
                "completion_rate_pct": float(result.completion_rate or 0),
                "total_credits_used": float(result.total_credits or 0),
                "total_cost": float(result.total_cost or 0),
            }
            for result in results
        ]

    def get_system_activity(
        self, user_id: Optional[str] = None, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get recent system activity and issues

        Args:
            user_id: Optional user ID filter
            limit: Maximum number of records to return

        Returns:
            List of recent system activities
        """
        query = self.db.query(SystemActivity).order_by(desc(SystemActivity.timestamp)).limit(limit)

        if user_id:
            query = query.filter(SystemActivity.user_id == user_id)

        results = query.all()

        return [
            {
                "id": str(result.id),
                "activity_type": result.activity_type,
                "title": result.title,
                "description": result.description,
                "severity": result.severity,
                "status": result.status,
                "timestamp": result.timestamp.isoformat(),
                "user_id": result.user_id,
                "customer_id": result.customer_id,
                "metadata": result.activity_metadata,
            }
            for result in results
        ]

    # Data recording methods

    def record_api_request(
        self,
        request_type: RequestType,
        endpoint: str,
        status: RequestStatus,
        duration_ms: Optional[int] = None,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        method: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_data: Optional[Dict] = None,
        response_data: Optional[Dict] = None,
        error_message: Optional[str] = None,
        agent_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        application_id: Optional[str] = None,
        credits_used: float = 0.0,
        cost: float = 0.0,
    ) -> ApiRequestEvent:
        """
        Record an API request event

        Args:
            request_type: Type of request
            endpoint: API endpoint
            status: Request status
            duration_ms: Request duration in milliseconds
            user_id: User ID
            user_email: User email
            method: HTTP method
            ip_address: Client IP address
            user_agent: User agent string
            request_data: Request payload
            response_data: Response data
            error_message: Error message if failed
            agent_id: Related agent ID
            workflow_id: Related workflow ID
            application_id: Related application ID
            credits_used: Credits consumed
            cost: Cost incurred

        Returns:
            Created ApiRequestEvent instance
        """
        event = ApiRequestEvent(
            request_type=request_type,
            endpoint=endpoint,
            status=status,
            duration_ms=duration_ms,
            user_id=user_id,
            user_email=user_email,
            method=method,
            ip_address=ip_address,
            user_agent=user_agent,
            request_data=request_data,
            response_data=response_data,
            error_message=error_message,
            agent_id=agent_id,
            workflow_id=workflow_id,
            application_id=application_id,
            credits_used=credits_used,
            cost=cost,
        )

        self.db.add(event)
        self.db.commit()
        self.db.refresh(event)

        return event

    def record_system_activity(
        self,
        activity_type: str,
        title: str,
        description: Optional[str] = None,
        severity: Optional[str] = None,
        status: Optional[str] = None,
        user_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        metadata: Optional[Dict] = None,
    ) -> SystemActivity:
        """
        Record a system activity or issue

        Args:
            activity_type: Type of activity (inquiry, alert, etc.)
            title: Activity title
            description: Activity description
            severity: Severity level (info, warning, error)
            status: Activity status
            user_id: Related user ID
            customer_id: Related customer ID
            metadata: Additional metadata

        Returns:
            Created SystemActivity instance
        """
        activity = SystemActivity(
            activity_type=activity_type,
            title=title,
            description=description,
            severity=severity,
            status=status,
            user_id=user_id,
            customer_id=customer_id,
            activity_metadata=metadata,
        )

        self.db.add(activity)
        self.db.commit()
        self.db.refresh(activity)

        return activity

    def update_dashboard_metrics(
        self,
        date: datetime,
        user_id: Optional[str] = None,
        active_agents: Optional[int] = None,
        credit_usage: Optional[float] = None,
        agent_requests: Optional[int] = None,
        workflow_requests: Optional[int] = None,
        custom_mcps: Optional[int] = None,
        total_cost: Optional[float] = None,
        app_credits_used: Optional[int] = None,
        app_credits_cost: Optional[float] = None,
    ) -> DashboardMetrics:
        """
        Update or create dashboard metrics for a specific date

        Args:
            date: Date for the metrics
            user_id: Optional user ID
            active_agents: Number of active agents
            credit_usage: Total credit usage
            agent_requests: Number of agent requests
            workflow_requests: Number of workflow requests
            custom_mcps: Number of custom MCPs
            total_cost: Total cost
            app_credits_used: App credits used
            app_credits_cost: App credits cost

        Returns:
            Updated or created DashboardMetrics instance
        """
        # Try to find existing metrics for the date
        query = self.db.query(DashboardMetrics).filter(DashboardMetrics.date == date.date())

        if user_id:
            query = query.filter(DashboardMetrics.user_id == user_id)

        metrics = query.first()

        if not metrics:
            metrics = DashboardMetrics(date=date.date(), user_id=user_id)
            self.db.add(metrics)

        # Update provided values
        if active_agents is not None:
            metrics.active_agents = active_agents
        if credit_usage is not None:
            metrics.credit_usage = credit_usage
        if agent_requests is not None:
            metrics.agent_requests = agent_requests
        if workflow_requests is not None:
            metrics.workflow_requests = workflow_requests
        if custom_mcps is not None:
            metrics.custom_mcps = custom_mcps
        if total_cost is not None:
            metrics.total_cost = total_cost
        if app_credits_used is not None:
            metrics.app_credits_used = app_credits_used
        if app_credits_cost is not None:
            metrics.app_credits_cost = app_credits_cost

        self.db.commit()
        self.db.refresh(metrics)

        return metrics

    def record_credit_usage(
        self,
        date: datetime,
        category: CreditCategory,
        credits_used: float,
        cost: float,
        request_count: int = 1,
        user_id: Optional[str] = None,
    ) -> CreditUsageBreakdown:
        """
        Record credit usage breakdown by category

        Args:
            date: Date of usage
            category: Credit category
            credits_used: Credits consumed
            cost: Cost incurred
            request_count: Number of requests
            user_id: Optional user ID

        Returns:
            Created or updated CreditUsageBreakdown instance
        """
        # Try to find existing breakdown for the date and category
        query = self.db.query(CreditUsageBreakdown).filter(
            and_(
                CreditUsageBreakdown.date == date.date(), CreditUsageBreakdown.category == category
            )
        )

        if user_id:
            query = query.filter(CreditUsageBreakdown.user_id == user_id)

        breakdown = query.first()

        if not breakdown:
            breakdown = CreditUsageBreakdown(
                date=date.date(),
                category=category,
                user_id=user_id,
                credits_used=0.0,
                cost=0.0,
                request_count=0,
            )
            self.db.add(breakdown)

        # Accumulate values
        breakdown.credits_used += credits_used
        breakdown.cost += cost
        breakdown.request_count += request_count

        self.db.commit()
        self.db.refresh(breakdown)

        return breakdown

    def record_app_credit_usage(
        self,
        timestamp: datetime,
        credits_used: float,
        cost: float,
        application_id: Optional[str] = None,
        application_name: Optional[str] = None,
        user_id: Optional[str] = None,
    ) -> AppCreditUsage:
        """
        Record application credit usage

        Args:
            timestamp: Timestamp of usage
            credits_used: Credits consumed
            cost: Cost incurred
            application_id: Application ID
            application_name: Application name
            user_id: User ID

        Returns:
            Created AppCreditUsage instance
        """
        # Calculate cumulative values
        cumulative_query = self.db.query(
            func.coalesce(func.sum(AppCreditUsage.credits_used), 0).label("total_credits"),
            func.coalesce(func.sum(AppCreditUsage.cost), 0).label("total_cost"),
        ).filter(AppCreditUsage.timestamp <= timestamp)

        if user_id:
            cumulative_query = cumulative_query.filter(AppCreditUsage.user_id == user_id)

        if application_id:
            cumulative_query = cumulative_query.filter(
                AppCreditUsage.application_id == application_id
            )

        cumulative_result = cumulative_query.first()
        cumulative_credits = float(cumulative_result.total_credits or 0) + credits_used
        cumulative_cost = float(cumulative_result.total_cost or 0) + cost

        usage = AppCreditUsage(
            timestamp=timestamp,
            application_id=application_id,
            application_name=application_name,
            user_id=user_id,
            credits_used=credits_used,
            cost=cost,
            cumulative_credits=cumulative_credits,
            cumulative_cost=cumulative_cost,
        )

        self.db.add(usage)
        self.db.commit()
        self.db.refresh(usage)

        return usage
