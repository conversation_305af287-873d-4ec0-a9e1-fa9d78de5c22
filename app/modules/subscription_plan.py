from pydantic import BaseModel, Field
from typing import Any
from datetime import datetime
from bson import ObjectId
from pydantic_core import core_schema


class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_core_schema__(
        cls, _source_type: Any, _handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            python_schema=core_schema.is_instance_schema(ObjectId),
            json_schema=core_schema.str_schema(pattern=r"^[a-f0-9]{24}$"),
            serialization=core_schema.to_string_ser_schema(),
        )


class SubscriptionPlan(BaseModel):
    id: str = Field(default_factory=lambda: str(ObjectId()))
    name: str
    description: str
    price: float
    currency: str
    interval: str
    interval_count: int
    active: str
    stripe_product_id: str
    stripe_price_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {"json_encoders": {ObjectId: str}, "populate_by_name": True}

    @classmethod
    def from_mongo(cls, data: dict):
        """
        Convert MongoDB document to Pydantic model,
        converting '_id' to 'id' if it exists
        """
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        return cls(**data)

    def to_mongo(self):
        """Convert Pydantic model to MongoDB document"""
        data = self.model_dump(by_alias=True, exclude=["id"])
        if self.id:
            data["id"] = ObjectId(self.id)
        return data
