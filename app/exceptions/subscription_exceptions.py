from werkzeug.exceptions import HTTPException


class BaseExceptionClass(HTTPException):
    code: int
    description: str


class SubscriptionError(BaseExceptionClass):
    code = 400
    description = "Invalid subscription plan."


class PaymentError(BaseExceptionClass):
    code = 500
    description = "Error creating subscription."


class PlanNotFoundError(BaseExceptionClass):
    code = 404
    description = "Subscription plan not found."


class InvalidCouponError(BaseExceptionClass):
    code = 400
    description = "Invalid coupon code."


class InsufficientBalanceError(BaseExceptionClass):
    code = 402
    description = "Insufficient balance to complete the subscription."


class UnauthorizedError(BaseExceptionClass):
    code = 401
    description = "Unauthorized access to subscription."


class SubscriptionLimitExceededError(BaseExceptionClass):
    code = 403
    description = "Subscription limit exceeded."


class PaymentMethodError(BaseExceptionClass):
    code = 422
    description = "Invalid or unsupported payment method."


class SubscriptionInactiveError(BaseExceptionClass):
    code = 410
    description = "The subscription is no longer active."


class SubscriptionActiveError(BaseExceptionClass):
    code = 410
    description = "The subscription is already active."


class SubscriptionRenewalError(BaseExceptionClass):
    code = 500
    description = "Error renewing subscription."


class PlanUpgradeError(BaseExceptionClass):
    code = 400
    description = "Cannot upgrade to the selected plan."


class PlanDowngradeError(BaseExceptionClass):
    code = 400
    description = "Cannot downgrade to the selected plan."


class InvalidSubscriptionPeriodError(BaseExceptionClass):
    code = 400
    description = "Invalid subscription period selected."


class SubscriptionCancellationError(BaseExceptionClass):
    code = 500
    description = "Error cancelling the subscription."


class TrialExpiredError(BaseExceptionClass):
    code = 410
    description = "Trial period has expired."


class SubscriptionSuspendedError(BaseExceptionClass):
    code = 403
    description = "The subscription has been suspended."


class SubscriptionCreationError(BaseExceptionClass):
    code = 500
    description = "Error creating subscription."


class InsufficientCreditsError(BaseExceptionClass):
    code = 402
    description = "Insufficient credit to complete the action."
