from werkzeug.exceptions import HTTPException

class BaseExceptionClass(HTTPException):
    code: int
    description: str


class VideoNotFoundError(BaseExceptionClass):
    """
    Exception raised when a Video cannot be found with the provided identifiers.
    """

    code = 404
    description = "Video not found."


class VideoListFetchError(BaseExceptionClass):
    """
    Exception raised when an error occurs while attempting to fetch the list of Videos from the database.
    """

    code = 500
    description = "Failed to fetch the list of Videos."


class VideoInitializationError(BaseExceptionClass):
    """
    Exception raised when an error occurs while initializing video.
    """

    code = 500
    description = "Failed to finitialize the video."


class VideoForbidden(BaseExceptionClass):
    """
    Exception raised when user is authorised but cannot access some specific data.
    """

    code = 403
    description = "Failed to access this video"


class VideoInvalidStepOrder(BaseExceptionClass):
    """
    Exception raised when user is authorised but cannot access some specific data.
    """

    code = 400
    description = "Failed to complete the previous steps"


class VideoCreationFailed(BaseExceptionClass):

    code = 400
    description = "Failed to generate the video"


class TavusVoiceGenerationFailed(BaseExceptionClass):
    code = 500
    description = "Failed to tavus generate video in playht."
