from werkzeug.exceptions import NotFound, BadRequest
from functools import wraps
import logging

from pymongo.errors import PyMongoError
from bson import errors as bson_errors

from .common import InvalidAuthError, ForbiddenAccessError
from .subscription_exceptions import (
    SubscriptionInactiveError,
    SubscriptionError,
    InsufficientCreditsError,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def handle_errors(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except NotFound as e:
            logger.warning(f"NotFound error: {str(e)}")
            return {"message": str(e), "code": 404}, 404
        except BadRequest as e:
            logger.warning(f"BadRequest error: {str(e)}")
            return {"message": str(e), "code": 400}, 400
        except SubscriptionInactiveError as e:
            logger.warning(f"Subscription error: {str(e)}")
            return {"message": str(e), "code": 410}, 410
        except InvalidAuthError as e:
            logger.warning(f"Unauthorized error: {str(e)}")
            return {"message": str(e), "code": 401}, 401
        except ForbiddenAccessError as e:
            logger.warning(f"Forbidden error: {str(e)}")
            return {"message": str(e), "code": 403}, 403
        except InsufficientCreditsError as e:
            logger.warning(f"Forbidden error: {str(e)}")
            return {"message": str(e), "code": 403}, 403
        except ValueError as e:
            logger.warning(f"Value error: {str(e)}")
            return {"message": str(e), "code": 400}, 400
        except SubscriptionError as e:
            logger.error(f"Subscription error for user: {str(e)}")
            return {"message": str(e), "code": 400}, 400
        except PyMongoError as e:
            logger.error(f"Database error: {str(e)}")
            return {"message": str(e), "code": 500}, 500
        except bson_errors.InvalidId as e:
            logger.warning(f"Invalid ObjectId error: {str(e)}")
            return {"message": str(e), "code": 400}, 400
        except Exception as e:
            logger.exception(f"Unexpected error in {func.__name__}: {str(e)}")
            return {"message": "An unexpected error occurred", "code": 500}, 500

    return wrapper
