from werkzeug.exceptions import HTTPException


class BaseExceptionClass(HTTPException):
    code: int
    description: str


def handle_bad_request(error):
    return {"message": str(error)}, 400


def handle_generic_error(error):
    return {"message": "An unexpected error occurred"}, 500


class InvalidAuthError(BaseExceptionClass):

    code = 401
    description = "Authentication error."


class InvalidValueError(BaseExceptionClass):
    """
    Exception raised when a provided value for an operation is deemed invalid.
    """

    code = 400
    description = "The provided value is invalid."


class ForbiddenAccessError(BaseExceptionClass):
    code = 403
    description = "Access forbidden."


class InvalidAuthTokenFormatError(BaseExceptionClass):
    """
    Exception raised when the format of the provided authentication token does not meet the expected format.
    """

    code = 400
    description = "Invalid auth token format."


class InvalidTokenError(BaseExceptionClass):
    """Exception raised when a password reset token is invalid or expired."""

    code = 422
    description = "Invalid or expired auth token."


class InvalidEmailOrPasswordError(BaseExceptionClass):
    """
    Exception raised during the authentication process when either the email or password provided is incorrect.
    """

    code = 400
    description = "Invalid email id or password."


class InactiveAccountError(BaseExceptionClass):
    """
    Exception raised during the authentication process when either the email or password provided is incorrect.
    """

    code = 400
    description = "Account is Inactive."


class AuthenticationError(BaseExceptionClass):
    """
    Exception raised during the authentication process when either the email or password provided is incorrect.
    """

    code = 400
    description = "Failed to generate authentication token."


class InvalidEmailError(BaseExceptionClass):
    """
    Exception raised when the provided email does not match the required format or validation criteria.
    """

    code = 400
    description = "The provided email is invalid."


class InternalError(BaseExceptionClass):
    """
    Exception raised when there is an internal server error.
    """

    code = 500
    description = "Internal server error."


class CallerEndpointException(BaseExceptionClass):
    """
    Exception raised when there is an error in the caller api.
    """

    code = 500
    description = "Something went wrong."


class DatabaseError(BaseExceptionClass):
    code = 500
    description = "Something went wrong in the database."


class TokenExpiredError(BaseExceptionClass):
    code = 401
    description = "Token has expired. Please log in again."
