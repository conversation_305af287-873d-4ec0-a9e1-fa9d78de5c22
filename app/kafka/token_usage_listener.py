# app/kafka/token_usage_listener.py

import json
import logging
from confluent_kafka import Consumer, KafkaException
from sqlalchemy.orm import Session

from app.core.config import settings
from app.services.payment_service import PaymentService
from app.schemas.payment import TokenUsageEvent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_kafka_consumer():
    """Creates and returns a Kafka consumer."""
    consumer_conf = {
        'bootstrap.servers': settings.BOOTSTRAP_SERVERS,
        'group.id': 'payment-service-token-usage-group',
        'auto.offset.reset': 'earliest',
        # Add any other necessary Kafka consumer configurations
    }
    return Consumer(consumer_conf)

def process_token_usage_event(db: Session, msg_value: dict):
    """
    Processes a single token usage event.
    """
    try:
        # 1. Validate the incoming message payload
        token_usage_event = TokenUsageEvent(**msg_value)
        logger.info(f"Processing event: {token_usage_event.event_id} for org: {token_usage_event.organisation_id}")

        payment_service = PaymentService()

        # 2. Ensure idempotency - check if this event has been processed before
        if payment_service.has_processed_event(token_usage_event.event_id, db):
            logger.warning(f"Event {token_usage_event.event_id} has already been processed. Skipping.")
            return

        # 3. Deduct credits and log the transaction atomically
        payment_service.deduct_credits_for_token_usage(event=token_usage_event, db=db)

        # 4. (Optional but recommended) Update the user-service
        # This assumes you have a gRPC client for the user service
        # user_service_client.update_organization_balance(...)


        logger.info(f"Successfully processed event {token_usage_event.event_id}")

    except Exception as e:
        logger.error(f"Error processing token usage event: {e}", exc_info=True)
        # Here you might want to add logic to move the message to a dead-letter queue

def start_token_usage_listener(db: Session):
    """
    Starts the Kafka consumer to listen for token usage events.
    """
    consumer = get_kafka_consumer()
    consumer.subscribe([settings.KAFKA_TOKEN_USAGE_TOPIC])
    logger.info(f"Subscribed to topic: {settings.KAFKA_TOKEN_USAGE_TOPIC} and waiting for messages...")

    try:
        while True:
            msg = consumer.poll(timeout=1.0)
            if msg is None:
                continue
            if msg.error():
                if msg.error().code() == KafkaException._PARTITION_EOF:
                    # End of partition event
                    logger.info(f'{msg.topic()} [{msg.partition()}] reached end at offset {msg.offset()}')
                elif msg.error():
                    raise KafkaException(msg.error())
            else:
                # Proper message
                msg_value = json.loads(msg.value().decode('utf-8'))
                process_token_usage_event(db, msg_value)

    except KeyboardInterrupt:
        logger.info("Aborted by user")
    finally:
        # Close down consumer to commit final offsets.
        consumer.close()

# You would typically run this listener as a separate process or service.
# For example, in your main.py or a separate runner script.
if __name__ == '__main__':
    # This is for demonstration. You'd integrate this into your app's lifecycle.
    # from app.db.session import SessionLocal
    # db = SessionLocal()
    # start_token_usage_listener(db)
    pass