from app.service.tavily import search_tool
from app.service.serper import search_from_serper


from autogen.agentchat import (
    Agent,
    AssistantAgent,
    GroupChat,
    UserProxyAgent,
    register_function,
)

from model_config import config_list

from autogen.coding import LocalCommandLineCodeExecutor

from .prompts import (
    ANALYSE_AGENT_PROMPT,
    ARTICLE_ANALYSE_AGENT_PROMPT,
    SCRIPT_ANALYSE_AGENT_PROMPT,
    TITLE_AGENT_PROMPT,
    TONE_AGENT_PROMPT,
    TONE_AGENT_REEL_PROMPT,
    CONTENT_AGENT_PROMPT,
    CONTENT_AGENT_REEL_PROMPT,
    INTRO_HOOK_AGENT_PROMPT,
    INTRO_HOOK_REEL_AGENT_PROMPT,
    FORMATTER_AGENT_PROMPT,
    SPOKEN_ENGLISH_AGENT_PROMPT,
    OUTRO_AGENT_PROMPT,
    SCENE_CREATOR_AGENT_PROMPT,
)


llm_config = {
    "config_list": config_list,
    "timeout": 180,
    "cache_seed": None,
}

code_executor = LocalCommandLineCodeExecutor(work_dir="coding")


user_proxy = UserProxyAgent(
    name="Admin",
    system_message="""
    A human admin. Give the task, and send instructions to writer to refine the 
    content generated by the Content Generator for a video
    """,
    code_execution_config=False,
)

Master_Agent = AssistantAgent(
    name="Master_Agent",
    system_message="""" 
    You are a helpful AI assistant that determines 
    from the previous chat whether the user is asking for a reel or a video. 
    If the user doesn't mention explicitly consider it a video . 
    If the user say that the video is 60 sec or less than that consider it is a reel.
    If it is a reel say "It is a reel." else say "It is a video.
    """,
    llm_config=llm_config,
)

Research_Agent = AssistantAgent(
    name="Research_Agent",
    system_message="""
    Your input is the output of the user_proxy agent.Only use the tool you have been provided with.
    """,
    llm_config=llm_config,
)

Transcript_Agent = AssistantAgent(
    name="Transcript_Agent",
    system_message="""
    Your input is the output of the user_proxy agent. Only use the tool you have been provided with.
    """,
    llm_config=llm_config,
)

Analyse_Agent = AssistantAgent(
    name="Analyse_Agent",
    system_message=ANALYSE_AGENT_PROMPT,
    llm_config=llm_config,
)

Article_Analyse_Agent = AssistantAgent(
    name="Analyse_Agent",
    system_message=ARTICLE_ANALYSE_AGENT_PROMPT,
    llm_config=llm_config,
)


Script_Analyse_Agent = AssistantAgent(
    name="Analyse_Agent",
    system_message=SCRIPT_ANALYSE_AGENT_PROMPT,
    llm_config=llm_config,
)

Tone_Agent = AssistantAgent(
    name="Tone_Agent",
    system_message=TONE_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Title_agent = AssistantAgent(
    name="Title_Agent",
    system_message=TITLE_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Intro_Hook_Agent = AssistantAgent(
    name="Intro_Hook_Agent",
    system_message=INTRO_HOOK_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Content_Agent = AssistantAgent(
    name="Content_Agent",
    system_message=CONTENT_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Outro_Agent = AssistantAgent(
    name="Outro_Agent",
    system_message=OUTRO_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Formatter_Agent = AssistantAgent(
    name="Formatter_Agent",
    system_message=FORMATTER_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)


Formatter_Agent = AssistantAgent(
    name="Formatter_Agent",
    system_message=FORMATTER_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)


Spoken_English_Agent = AssistantAgent(
    name="Spoken_English_Agent",
    system_message=SPOKEN_ENGLISH_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Executor_Agent = AssistantAgent(
    name="Executor_Agent",
    system_message="You execute a function call and return the results.",
    code_execution_config={"executor": code_executor},
    max_consecutive_auto_reply=1,
)

Executor_Agent1 = AssistantAgent(
    name="Executor_Agent1",
    system_message="You execute a function call and return the results.",
    code_execution_config={"executor": code_executor},
    max_consecutive_auto_reply=1,
)

### These agents are for reel
Intro_Hook_Agent_Reel = AssistantAgent(
    name="Intro_Hook_Agent_Reel",
    system_message=INTRO_HOOK_REEL_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Content_Agent_Reel = AssistantAgent(
    name="Content_Agent_Reel",
    system_message=CONTENT_AGENT_REEL_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Tone_Agent_Reel = AssistantAgent(
    name="Tone_Agent_Reel",
    system_message=TONE_AGENT_REEL_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Reviewer_Agent = AssistantAgent(
    name="Reviewer",
    system_message="""
    You are Reviewer. Your task is to check and validate the facts in the content by Tone_Agent_Reel. 
    If you find the facts are not correct, either correct them or remove them. Also adjust sentent structure if you remove them. Don't do any other changes.
    only return the script in text.
    """,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Scene_Creator_Agent = AssistantAgent(
    name="Scene_Creator_Agent",
    system_message=SCENE_CREATOR_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)


def custom_speaker_selection_func(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Research_Agent
    elif last_speaker is Executor_Agent:
        return Title_agent
    elif last_speaker is Title_agent:
        return Content_Agent
    elif last_speaker is Content_Agent:
        return Tone_Agent
    elif last_speaker is Tone_Agent:
        return Intro_Hook_Agent
    elif last_speaker is Intro_Hook_Agent:
        return Outro_Agent
    elif last_speaker is Outro_Agent:
        return Spoken_English_Agent
    elif last_speaker is Spoken_English_Agent:
        return Formatter_Agent
    elif last_speaker is Formatter_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func4(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Research_Agent
    elif last_speaker is Executor_Agent1:
        return Title_agent
    elif last_speaker is Title_agent:
        return Content_Agent
    elif last_speaker is Content_Agent:
        return Tone_Agent
    elif last_speaker is Tone_Agent:
        return Intro_Hook_Agent
    elif last_speaker is Intro_Hook_Agent:
        return Outro_Agent
    elif last_speaker is Outro_Agent:
        return Spoken_English_Agent
    elif last_speaker is Spoken_English_Agent:
        return Formatter_Agent
    elif last_speaker is Formatter_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func1(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        #     return Transcript_Agent
        # elif last_speaker is Executor_Agent:
        return Analyse_Agent
    elif last_speaker is Analyse_Agent:
        return Title_agent
    elif last_speaker is Title_agent:
        return Content_Agent
    elif last_speaker is Content_Agent:
        return Tone_Agent
    elif last_speaker is Tone_Agent:
        return Intro_Hook_Agent
    elif last_speaker is Intro_Hook_Agent:
        return Outro_Agent
    elif last_speaker is Outro_Agent:
        return Spoken_English_Agent
    elif last_speaker is Spoken_English_Agent:
        return Formatter_Agent
    elif last_speaker is Formatter_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func2(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Article_Analyse_Agent
    elif last_speaker is Article_Analyse_Agent:
        return Title_agent
    elif last_speaker is Title_agent:
        return Content_Agent
    elif last_speaker is Content_Agent:
        return Tone_Agent
    elif last_speaker is Tone_Agent:
        return Intro_Hook_Agent
    elif last_speaker is Intro_Hook_Agent:
        return Outro_Agent
    elif last_speaker is Outro_Agent:
        return Spoken_English_Agent
    elif last_speaker is Spoken_English_Agent:
        return Formatter_Agent
    elif last_speaker is Formatter_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func3(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Script_Analyse_Agent
    elif last_speaker is Script_Analyse_Agent:
        return Title_agent
    elif last_speaker is Title_agent:
        return Content_Agent
    elif last_speaker is Content_Agent:
        return Tone_Agent
    elif last_speaker is Tone_Agent:
        return Intro_Hook_Agent
    elif last_speaker is Intro_Hook_Agent:
        return Outro_Agent
    elif last_speaker is Outro_Agent:
        return Spoken_English_Agent
    elif last_speaker is Spoken_English_Agent:
        return Formatter_Agent
    elif last_speaker is Formatter_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func_reel(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Research_Agent
    elif last_speaker is Executor_Agent:
        return Intro_Hook_Agent_Reel
    elif last_speaker is Intro_Hook_Agent_Reel:
        return Content_Agent_Reel
    elif last_speaker is Content_Agent_Reel:
        return Tone_Agent_Reel
    elif last_speaker is Tone_Agent_Reel:
        return Reviewer_Agent
    elif last_speaker is Reviewer_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func_reel4(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Research_Agent
    elif last_speaker is Executor_Agent1:
        return Intro_Hook_Agent_Reel
    elif last_speaker is Intro_Hook_Agent_Reel:
        return Content_Agent_Reel
    elif last_speaker is Content_Agent_Reel:
        return Tone_Agent_Reel
    elif last_speaker is Tone_Agent_Reel:
        return Reviewer_Agent
    elif last_speaker is Reviewer_Agent:
        pass

    else:
        return "auto"


def custom_speaker_selection_func_reel1(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Analyse_Agent
    elif last_speaker is Analyse_Agent:
        return Intro_Hook_Agent_Reel
    elif last_speaker is Intro_Hook_Agent_Reel:
        return Content_Agent_Reel
    elif last_speaker is Content_Agent_Reel:
        return Tone_Agent_Reel
    elif last_speaker is Tone_Agent_Reel:
        return Reviewer_Agent
    elif last_speaker is Reviewer_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func_reel2(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Article_Analyse_Agent
    elif last_speaker is Article_Analyse_Agent:
        return Intro_Hook_Agent_Reel
    elif last_speaker is Intro_Hook_Agent_Reel:
        return Content_Agent_Reel
    elif last_speaker is Content_Agent_Reel:
        return Tone_Agent_Reel
    elif last_speaker is Tone_Agent_Reel:
        return Reviewer_Agent
    elif last_speaker is Reviewer_Agent:
        pass
    else:
        return "auto"


def custom_speaker_selection_func_reel3(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Script_Analyse_Agent
    elif last_speaker is Script_Analyse_Agent:
        return Intro_Hook_Agent_Reel
    elif last_speaker is Intro_Hook_Agent_Reel:
        return Content_Agent_Reel
    elif last_speaker is Content_Agent_Reel:
        return Tone_Agent_Reel
    elif last_speaker is Tone_Agent_Reel:
        return Reviewer_Agent
    elif last_speaker is Reviewer_Agent:
        pass
    else:
        return "auto"


register_function(
    search_tool,
    caller=Research_Agent,
    executor=Executor_Agent,
    name="search_tool",
    description="Search the web for the given query",
)

register_function(
    search_from_serper,
    caller=Research_Agent,
    executor=Executor_Agent1,
    name="search_from_serper",
    description="Search the web for the given query",
)
