from autogen.cache import Cache
from .agents import user_proxy, groupchat, search_terms_manager
import base64
import os
import requests
from pathlib import Path
import json


def generate_image_prompts(script, video_script_with_timestamps):
    with Cache.disk(cache_seed=44) as cache:
        chat_history = user_proxy.initiate_chat(
            search_terms_manager,
            message=f"""
                This is the content :-
                    script: {script}
                    video_transcript: {video_script_with_timestamps}
                """,
            cache=cache,
            max_turns=1,
        )

    print("agent cost ====>>>", chat_history.cost)

    user_proxy.reset()
    groupchat.reset()
    search_terms_manager.reset()


def read_prompt_generate_image():
    with open("./content/prompts.json", "r") as file:
        prompts = json.load(file)
    prompts = [data["prompts"] for data in prompts.get("data", [])]
    print("prompts", prompts)
    for index, prompt in enumerate(prompts):
        res = generate_image(prompt)
        data = res["data"][0]["base64"]
        decode_base64_image(data, f"./temp/image/{index}.jpg")


def decode_base64_image(base64_string, output_path="./temp/image/image.jpg"):
    """
    Convert a base64 string with escaped characters to an image file.

    Parameters:
    base64_string (str): The base64 encoded image string (can contain escaped characters)
    output_path (str): Path where the decoded image should be saved

    Returns:
    str: Path to the saved image file
    """
    try:
        # Remove any quotes if present
        base64_string = base64_string.strip("\"'")
        # Replace escaped forward slashes with regular forward slashes
        base64_string = base64_string.replace("\/", "/")
        # Decode the base64 string
        image_data = base64.b64decode(base64_string)
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        # Write the binary data to an image file
        with open(output_path, "wb") as f:
            f.write(image_data)
        return output_path
    except base64.binascii.Error:
        raise ValueError("Invalid base64 string")
    except Exception as e:
        raise Exception(f"Error decoding image: {str(e)}")


def generate_image(prompt):
    url = "https://api.freepik.com/v1/ai/text-to-image"
    payload = {
        "prompt": prompt,
        "styling": {"style": "photo"},
        "guidance_scale": 2,
        "image": {"size": "social_story_9_16"},
        "num_images": 1,
        "seed": 42,
    }
    headers = {
        "x-freepik-api-key": "FPSX316065c629b64d8b81891a4e2b9012f6",
        "Content-Type": "application/json ",
    }
    response = requests.request("POST", url, json=payload, headers=headers)
    response.raise_for_status()
    data = response.json()
    return data


# read_prompt_generate_image()


# generate_image_prompts(
#     """Alright mates... ever wonder what it's like to outpace a cheetah on steroids? Brace yourselves, 'cause we're diving into the world of lightning-fast hypercars. And today, we're talking about the reigning king of speed in 2023—the Koenigsegg Jesko Absolut!
# This Swedish beast isn't just fast... it's blisteringly fast, breaking that 310 mph barrier! That's right, the Jesko Absolut isn't just pushing boundaries—it’s smashing 'em. With a twin-turbocharged V8 engine cranking out a wild 1,600 horsepower using E85 biofuel, this masterpiece of engineering isn’t messing around.
# Now, according to Koenigsegg, this is their fastest machine yet. While the speed is theoretical, computer simulations peg it right at the top mark. Who can even conceptualize that? Isn’t that wild?
# But the Jesko Absolut, it's not just about speed; it’s the ultimate showcase of engineering wizardry. It shows how far human ambition stretches. I mean, who would've thought... right? It's completely redefining the hypercar game.
# Mind blown? Let me know if you're ready for more of this adrenaline rush. Like, subscribe... and let's talk more speed limits we can break together!
# Stay tuned for more jaw-dropping rides!
# """,
#     """1
# 00:00:00,360 --> 00:00:04,025
# Alright mate. So, ever wonder what it's like to outpace a cheetah on steroids?

# 2
# 00:00:04,185 --> 00:00:08,065
# Brace yourselves. Cause we're diving into the world of lightning fast hypercars.

# 3
# 00:00:08,225 --> 00:00:11,689
# And today we're talking about the reigning king of speed in 2023.

# 4
# 00:00:11,777 --> 00:00:14,537
# The Koenigsegg Jesko Absolut.

# 5
# 00:00:14,681 --> 00:00:18,129
# This Swedish beast isn't just fast, it's blisteringly fast.

# 6
# 00:00:18,257 --> 00:00:22,441
# Breaking that 310mph barrier. That's right, the Jesko

# 7
# 00:00:22,473 --> 00:00:26,593
# Absolut isn't just pushing boundaries. It's smashing em. With a twin turbocharged

# 8
# 00:00:26,649 --> 00:00:30,106
# V8 engine, cranking out a wild 1,600 horsepower

# 9
# 00:00:30,193 --> 00:00:33,653
# using E85 Bio Fue, this masterpiece of engineering

# 10
# 00:00:33,709 --> 00:00:37,493
# isn't messing around. Now according to Koenigsegg, this is their fastest

# 11
# 00:00:37,549 --> 00:00:40,965
# machine yet. While the speed is theoretical, computer simulations

# 12
# 00:00:41,005 --> 00:00:44,541
# peg it right at the top mark. Who can even conceptualize that?

# 13
# 00:00:44,653 --> 00:00:47,517
# Isn't that wild? But the Jesko Absolute.

# 14
# 00:00:47,581 --> 00:00:51,117
# It's not just about speed. It's the ultimate showcase of engineering

# 15
# 00:00:51,181 --> 00:00:54,509
# wizardry. It shows how far human ambition stretches.

# 16
# 00:00:54,637 --> 00:00:58,573
# I mean, who would have thought, right? It's completely redefining the hypercar

# 17
# 00:00:58,629 --> 00:01:02,139
# game. Mind blown. Let me know if you're ready for more of this adrenaline

# 18
# 00:01:02,187 --> 00:01:05,707
# rush. Like subscribe and let's talk more speed limits we can

# 19
# 00:01:05,731 --> 00:01:08,475
# break together. Stay tuned for more jaw dropping rides.

# """,
# )
