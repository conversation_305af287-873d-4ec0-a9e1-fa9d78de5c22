from autogen.cache import Cache
from .agents import user_proxy, groupchat, search_terms_manager


def generate_search_terms(script, video_script_with_timestamps):
    with Cache.disk(cache_seed=44) as cache:
        chat_history = user_proxy.initiate_chat(
            search_terms_manager,
            message=f"""
                This is the content :-
                    script: {script}
                    video_transcript: {video_script_with_timestamps}
                """,
            cache=cache,
            max_turns=1,
        )

    print("agent cost ====>>>", chat_history.cost)

    user_proxy.reset()
    groupchat.reset()
    search_terms_manager.reset()
