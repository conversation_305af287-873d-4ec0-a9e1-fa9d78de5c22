import os
import sys
from typing import Annotated


from autogen.agentchat import (
    Agent,
    AssistantAgent,
    GroupChat,
    UserProxyAgent,
    register_function,
)

from autogen.coding import LocalCommandLineCodeExecutor

from app.service.video.utils import create_and_save_clips
from app.service.file_saver import store_clip_data

from .prompts import (
    ANALYSE_AGENT_PROMPT,
    VIDEO_EDITOR_AGENT_PROMPT,
    CLIP_INSERT_AGENT_PROMPT,
    CLIP_FILTER_AGENT_PROMPT,
)


from model_config import config_list

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


llm_config = {
    "config_list": config_list,
    "timeout": 120,
    "cache_seed": None,
}


code_executor = LocalCommandLineCodeExecutor(work_dir="coding")


user_proxy = UserProxyAgent(
    name="Admin",
    system_message="""
    A human admin who checks that agents are working properly and <PERSON><PERSON><PERSON><PERSON><PERSON> the process when desired output is attained.
    """,
    code_execution_config=False,
)

Analyzer_Agent = AssistantAgent(
    name="Analyzer_Agent",
    system_message=ANALYSE_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)

Video_Editor_Agent = AssistantAgent(
    name="Video_Editor_Agent",
    system_message=VIDEO_EDITOR_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
    is_termination_msg=lambda x: x.get("content", "")
    and x.get("content", "").rstrip().endswith("TERMINATE"),
)


Clip_Filter_Agent = AssistantAgent(
    name="Clip_Filter_Agent",
    system_message=CLIP_FILTER_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)


Create_Sub_clips_Agent = AssistantAgent(
    name="Create_Sub_clips_Agent",
    system_message="""
    Your task is to take the input video clips data and return a list of tuples representing the start and end times (in seconds) of each clip and pass this data and video_path, video_id in Executor_Agent. 
    The output should be in the following format:
    Results: [(start_time_1, end_time_1), (start_time_2, end_time_2), ...]

    Do not include the words "startTime", "start", "endTime", or "end" in the response. Always return the times in seconds as a list of tuples. 
    Pass this Results in Executor_Agent as input so it will execute the function.
    """,
    llm_config=llm_config,
)


Executor_Agent = AssistantAgent(
    name="Executor_Agent",
    system_message="Get the results data from Create_Sub_clips_Agent and You execute a function call and return the results.",
    code_execution_config={"executor": code_executor},
    max_consecutive_auto_reply=1,
)

Clip_Insertion_Output_Agent = AssistantAgent(
    name="Clip_Insertion_Output_Agent",
    system_message=CLIP_INSERT_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
    is_termination_msg=lambda x: x.get("content", "")
    and x.get("content", "").rstrip().endswith("TERMINATE"),
)

Data_Writer_Agent = AssistantAgent(
    name="Data_Writer_Agent",
    llm_config=llm_config,
    system_message="""you are the data writer agent, Your task to get the data from Clip_Insertion_Output_Agent store that data in a JSON file using the provided function write_file so pass the list as input.""",
)


@user_proxy.register_for_execution()
@Data_Writer_Agent.register_for_llm(description="Get the data and save in to json file")
def write_file(
    data: Annotated[list, "The response from the LLM"],
) -> str:
    store_clip_data(data=data)
    return "success"


Critic = AssistantAgent(
    name="Critic",
    system_message="""
    Critic. Double-check data from other agents and provide feedback. Check whether the clips data store.
    Reply "TERMINATE" in the end when everything is done.
    """,
    llm_config=llm_config,
)


register_function(
    create_and_save_clips,
    caller=Create_Sub_clips_Agent,
    executor=Executor_Agent,
    name="create_and_save_clips",
    description="create the sub clips of the video using sub clips time stamps",
)


def custom_speaker_selection_func(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Analyzer_Agent
    elif last_speaker is Analyzer_Agent:
        return Video_Editor_Agent
    elif last_speaker is Video_Editor_Agent:
        return Clip_Filter_Agent
    elif last_speaker is Clip_Filter_Agent:
        return Clip_Insertion_Output_Agent
    elif last_speaker is Clip_Insertion_Output_Agent:
        store_clip_data(data=last_speaker.last_message()["content"])
        last_speaker.reset()
    else:
        return "auto"


def custom_speaker_selection_func1(last_speaker: Agent, groupchat: GroupChat):
    groupchat.messages
    if last_speaker is user_proxy:
        return Create_Sub_clips_Agent
    elif last_speaker is Create_Sub_clips_Agent:
        return Executor_Agent
    elif last_speaker is Executor_Agent:
        pass
    else:
        return "auto"
