import os
import sys


from autogen.agentchat import (
    GroupChat,
    GroupChatManager,
)

from autogen.cache import Cache


from .agents import (
    user_proxy,
    Ana<PERSON><PERSON>_Agent,
    Video_Editor_Agent,
    Clip_Filter_Agent,
    Clip_Insertion_Output_Agent,
    Create_Sub_clips_Agent,
    Executor_Agent,
    custom_speaker_selection_func,
    custom_speaker_selection_func1,
)

from model_config import config_list

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


llm_config = {
    "config_list": config_list,
    "timeout": 120,
    "cache_seed": None,
}


def generate_sub_clips(
    video_id, video_clips_details, script, script_with_timestamp, video_file_path
):

    inputs = f"""
        1. this is the youtube video clips details:
            content of video clips : {video_clips_details},
        2. this is the video script for that want to create using sub clips: 
            video script: {script}
            script with subtitles: {script_with_timestamp}
    """

    groupchat = GroupChat(
        agents=[
            user_proxy,
            Analyzer_Agent,
            Video_Editor_Agent,
            Clip_Filter_Agent,
            Clip_Insertion_Output_Agent,
        ],
        messages=[],
        max_round=10,
        speaker_selection_method=custom_speaker_selection_func,
    )

    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config={"config_list": config_list, "cache_seed": None},
        is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
    )

    groupchat1 = GroupChat(
        agents=[
            user_proxy,
            Create_Sub_clips_Agent,
            Executor_Agent,
        ],
        messages=[],
        max_round=5,
        speaker_selection_method=custom_speaker_selection_func1,
    )

    manager1 = GroupChatManager(
        groupchat=groupchat1,
        llm_config={"config_list": config_list, "cache_seed": None},
        is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
    )

    input1 = f"This is the clips data: {video_clips_details} and the video path: {video_file_path} and video_id: {video_id}"

    with Cache.disk(cache_seed=44) as cache:
        chat_history = user_proxy.initiate_chats(
            [
                {
                    "recipient": manager,
                    "message": inputs,
                    "cache": cache,
                    "max_turns": 1,
                },
                {
                    "recipient": manager1,
                    "message": input1,
                    "cache": cache,
                    "max_turns": 1,
                },
            ]
        )
    user_proxy.reset()
    groupchat.reset()
    manager.reset()
    groupchat1.reset()
    manager1.reset()

    print("agent cost 1 ====>>>", chat_history[0].cost)
    print("agent cost 2 ====>>>", chat_history[1].cost)
