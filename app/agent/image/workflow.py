import os
import sys
from app.service.serper import images_search
from app.service.web_scrap import scrape_article
from app.service.file_saver import store_image_data

from autogen.agentchat import (
    GroupChat,
    GroupChatManager,
    Agent,
)

from .agents import ImageAgentService

from autogen.cache import Cache
from app.service.llm.llm_call import get_image_search_topic
from app.service.llm.multi_model_call import get_images_details

from model_config import config_list

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

llm_config = {
    "config_list": config_list,
    "timeout": 120,
    "cache_seed": None,
}

agent_service = ImageAgentService()
res = agent_service.user_proxy


def generate_images(video_id, topic, script, script_with_timestamp, link=None):

    agent_service = ImageAgentService()

    search_words = get_image_search_topic(topic)

    if link is not None:
        images = scrape_article(link)["images"]
        if len(images) == 0:
            images = images_search(f"{search_words} latest images")
    else:
        images = images_search(f"{search_words} latest images")

    images_details, image_links = get_images_details(video_id, script, images)

    inputs = f"""
        1. this is the images details:
            content of images : {images_details} with the image number
        2. this is the video script for that want to create by attaching these images: 
            video script: {script}
            script with subtitles: {script_with_timestamp}
    """

    def custom_speaker_selection_func(last_speaker: Agent, groupchat: GroupChat):
        groupchat.messages
        if last_speaker is agent_service.user_proxy:
            return agent_service.Analyzer_Agent
        elif last_speaker is agent_service.Analyzer_Agent:
            return agent_service.Video_Editor_Agent
        elif last_speaker is agent_service.Video_Editor_Agent:
            return agent_service.Image_Filter_Agent
        elif last_speaker is agent_service.Image_Filter_Agent:
            return agent_service.Image_Insertion_Output_Agent
        elif last_speaker is agent_service.Image_Insertion_Output_Agent:
            store_image_data(data=last_speaker.last_message()["content"])
            last_speaker.reset()
        else:
            return "auto"

    groupchat = GroupChat(
        agents=[
            agent_service.user_proxy,
            agent_service.Analyzer_Agent,
            agent_service.Video_Editor_Agent,
            agent_service.Image_Filter_Agent,
            agent_service.Image_Insertion_Output_Agent,
        ],
        messages=[],
        max_round=10,
        speaker_selection_method=custom_speaker_selection_func,
    )

    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config={"config_list": config_list, "cache_seed": None},
        is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
    )

    with Cache.disk(cache_seed=44) as cache:
        chat_history = agent_service.user_proxy.initiate_chat(
            manager,
            message=inputs,
            cache=cache,
            max_turns=1,
            summary_method="reflection_with_llm",
            summary_args={
                "summary_prompt": "Return the last agent response message in text format"
            },
        )

    agent_service.user_proxy.reset()
    groupchat.reset()
    manager.reset()

    print("agent cost ====>>>", chat_history.cost)

    print("summery", chat_history.summary)

    return image_links


def generate_and_save_images(script, script_with_timestamp, images):
    agent_service = ImageAgentService()

    images_details = get_images_details(script, images)

    inputs = f"""
        1. this is the images details:
            content of images : {images_details} with the image number
        2. this is the video script for that want to create by attaching these images:
            video script: {script}
            script with subtitles: {script_with_timestamp}
    """

    def custom_speaker_selection_func(last_speaker: Agent, groupchat: GroupChat):
        groupchat.messages
        if last_speaker is agent_service.user_proxy:
            return agent_service.Analyzer_Agent
        elif last_speaker is agent_service.Analyzer_Agent:
            return agent_service.Video_Editor_Agent
        elif last_speaker is agent_service.Video_Editor_Agent:
            return agent_service.Image_Filter_Agent
        elif last_speaker is agent_service.Image_Filter_Agent:
            return agent_service.Image_Insertion_Output_Agent
        elif last_speaker is agent_service.Image_Insertion_Output_Agent:
            store_image_data(data=last_speaker.last_message()["content"])
            last_speaker.reset()
        else:
            return "auto"

    groupchat = GroupChat(
        agents=[
            agent_service.user_proxy,
            agent_service.Analyzer_Agent,
            agent_service.Video_Editor_Agent,
            agent_service.Image_Filter_Agent,
            agent_service.Image_Insertion_Output_Agent,
        ],
        messages=[],
        max_round=10,
        speaker_selection_method=custom_speaker_selection_func,
    )

    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config={"config_list": config_list, "cache_seed": None},
        is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
    )

    with Cache.disk(cache_seed=44) as cache:
        chat_history = agent_service.user_proxy.initiate_chat(
            manager,
            message=inputs,
            cache=cache,
            max_turns=1,
            summary_method="reflection_with_llm",
            summary_args={
                "summary_prompt": "Return the last agent response message in text format"
            },
        )

    agent_service.user_proxy.reset()
    groupchat.reset()
    manager.reset()

    print("agent cost ====>>>", chat_history.cost)

    print(chat_history.summary)

    return chat_history.summary
