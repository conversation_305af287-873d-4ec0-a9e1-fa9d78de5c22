import os
import sys

from typing import Annotated


from .prompt import (
    ANALYZER_AGENT_PROMPT,
    VIDEO_EDITOR_AGENT_PROMPT,
    IMAGE_FILTER_AGENT_PROMPT,
    IMAGE_INSERTION_AGENT_PROMPT,
)

from autogen.agentchat import (
    AssistantAgent,
    UserProxyAgent,
)

from app.service.file_saver import store_image_data

from autogen.coding import LocalCommandLineCodeExecutor

from model_config import config_list

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


llm_config = {
    "config_list": config_list,
    "timeout": 120,
    "cache_seed": None,
}


code_executor = LocalCommandLineCodeExecutor(work_dir="coding")


class ImageAgentService:

    def __init__(self):

        self.user_proxy = UserProxyAgent(
            name="Admin",
            system_message="""
            A human admin who checks that agents are working properly and TERMINA<PERSON> the process when desired output is attained.
            """,
            code_execution_config=False,
        )

        self.Analyzer_Agent = AssistantAgent(
            name="Analyzer_Agent",
            system_message=ANALYZER_AGENT_PROMPT,
            llm_config={"config_list": config_list, "cache_seed": None},
        )

        self.Video_Editor_Agent = AssistantAgent(
            name="Video_Editor_Agent",
            system_message=VIDEO_EDITOR_AGENT_PROMPT,
            llm_config={"config_list": config_list, "cache_seed": None},
            is_termination_msg=lambda x: x.get("content", "")
            and x.get("content", "").rstrip().endswith("TERMINATE"),
        )

        self.Image_Filter_Agent = AssistantAgent(
            name="Image_Filter_Agent",
            system_message=IMAGE_FILTER_AGENT_PROMPT,
            llm_config={"config_list": config_list, "cache_seed": None},
        )

        self.Image_Insertion_Output_Agent = AssistantAgent(
            name="Image_Insertion_Output_Agent",
            system_message=IMAGE_INSERTION_AGENT_PROMPT,
            llm_config={"config_list": config_list, "cache_seed": None},
            is_termination_msg=lambda x: x.get("content", "")
            and x.get("content", "").rstrip().endswith("TERMINATE"),
        )

        self.Data_Writer_Agent = AssistantAgent(
            name="Data_Writer_Agent",
            llm_config=llm_config,
            system_message="""you are the data writer agent, Your task to get the data from Image_Insertion_Output_Agent store that data in a JSON file using the provided function write_file so pass the list as input.""",
        )

        self.Critic = AssistantAgent(
            name="Critic",
            system_message="""
            Critic. Double-check data from other agents and provide feedback. Check whether the images data store.
            Reply "TERMINATE" in the end when everything is done.
            """,
            llm_config=llm_config,
        )

        @self.user_proxy.register_for_execution()
        @self.Data_Writer_Agent.register_for_llm(
            description="Get the data and save in to json file"
        )
        def write_file(
            data: Annotated[list, "The response from the LLM"],
        ) -> str:
            store_image_data(data=data)
            return "success"
