from termcolor import colored
from datetime import datetime, timedelta
from werkzeug.exceptions import BadRequest
import json
import time
from bson import ObjectId
from app.agent.script.workflow import ScriptWork<PERSON>low

from .web_scrap import scrape_article
from .llm.llm_call import get_article_summary
from .utils import check_valid_url
from app.loggers.monitoring import sentry_client
from .serper import news_search
from app.constant.script_enums import ScriptType
from app.repositories.script_repository import ScriptRepository
from app.repositories.user_setting_repository import UserSettingRepository
from app.repositories.trending_repository import TrendingRepository

script_repository = ScriptRepository()
user_setting_repository = UserSettingRepository()
trending_repository = TrendingRepository()
script_workflow = ScriptWorkFlow()


def generate_script(
    user_id,
    topic,
    video_type,
    keywords,
    script_type,
    add_brand,
    music_media_id,
    link=None,
    video_link=None,
    queue=None,
):
    try:

        script_data = script_repository.get_script_of_type_and_title(
            video_type,
            topic,
        )

        if script_data:
            script_data = script_repository.to_dict(script_data)
            if queue:
                queue.put(
                    {
                        "index": None,
                        "content": None,
                        "script": script_data,
                        "status": "completed",
                    }
                )
                time.sleep(1)
                queue.put("[DONE]")
                script_workflow.remove_queue()
            return script_data

        script_functions = {
            ScriptType.VIDEO: script_workflow.script_using_video,
            ScriptType.BLOG: script_workflow.script_using_blog,
            ScriptType.TOPIC: script_workflow.script_using_topic,
            ScriptType.SCRIPT: script_workflow.script_using_script,
        }

        script_function = script_functions.get(script_type)
        if not script_function:
            raise ValueError(f"Invalid script type: {script_type}")

        args = [topic, video_type, keywords]

        if link:
            args.insert(3, link)

        if video_link:
            args.insert(3, None)
            args.insert(4, video_link)

        if queue:
            script_workflow.set_queue(queue)

        script = script_function(*args)

        # scenes = json.loads(generate_script_contents(script))

        if music_media_id:
            music_media_id = ObjectId(music_media_id)

        script_data = {
            "user_id": user_id,
            "title": topic,
            "script": script,
            "type": script_type,
            "keywords": keywords,
            "video_type": video_type,
            "video_link": video_link,
            "music_media_id": music_media_id,
            "add_brand": add_brand,
            # "scenes": scenes.get("scenes"),
        }

        if link:
            script_data["link"] = link

        id = script_repository.create_script(script_data)

        script_data = script_repository.get_script(id)
        script_data = script_repository.to_dict(script_data)

        if queue:
            queue.put(
                {
                    "index": None,
                    "content": None,
                    "script": script_data,
                    "status": "completed",
                }
            )
            time.sleep(1)
            queue.put("[DONE]")
            script_workflow.remove_queue()

        return script_data

    except Exception as e:
        print(colored(f"An error occurred: {e}", "red"))
        sentry_client.capture_exception(e)
        raise


def generate_script_using_video_link(
    user_id, topic, video_type, link, video_link, keywords, add_brand, music_media_id
):
    return generate_script(
        user_id,
        topic,
        video_type,
        keywords,
        ScriptType.VIDEO,
        add_brand,
        music_media_id,
        link,
        video_link,
    )


def generate_script_using_blog(
    user_id, topic, video_type, link, keywords, add_brand, music_media_id
):
    if not check_valid_url(link):
        raise BadRequest("Invalid url: {0}".format(link))
    return generate_script(
        user_id,
        topic,
        video_type,
        keywords,
        ScriptType.BLOG,
        add_brand,
        music_media_id,
        link,
    )


def generate_script_using_topic(
    user_id, topic, video_type, keywords, add_brand, music_media_id
):
    return generate_script(
        user_id,
        topic,
        video_type,
        keywords,
        ScriptType.TOPIC,
        add_brand,
        music_media_id,
    )


def generate_script_using_script(
    user_id, topic, video_type, keywords, add_brand, music_media_id
):
    return generate_script(
        user_id,
        topic,
        video_type,
        keywords,
        ScriptType.SCRIPT,
        add_brand,
        music_media_id,
    )


def regenerate_script(user_id, script_data, topic, video_type, link, keywords):
    try:

        script_id = str(script_data["_id"])

        script_functions = {
            ScriptType.VIDEO: script_workflow.script_using_video,
            ScriptType.BLOG: script_workflow.script_using_blog,
            ScriptType.TOPIC: script_workflow.script_using_topic,
            ScriptType.SCRIPT: script_workflow.script_using_script,
        }

        script_function = script_functions.get(script_data["type"])

        if not script_function:
            raise ValueError(f"Invalid script type: {script_data["type"]}")

        args = [topic, video_type, keywords]

        if link:
            args.insert(2, link)

        script = script_function(*args)

        script_data = {
            "user_id": user_id,
            "title": topic,
            "script": script,
            "keywords": keywords,
            "video_type": video_type,
        }

        if link:
            script_data["link"] = link

        script_repository.update_script(script_id, script_data)

        script_data = script_repository.get_script(script_id)

        return script_repository.to_dict(script_data)

    except Exception as e:
        print(colored(f"An error occurred: {e}", "red"))
        return None


def get_user_script(user_id, script_id):
    try:
        script = script_repository.get_script(script_id)
        if script and script.get("user_id") == user_id:
            return script
        return None
    except Exception as e:
        print(colored(f"Error retrieving script: {e}", "red"))
        return None


def get_all_user_scripts(user_id, page, limit, filter):
    try:
        scripts, total_count = script_repository.get_all_scripts_by_user(
            user_id, page, limit, filter
        )
        return scripts, total_count
    except Exception as e:
        print(colored(f"Error retrieving user scripts: {e}", "red"))
        return None, 0


def update_script(user_id, script_id, update_data):
    try:
        existing_script = script_repository.get_script(script_id)

        if not existing_script or str(existing_script.get("user_id")) != user_id:
            return None

        updated_script = {**existing_script, **update_data}
        script_repository.update_script(script_id, updated_script)
        return script_repository.get_script(script_id)
    except Exception as e:
        print(colored(f"Error updating script: {e}", "red"))
        return None


def delete_script(user_id, script_id):
    try:
        existing_script = script_repository.get_script(script_id)
        if not existing_script or existing_script.get("user_id") != user_id:
            return False

        script_repository.delete_script(script_id)
        return True
    except Exception as e:
        print(colored(f"Error deleting script: {e}", "red"))
        return False


def get_trending(user_id, page, per_page):

    try:

        user_setting = user_setting_repository.get_user_settings_by_user(user_id)

        user_domain = ["Science & Technology"]

        if len(user_setting) != 0:

            user_domain = user_setting[0].get(
                "user_interest_domain", ["Science & Technology"]
            )

        queries = []

        for domain in user_domain:

            queries.append(f"latest and trending news in {domain}")

        # Add the new filter for the "field" to be equal to either "ai" or "blockchain"
        field_filter = {"field": {"$in": user_domain}}

        trending_count = trending_repository.get_trending_count(field_filter)

        result = trending_repository.get_trendings_paginated(
            page, per_page, user_domain
        )

        current_time = datetime.utcnow()

        if trending_count < 5 or (
            datetime.fromisoformat(result["trendings"][0]["updated_at"])
            + timedelta(hours=24)
            < current_time
        ):

            response = news_search(queries)

            for index, data in enumerate(response):

                news = data["news"]

                field = user_domain[index]

                for data in news:
                    trending_repository.create_trending(
                        {
                            "title": data["title"],
                            "link": data["link"],
                            "snippet": data["snippet"],
                            "date": data["date"],
                            "source": data["source"],
                            "image_url": data.get("imageUrl", None),
                            "field": field,
                        }
                    )

            result = trending_repository.get_trendings_paginated(
                page, per_page, user_domain
            )

        return {
            "trendings": result["trendings"],
            "page": result["page"],
            "per_page": result["per_page"],
            "total_count": result["total_count"],
            "total_pages": result["total_pages"],
        }

    except Exception as e:
        print(colored(f"Error get_trending: {e}", "red"))
        raise e


def get_similar_trending(domain, page, per_page):

    try:

        queries = []

        queries.append(f"latest and trending news in {domain}")

        # Add the new filter for the "field" to be equal to either "ai" or "blockchain"
        field_filter = {"field": {"$in": [domain]}}

        trending_count = trending_repository.get_trending_count(field_filter)

        result = trending_repository.get_trendings_paginated(page, per_page, [domain])

        if trending_count < 5:
            response = news_search(queries)

            for index, data in enumerate(response):

                news = data["news"]

                field = domain

                for data in news:
                    trending_repository.create_trending(
                        {
                            "title": data["title"],
                            "link": data["link"],
                            "snippet": data["snippet"],
                            "date": data["date"],
                            "source": data["source"],
                            "image_url": data.get("imageUrl", None),
                            "field": field,
                        }
                    )

            result = trending_repository.get_trendings_paginated(
                page, per_page, [domain]
            )

        return {
            "trendings": result["trendings"],
            "page": result["page"],
            "per_page": result["per_page"],
            "total_count": result["total_count"],
            "total_pages": result["total_pages"],
        }

    except Exception as e:
        print(colored(f"Error get_trending: {e}", "red"))
        raise e


def get_topic_details(topic_id):
    try:

        topic_data = trending_repository.get_trending_by_id(topic_id)

        if not topic_data:
            raise Exception("Not found topic")

        if not topic_data.get("summary"):

            scrap_result = scrape_article(topic_data["link"])

            summary = get_article_summary(scrap_result["text"])

            topic_data = trending_repository.update_trending(
                str(topic_data["_id"]),
                {
                    "summary": summary,
                    "images": scrap_result["images"],
                    "videos": scrap_result["videos"],
                },
            )

        return trending_repository.to_dict(topic_data)

    except Exception as e:
        print(colored(f"Error get_trending: {e}", "red"))
        raise e
