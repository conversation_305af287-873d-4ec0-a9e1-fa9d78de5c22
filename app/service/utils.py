from urllib.parse import urlparse, unquote
import os
import sys

import logging

import requests
from termcolor import colored
import re
import base64
import uuid
import hashlib
import PyPDF2
import io
import hmac
import secrets


# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def save_video(video_url: str, video_id=uuid.uuid4(), directory: str = "./temp") -> str:
    """
    Saves a video from a given URL and returns the path to the video.
    If the file already exists, it doesn't rewrite it.

    Args:
        video_url (str): The URL of the video to save.
        video_id (uuid.UUID): Unique identifier for the video file. Defaults to a new UUID.
        directory (str): The path of the temporary directory to save the video to.

    Returns:
        str: The path to the saved video.
    """
    video_path = f"{directory}/{video_id}.mp4"

    if not os.path.exists(video_path):
        # Ensure the directory exists
        os.makedirs(directory, exist_ok=True)

        # Download and save the video
        response = requests.get(video_url)
        response.raise_for_status()  # Raise an exception for bad responses

        with open(video_path, "wb") as f:
            f.write(response.content)
        print(f"Video saved to {video_path}")
    else:
        print(f"Video already exists at {video_path}")

    return video_path


def clean_dir(path: str) -> None:
    """
    Removes every file in a directory.

    Args:
        path (str): Path to directory.

    Returns:
        None
    """
    try:
        for file in os.listdir(path):
            file_path = os.path.join(path, file)
            os.remove(file_path)
            logger.info(f"Removed file: {file_path}")

        logger.info(colored(f"Cleaned {path} directory", "green"))
    except Exception as e:
        logger.error(f"Error occurred while cleaning directory {path}: {str(e)}")


def create_dir(path: str) -> None:
    try:
        if not os.path.exists(path):
            os.mkdir(path)
            logger.info(f"Created directory: {path}")
    except Exception as e:
        logger.error(f"Error occurred while creating directory {path}: {str(e)}")


def generate_numeric_otp():
    # Generate a random integer between 0 and 999999
    otp = secrets.randbelow(1000000)
    # Convert to string and pad with leading zeros if necessary
    return f"{otp:06d}"


def is_valid_url(url):
    """
    Check if the given string is a valid URL.
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False


def save_data_url(data_url, filename):
    # Extract the base64-encoded data and MIME type from the Data URL
    _, data = data_url.split(",", 1)
    mime_type = data_url.split(";")[0].split(":")[1]

    # Determine the file extension based on the MIME type
    if mime_type == "image/gif":
        extension = ".gif"
    elif mime_type == "image/jpeg":
        extension = ".jpg"
    elif mime_type == "image/png":
        extension = ".png"
    else:
        extension = ".dat"

    # Decode the base64 data
    decoded_data = base64.b64decode(unquote(data))

    # Save the decoded data to an image file
    filename = filename + extension
    with open(filename, "wb") as f:
        f.write(decoded_data)


def extract_pdf_content_from_url(pdf_url):
    """
    Extracts the content from a PDF file hosted on a URL.

    Args:
        pdf_url (str): The URL of the PDF file.

    Returns:
        str: The content of the PDF file.
    """
    # Send a GET request to the PDF URL
    response = requests.get(pdf_url)

    # Check if the request was successful
    if response.status_code == 200:
        # Open the PDF file from the response content
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(response.content))

        # Initialize an empty string to store the content
        content = ""

        # Iterate over each page in the PDF
        for page_num in range(len(pdf_reader.pages)):
            # Get the current page object
            page = pdf_reader.pages[page_num]

            # Extract the text from the page and add it to the content string
            content += page.extract_text()

        index = content.find("References")

        if index != -1:
            # Extract the text before "hellow"
            content = content[:index].strip()

        return content

    else:
        print(f"Error: Failed to fetch PDF from {pdf_url}")
        return None


def split_long_string(input_string):
    if len(input_string) <= 2000:
        return [input_string]
    else:
        substrings = []
        start = 0
        end = 2000
        while start < len(input_string):
            substrings.append(input_string[start:end])
            start = end
            end += 2000
            if end > len(input_string):
                end = len(input_string)
        return substrings


def generate_hmac(secret_key, expires):

    # url info
    resource = "/api/v2/videos/search"

    hmacBuilder = hmac.new(
        bytearray(secret_key + expires, "utf-8"),
        resource.encode("utf-8"),
        hashlib.sha256,
    )
    hmacHex = hmacBuilder.hexdigest()

    return hmacHex


def download_image(image_url, save_path):
    try:
        if not os.path.exists(save_path):
            # Download the image
            response = requests.get(image_url)
            response.raise_for_status()  # Check if the request was successful
            # Save the image to the specified path
            with open(save_path, "wb") as file:
                file.write(response.content)
        return save_path
    except requests.exceptions.RequestException as e:
        print(f"Failed to download the image: {e}")
    except OSError as e:
        print(f"Failed to delete the image: {e}")


def remove_file(save_path):
    # Delete the image file after use
    os.remove(save_path)
    print(f"file {save_path} deleted after use")


def check_link_validity(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.status_code == 200
    except requests.RequestException:
        return False


def check_env_vars() -> None:
    """
    Checks if the necessary environment variables are set.

    Returns:
        None

    Raises:
        SystemExit: If any required environment variables are missing.
    """
    try:
        required_vars = ["PEXELS_API_KEY", "IMAGEMAGICK_BINARY"]
        missing_vars = [
            var + os.getenv(var)
            for var in required_vars
            if os.getenv(var) is None or (len(os.getenv(var)) == 0)
        ]

        if missing_vars:
            missing_vars_str = ", ".join(missing_vars)
            logger.error(
                colored(
                    f"The following environment variables are missing: {missing_vars_str}",
                    "red",
                )
            )
            logger.error(
                colored(
                    "Please consult 'EnvironmentVariables.md' for instructions on how to set them.",
                    "yellow",
                )
            )
            sys.exit(1)  # Aborts the program
    except Exception as e:
        logger.error(f"Error occurred while checking environment variables: {str(e)}")
        sys.exit(1)  # Aborts the program if an unexpected error occurs


def check_valid_url(url: str) -> bool:
    """
    Check if the given string is a valid URL.

    Args:
    url (str): The URL to validate.

    Returns:
    bool: True if the URL is valid, False otherwise.
    """

    # Regular expression pattern for URL validation
    pattern = re.compile(
        r"^(?:http|ftp)s?://"  # http:// or https://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    # Check if the URL matches the pattern
    if re.match(pattern, url) is not None:
        try:
            # Additional check using urlparse
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except ValueError:
            return False
    return False


def download_font(url, output_dir="./temp/font"):
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get the filename from the URL
    filename = os.path.basename(urlparse(url).path)

    # If the filename doesn't end with .ttf, add it
    if not filename.lower().endswith(".ttf"):
        filename += ".ttf"

    # Full path for the output file
    output_path = os.path.join(output_dir, filename)

    # Download the file
    response = requests.get(url)

    if response.status_code == 200:
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Font downloaded successfully: {output_path}")
        return output_path
    else:
        print(f"Failed to download font. Status code: {response.status_code}")
        return None
