from bson import ObjectId
from app.repositories.usage_repository import UsageRepository


class UsageService:
    def __init__(self):
        self.usage_repository = UsageRepository()
        # self.create_usage(
        #     "6685271890718646fb064411", {"credits_used": 1, "seconds_used": 59}
        # )

    def create_usage(self, user_id, data):
        try:
            data["user_id"] = ObjectId(user_id)
            self.usage_repository.create_usage(data)
        except Exception as e:
            print("Error getting create_usage", str(e))

    def get_usages(self, user_id, input_data):
        try:
            return self.usage_repository.get_stats(user_id, input_data)
        except Exception as e:
            print("<PERSON>rror getting stats for user", str(e))
            raise e

    def get_total_counts(self, user_id):
        return self.usage_repository.get_counts(user_id)
