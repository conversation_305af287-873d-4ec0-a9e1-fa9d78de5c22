import json
import logging
import os
from bson import ObjectId
from app.exceptions.subscription_exceptions import SubscriptionInactiveError
from ..audio import AudioService
from ..tavus import TavusService
from ...repositories.avatar_repository import AvatarRepository
from ...repositories.video_repository import VideoRepository
from ...repositories.script_repository import ScriptRepository
from ...repositories.user_repository import AuthRepository
from ...repositories.brand_repository import BrandRepository
from termcolor import colored
from app.constant.notification_enums import NotificationType
from app.constant.video_enums import VideoType, VideoViewType, VideoStatus, VideoQuality
from app.agent.video_clip.workflow import generate_sub_clips
from app.agent.image.workflow import generate_images
from app.agent.search_terms.workflow import generate_search_terms
from app.constant.script_enums import ScriptType
from app.constant.user_enums import EmailType
from app.loggers.monitoring import sentry_client
from ..youtube import download_youtube_video
from werkzeug.exceptions import NotFound
from ..llm.multi_model_call import get_video_clips_details
from ..llm.llm_call import get_title, get_script_text, get_intro_placement_time
from .stock_video import search_for_stock_videos_on_story_block, search_for_stock_videos
from ..utils import save_video, remove_file, download_image
from .video_template import long_video1, test_long_video
from .reel_template import create_reel_video1, create_reel_video
from .utils import stop_ffmpeg_processes
from app.service.usage import UsageService
from app.helper.mailer import EmailService
from ..notification import NotificationService


from .video_edit import upscale_video, create_thumbnail

from app.exceptions.video_exceptions import VideoCreationFailed

from app.helper.s3_manager import S3Uploader

from ..assembly_ai import create_subtitle_file

from config import FRONTEND_ENDPOINT

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VideoService:
    def __init__(self):
        self.audio_service = AudioService()
        self.tavus_service = TavusService()
        self.avatar_repository = AvatarRepository()
        self.video_repository = VideoRepository()
        self.script_repository = ScriptRepository()
        self.s3_service = S3Uploader()
        self.user_repository = AuthRepository()
        self.email_service = EmailService()
        self.brand_repository = BrandRepository()
        self.notification_service = NotificationService()
        self.usage_service = UsageService()

    def send_email(self, user_id, video_id, email_type, metadata=None):

        user_data = self.user_repository.find_user_by_id(user_id)

        link = (
            f"{FRONTEND_ENDPOINT}/dashboard/my-projects/{video_id}?videoId={video_id}"
        )

        self.email_service.send_email_with_template(
            user_data["name"], user_data["email"], link, email_type, metadata
        )

    def _extract_video_data(self, video_data):
        return (
            video_data["user_id"],
            video_data["script_id"],
            video_data.get("voice_id", None),
        )

    def generate_video_clips_details(
        self,
        video_data,
        script_data,
        task,
        percentage=2,
    ):
        try:
            video_id = str(video_data["_id"])
            video_link = script_data.get("video_link")

            video_file_path = video_data.get("video_file_path", None)

            if script_data.get("type") == ScriptType.VIDEO.value:

                task.update_state(
                    state="PROGRESS",
                    meta={"progress": percentage, "status": "fetching clips ..."},
                )

                if video_file_path:
                    if not os.path.exists(video_file_path):
                        if video_link:
                            video_file_path = save_video(
                                video_link,
                                video_id="video",
                                directory=f"./temp/{video_id}",
                            )
                        else:
                            video_file_path = download_youtube_video(
                                script_data["link"], f"./temp/{video_id}/"
                            )
                else:
                    if video_link:
                        video_file_path = save_video(
                            video_link,
                            video_id="video",
                            directory=f"./temp/{video_id}",
                        )
                    else:
                        video_file_path = download_youtube_video(
                            script_data["link"], f"./temp/{video_id}/"
                        )

                if not video_file_path:
                    raise Exception("Unable to download video.")

                if video_data.get("video_clips_details") is None:

                    video_clips_details = get_video_clips_details(
                        script_data["script"], video_file_path
                    )

                    if not video_clips_details:
                        raise Exception("Unable to find video clips.")

                    video_data = self.video_repository.update_video(
                        video_id,
                        {
                            "video_clips_details": video_clips_details,
                            "video_file_path": video_file_path,
                        },
                    )
            return video_data
        except Exception as e:
            print(colored(f"[-] Error generate_video_clips_details: {str(e)}", "red"))
            raise

    def generate_video(self, task, video_data):
        video_id = str(video_data["_id"])
        avatar_id = video_data.get("avatar_id", None)
        title = video_data.get("title", None)
        avatar_video_urls = video_data.get("avatar_video_urls", None)
        try:

            user_id, script_id, voice_id = self._extract_video_data(video_data)

            script_data = self.script_repository.get_script(script_id)
            script = script_data.get("script")

            if not script:
                raise NotFound("Script is not found.")

            if avatar_id and avatar_video_urls is None:

                video_ids = []
                video_links = []
                avatar_data = self.avatar_repository.get_avatar_by_id(avatar_id)

                if avatar_data.get("is_cloned") is False:

                    if video_data.get("avatar_video_ids") is None:

                        script = get_script_text(script_data["script"], "gpt4")

                        print(colored(f"script:{script}", "yellow"))

                        video_res = self.tavus_service.create_video_using_script(
                            name="testing",
                            script=script,
                            replica_id=avatar_data["avatar_id"],
                        )
                        self.video_repository.update_video(
                            video_id,
                            {"avatar_video_ids": [video_res["video_id"]]},
                        )

                        video_data = self.generate_video_clips_details(
                            video_data, script_data, task
                        )

                        avatar_video_urls = self.get_avatar_videos(
                            task, [video_res["video_id"]]
                        )
                    else:

                        video_data = self.generate_video_clips_details(
                            video_data, script_data, task
                        )

                        avatar_video_urls = self.get_avatar_videos(
                            task, video_data.get("avatar_video_ids")
                        )

                    if (
                        avatar_video_urls
                        and len(avatar_video_urls) != 0
                        and avatar_video_urls[0]
                    ):
                        self.video_repository.update_video(
                            video_id,
                            {"avatar_video_urls": avatar_video_urls},
                        )
                else:

                    audio_data = self.audio_service.generate_audio(
                        user_id, voice_id, script_id
                    )

                    video_data = self.generate_video_clips_details(
                        video_data, script_data, task
                    )

                    task.update_state(
                        state="PROGRESS",
                        meta={"progress": 5, "status": "audio generating ..."},
                    )

                    audio_data = self.audio_service.get_updated_audio(audio_data)

                    if video_data.get("avatar_video_ids") is None:
                        videos_res = self.tavus_service.generate_videos(
                            name="testing",
                            audio_urls=audio_data["audio_links"],
                            replica_id=avatar_data["avatar_id"],
                        )

                        for res in videos_res:
                            video_ids.append(res["video_id"])
                            video_links.append(res["video_link"])

                        self.video_repository.update_video(
                            video_id,
                            {"avatar_video_ids": video_ids},
                        )

            if avatar_video_urls:
                video_data = self.generate_video_clips_details(
                    video_data, script_data, task, 15
                )
                audio_data = self.audio_service.generate_audio_using_video(
                    video_id, user_id, voice_id, script_id, avatar_video_urls[0], script
                )
            else:
                audio_data = self.audio_service.generate_audio(
                    user_id, voice_id, script_id
                )
                video_data = self.generate_video_clips_details(
                    video_data, script_data, task
                )

                task.update_state(
                    state="PROGRESS",
                    meta={"progress": 5, "status": "audio generating ..."},
                )
                audio_data = self.audio_service.get_updated_audio(audio_data)

            task.update_state(
                state="PROGRESS",
                meta={"progress": 15, "status": "subtitle generating ..."},
            )

            audio_data = self.audio_service.generate_subtitles(
                video_id, str(audio_data["_id"])
            )

            title = video_data.get("title")

            task.update_state(
                state="PROGRESS",
                meta={"progress": 16, "status": "title generating ..."},
            )

            if title is None:
                # Generate a script
                title = get_title(
                    script_data["script"], "gpt-3.5-turbo"
                )  # Pass the AI model to the script generation

                video_data = self.video_repository.update_video(
                    video_id,
                    {
                        "title": title,
                    },
                )

                print(colored("title of script: " + title, "yellow"))

            video_data = self.video_repository.to_dict(video_data)

            video_data = self.process_script(
                script_data,
                video_data,
                audio_data,
                video_data.get("video_file_path"),
                task,
            )

            video_data = self.video_repository.to_dict(video_data)

            subtitles_path = create_subtitle_file(video_id, audio_data.get("subtitles"))

            final_video_path, duration = self.generate_final_video(
                video_data, script_data, audio_data, subtitles_path, task
            )

            if final_video_path is None:
                raise VideoCreationFailed("video is generation failed")

            task.update_state(
                state="PROGRESS",
                meta={"progress": 99, "status": "video uploading ..."},
            )

            thumbnail_path = f"./temp/{video_id}/thumbnail.jpg"

            create_thumbnail(final_video_path, thumbnail_path)

            link = self.s3_service.upload_file(
                "videos", final_video_path, f"{video_id}.mp4"
            )

            thumbnail_link = self.s3_service.upload_file(
                "videos", thumbnail_path, f"{video_id}.jpg"
            )

            video_data = self.video_repository.update_video(
                video_id,
                {
                    "link": link,
                    "thumbnail_link": thumbnail_link,
                    "status": VideoStatus.COMPLETED,
                    "duration": duration,
                },
            )

            self.usage_service.create_usage(
                ObjectId(user_id),
                {"credits_used": 1, "seconds_used": int(duration)},
            )

            self.send_email(user_id, video_id, EmailType.VIDEO_GENERATED.value)

            self.notification_service.create_notification(
                user_id,
                "Video Update",
                f"Video successfully generated, {title}",
                NotificationType.UPDATE.value,
                {
                    "type": "NOTIFICATION",
                    "data": {"video_id": video_id, "title": title},
                },
            )

            task.update_state(
                state="COMPLETED",
                meta={"progress": 100, "status": "video generated ..."},
            )

            return video_data

        except Exception as e:
            print("Error generating video", str(e))
            task.update_state(
                state="FAILED",
                meta={"progress": 0, "status": "failed ..."},
            )
            # If an error occurs, update the status to FAILED
            self.video_repository.update_video(
                video_id,
                {"status": VideoStatus.FAILED, "status_message": f"Error: {str(e)}"},
            )
            self.send_email(
                user_id,
                video_id,
                EmailType.VIDEO_GENERATION_FAILED.value,
                {"failed_reason": f"Error:{str(e)}"},
            )

            self.notification_service.create_notification(
                user_id,
                "Video Update",
                f"Video generation failed, {str(e)}",
                NotificationType.UPDATE.value,
                {
                    "type": "NOTIFICATION",
                    "data": {"video_id": video_id},
                },
            )
            sentry_client.capture_exception(e)
            return None

    def process_script(
        self,
        script_data,
        video_data,
        audio_data,
        video_file_path=None,
        task=None,
    ):
        video_id = str(video_data["_id"])
        avatar_id = video_data.get("avatar_id", None)

        task.update_state(
            state="PROGRESS",
            meta={"progress": 20, "status": "clips downloading ..."},
        )

        if script_data.get("type") == ScriptType.VIDEO.value:
            if (
                video_data.get("event_video_clips") is None
                or len(video_data.get("event_video_clips")) == 0
            ):
                self.generate_sub_clips_safely(
                    video_id,
                    video_data.get("video_clips_details"),
                    script_data["script"],
                    audio_data["subtitles"],
                    video_file_path,
                )

                with open("./content/clips.json", "r") as file:
                    clips = json.load(file)

                clips = sorted(clips["data"], key=lambda x: x["atTime"])

                video_data = self.video_repository.update_video(
                    video_id, {"event_video_clips": clips}
                )

        task.update_state(
            state="PROGRESS",
            meta={"progress": 25, "status": "stock clip extracting ..."},
        )

        if (
            video_data.get("stock_video_clips") is None
            or len(video_data.get("stock_video_clips")) == 0
        ) and (script_data["type"] != ScriptType.VIDEO.value or not avatar_id):

            self.generate_search_terms_safely(
                script_data["script"], audio_data["subtitles"]
            )

            with open("./content/search_terms.json", "r") as file:
                search_terms_data = json.load(file)

            search_terms_data, video_urls = self.fetch_stock_videos(
                video_id=video_id,
                search_terms_data=search_terms_data,
            )

            search_terms_data = sorted(search_terms_data, key=lambda x: x["at_time"])

            video_data = self.video_repository.update_video(
                video_id,
                {"stock_video_clips": search_terms_data, "video_urls": video_urls},
            )

        else:

            if video_data.get("video_urls") and len(video_data.get("video_urls")) != 0:
                self.download_videos(video_id, video_data["video_urls"])

        task.update_state(
            state="PROGRESS",
            meta={"progress": 35, "status": "image clip extracting ..."},
        )

        if video_data.get("images") is None or len(video_data.get("images")) == 0:

            link = None

            if script_data["type"] == ScriptType.BLOG.value:
                link = script_data["link"]

            image_links = self.generate_images_safely(
                video_id,
                script_data["title"],
                script_data["script"],
                audio_data["subtitles"],
                link,
            )

            with open("./content/images.json", "r") as file:
                image_data = json.load(file)

            for key, value in image_data.items():
                images = json.loads(key)

            images = sorted(images, key=lambda x: x["atTime"])

            video_data = self.video_repository.update_video(
                video_id, {"images": images, "image_links": image_links}
            )

        else:

            if (
                video_data.get("image_links")
                and len(video_data.get("image_links")) != 0
            ):
                for index, link in enumerate(video_data["image_links"]):
                    save_path = f"./temp/{video_id}/image_{index + 1}.jpg"
                    download_image(link, save_path)

        return video_data

    def generate_sub_clips_safely(
        self, video_id, video_clips_details, script, subtitles, video_file_path
    ):
        try:
            generate_sub_clips(
                video_id, video_clips_details, script, subtitles, video_file_path
            )
        except Exception as e:
            print(colored(f"[-] Error generating video clips: {e}", "red"))
            raise

    def generate_images_safely(self, video_id, title, script, subtitles, link):
        try:
            return generate_images(video_id, title, script, subtitles, link)
        except Exception as e:
            print(colored(f"[-] Error generating images: {e}", "red"))
            raise

    def generate_search_terms_safely(self, script, subtitles):
        try:
            return generate_search_terms(script, subtitles)
        except Exception as e:
            print(colored(f"[-] Error generating search term: {e}", "red"))
            raise

    def save_brand_details(
        self,
        video_id,
        user_id,
        view_type,
    ):
        try:
            brand = self.brand_repository.get_brands_by_user(user_id)
            if brand:
                logo = brand.get("logo")
                if logo:
                    save_path = f"./temp/{video_id}/logo.png"
                    download_image(logo, save_path)
                if VideoViewType.LANDSCAPE.value == view_type:
                    intro_landscape = brand.get("intro_landscape")
                    outro_landscape = brand.get("outro_landscape")
                    if intro_landscape:
                        save_video(
                            intro_landscape,
                            video_id="intro_video",
                            directory=f"./temp/{video_id}",
                        )
                    if outro_landscape:
                        save_video(
                            outro_landscape,
                            video_id="outro_video",
                            directory=f"./temp/{video_id}",
                        )
                else:
                    intro_portrait = brand.get("intro_portrait")
                    outro_portrait = brand.get("outro_portrait")
                    if intro_portrait:
                        save_video(
                            intro_portrait,
                            video_id="intro_video",
                            directory=f"./temp/{video_id}",
                        )
                    if outro_portrait:
                        save_video(
                            outro_portrait,
                            video_id="outro_video",
                            directory=f"./temp/{video_id}",
                        )

        except Exception as e:
            logger.error(f"Error generating images: {str(e)}")
            raise

    def generate_final_video(
        self, video_data, script_data, audio_data, subtitles_path, task
    ):
        try:
            duration = 0
            video_id = str(video_data["_id"])
            user_id = str(video_data["user_id"])
            view_type = video_data["view_type"]
            template_id = video_data.get("template_id")
            caption = video_data.get("caption")
            music_media_id = str(script_data.get("music_media_id", None))
            add_brand = script_data.get("add_brand", False)

            if add_brand:
                self.save_brand_details(video_id, user_id, view_type)

            if template_id is None:
                template_id = "template_1"

            task.update_state(
                state="PROGRESS",
                meta={"progress": 40, "status": "start video generation ..."},
            )

            if video_data.get("avatar_video_urls") or video_data.get(
                "avatar_video_ids"
            ):
                if video_data.get("avatar_video_urls"):
                    video_urls = video_data.get("avatar_video_urls")
                else:
                    video_urls = self.get_avatar_videos(
                        None, video_data["avatar_video_ids"], 40
                    )
                    self.video_repository.update_video(
                        video_id,
                        {"avatar_video_urls": video_urls},
                    )
                print("video_urls", video_urls)
                video_paths = []
                for index, video_url in enumerate(video_urls):
                    saved_video_path = save_video(
                        video_url,
                        video_id=f"avatar_{index}",
                        directory=f"./temp/{video_id}",
                    )
                    video_paths.append(saved_video_path)

                if script_data.get("video_type") == VideoType.SHORT.value:

                    final_video_path, duration = create_reel_video(
                        video_id,
                        video_paths,
                        subtitles_path,
                        video_data.get("event_video_clips"),
                        video_data.get("images"),
                        video_data.get("stock_video_clips"),
                        template_id,
                        script_data["type"] == ScriptType.VIDEO.value,
                        video_data.get("video_file_path"),
                        caption,
                        task,
                        music_media_id,
                    )
                else:
                    if video_data.get("intro_time") is None:
                        try:
                            intro_time = float(
                                get_intro_placement_time(audio_data["subtitles"])
                            )
                        except Exception as e:
                            print("Error in intro time", str(e))
                            intro_time = 7
                        self.video_repository.update_video(
                            video_id,
                            {"intro_time": intro_time},
                        )
                    else:
                        intro_time = video_data.get("intro_time")

                    print("intro_time", intro_time)

                    final_video_path, duration = test_long_video(
                        video_id,
                        video_paths,
                        video_data.get("title"),
                        video_data.get("event_video_clips"),
                        video_data.get("stock_video_clips"),
                        video_data.get("images"),
                        video_data.get("video_file_path"),
                        task,
                        intro_time,
                    )
            else:
                audio_path = f"./temp/{video_id}/audio.mp3"
                if script_data.get("video_type") == VideoType.SHORT.value:

                    final_video_path, duration = create_reel_video1(
                        video_id,
                        audio_path,
                        subtitles_path,
                        video_data.get("event_video_clips"),
                        video_data.get("images"),
                        video_data.get("stock_video_clips"),
                        script_data.get("type") == ScriptType.VIDEO.value,
                        video_data.get("video_file_path"),
                        caption,
                        task,
                        music_media_id,
                    )

                else:

                    final_video_path, duration = long_video1(
                        video_id,
                        audio_path,
                        video_data.get("event_video_clips"),
                        video_data.get("images"),
                        video_data.get("stock_video_clips"),
                        video_data.get("video_file_path"),
                        task,
                    )

            stop_ffmpeg_processes()

            return final_video_path, duration
        except Exception as e:
            print(colored(f"[-] Error generating final video: {e}", "red"))
            raise

    def get_avatar_videos(self, task, video_ids, percentage=5):
        try:
            if task:
                task.update_state(
                    state="PROGRESS",
                    meta={"progress": percentage, "status": "avatar generating ..."},
                )
            videos_data = self.tavus_service.get_videos(video_ids, task)
            return [
                video["download_url"]
                for video in videos_data
                if "download_url" in video
            ]
        except Exception as e:
            print(f"Error fetching avatar videos: {e}")
            return None

    def fetch_stock_videos(
        self, video_id, search_terms_data, video_provider="storyblocks", it=2, min_dur=5
    ):
        video_urls, new_search_terms_data = self.search_videos(
            search_terms_data, video_provider, it, min_dur
        )
        self.download_videos(video_id, video_urls)
        return new_search_terms_data, video_urls

    def search_videos(self, search_terms_data, video_provider, it, min_dur):
        search_terms = [
            data["search_terms"] for data in search_terms_data.get("data", [])
        ]
        video_urls = []
        new_search_terms_data = []
        for index, search_term in enumerate(search_terms):
            found_urls = (
                search_for_stock_videos_on_story_block
                if video_provider == "storyblocks"
                else search_for_stock_videos
            )(search_term, it, min_dur)
            for url in found_urls:
                if url is not None and url not in video_urls:
                    video_urls.append(url)
                    new_search_terms_data.append(search_terms_data["data"][index])
                    break
        return video_urls, new_search_terms_data

    def download_videos(self, video_id, video_urls):
        if not video_urls:
            print(colored("[-] No videos found to download.", "red"))
            return []

        print(colored(f"[+] Downloading {len(video_urls)} videos...", "blue"))
        video_paths = []
        for index, video_url in enumerate(video_urls):
            try:
                saved_video_path = save_video(
                    video_url, video_id=index, directory=f"./temp/{video_id}"
                )
                video_paths.append(saved_video_path)
            except Exception:
                print(colored(f"[-] Could not download video: {video_url}", "red"))
        return video_paths

    def download_video_with_quality(self, user_id, video_data, quality):

        video_id = str(video_data["_id"])

        try:

            user_data = self.user_repository.get_populated_user_details(user_id)

            if not user_data.get("subscription"):
                raise SubscriptionInactiveError("subscription is not active")

            if not video_data.get("link"):
                raise Exception("video is not available")
            if quality == VideoQuality.Q720p.value:

                download_link = self.s3_service.generate_presigned_url_for_download(
                    "videos", f"{video_id}.mp4"
                )

            elif quality == VideoQuality.Q1080p.value:

                if not video_data.get("link_1080p"):

                    video_path = f"./temp/{video_id}.mp4"

                    save_video(
                        video_data.get("link"),
                        video_id=video_id,
                    )

                    final_video_path = f"./temp/{video_id}_1080p.mp4"

                    upscale_video(
                        video_path,
                        final_video_path,
                        VideoViewType.LANDSCAPE.value == video_data.get("view_type"),
                    )

                    link = self.s3_service.upload_file(
                        "videos", final_video_path, f"{video_id}_1080.mp4"
                    )

                    video_data = self.video_repository.update_video(
                        video_id, {"link_1080p": link}
                    )
                    remove_file(video_path)
                    remove_file(final_video_path)

                download_link = self.s3_service.generate_presigned_url_for_download(
                    "videos",
                    f"{video_id}_1080.mp4",
                )

            else:
                raise Exception("video quality not available")

            return {"download_link": download_link}
        except Exception:
            print(
                colored(
                    f"[-] Could not download_video_with_quality: {video_id}",
                    "red",
                )
            )
            raise
