from app.repositories.brand_repository import BrandRepository
from app.exceptions.common import InvalidValueError


from werkzeug.exceptions import NotFound


class BrandService:
    def __init__(self):
        self.brand_repository = BrandRepository()

    def create_brand(self, data):
        """
        Create a new brand.
        """
        try:
            # Validate required fields
            required_fields = ["name", "logo", "user_id"]
            for field in required_fields:
                if field not in data or not data[field]:
                    raise InvalidValueError(f"Missing required field: {field}")

            # Create the brand
            brand_data = self.brand_repository.create_brand(data)

            return self.brand_repository.to_dict(brand_data)
        except Exception as e:
            raise InvalidValueError(f"Error creating brand: {str(e)}")

    def update_brand(self, user_id, data):
        """
        Update an existing brand.
        """
        try:
            # Check if brand exists and belongs to the user
            existing_brand = self.brand_repository.get_brands_by_user(user_id)

            if not existing_brand:
                raise NotFound("Brand not found")

            # Update only provided fields
            update_data = {}

            allowed_fields = [
                "name",
                "logo",
                "intro_landscape",
                "outro_landscape",
                "intro_portrait",
                "outro_portrait",
            ]
            for field in allowed_fields:
                if field in data:
                    update_data[field] = data[field]

            # Update the brand
            brand_data = self.brand_repository.update_brand(
                str(existing_brand["_id"]), update_data
            )

            return self.brand_repository.to_dict(brand_data)
        except NotFound:
            raise
        except Exception as e:
            raise InvalidValueError(f"Error updating brand: {str(e)}")

    def get_brand(self, user_id):
        """
        Get a specific brand.
        """
        brand = self.brand_repository.get_brands_by_user(user_id)

        brand = self.brand_repository.to_dict(brand)

        return brand

    def get_user_brands(self, user_id, page, per_page):
        """
        Get all brands for a user with pagination.
        """
        try:
            skip = (page - 1) * per_page
            brands = list(
                self.brand_repository.get_brands_by_user(user_id)
                .skip(skip)
                .limit(per_page)
            )
            total = self.brand_repository.get_by_user_id(user_id).count()
            return {
                "brands": brands,
                "total": total,
                "page": page,
                "per_page": per_page,
            }
        except Exception as e:
            raise InvalidValueError(f"Error retrieving user brands: {str(e)}")

    def delete_brand(self, user_id):
        """
        Delete a brand.
        """
        try:
            # Check if brand exists and belongs to the user
            existing_brand = self.get_brand(user_id)
            if not existing_brand:
                raise NotFound("Brand not found")

            # Delete the brand
            self.brand_repository.delete_brand(str(existing_brand["_id"]))
            return True
        except NotFound:
            raise
        except Exception as e:
            raise InvalidValueError(f"Error deleting brand: {str(e)}")
