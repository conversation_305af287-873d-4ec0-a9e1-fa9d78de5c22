import requests
from termcolor import colored
import random

import json
from config import WEBSHARE_PROXY_API

from datetime import datetime, timedelta


def proxy_list():
    file_path = "./content/proxy.json"

    try:

        with open(file_path, "r") as file:
            data = json.load(file)

        if (
            datetime.fromisoformat(data.get("time")) + timedelta(seconds=2)
            < datetime.utcnow()
        ):

            # Select a random number between 1 and 4
            page = random.choice([1, 2, 3, 4])

            response = requests.get(
                f"https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page={page}&page_size=25",
                headers={"Authorization": WEBSHARE_PROXY_API},
            )

            response.raise_for_status()

            proxies = response.json()["results"]

            proxies_list = []

            for proxy in proxies:
                url = f"http://{proxy["username"]}:{proxy["password"]}@{proxy["proxy_address"]}:{proxy["port"]}"

                proxies_list.append(url)

            data = {"proxies_list": proxies_list, "time": datetime.utcnow().isoformat()}

            with open(file_path, "w") as json_file:
                json.dump(data, json_file)

            return proxies_list

        else:
            return data.get("proxies_list")

    except Exception as err:
        print(colored(f"[-] Error: {str(err)}", "red"))
