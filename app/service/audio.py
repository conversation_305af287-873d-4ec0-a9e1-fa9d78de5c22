import sys
import os

from app.helper.validation import is_valid_url

from app.repositories.script_repository import ScriptRepository
from app.repositories.voice_repository import VoiceRepository
from app.repositories.audio_repository import AudioRepository

from .voice import VoiceService
from .play_ht import PlayHtService
from .video.utils import convert_video_to_audio
from .utils import save_video

import urllib.request

from bson import ObjectId

from termcolor import colored

from app.constant.audio_enums import Status

from .assembly_ai import generate_subtitles

from moviepy.editor import AudioFileClip, concatenate_audioclips

from .llm.llm_call import get_script_text

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


class AudioService:
    def __init__(self):
        self.voice_repository = VoiceRepository()
        self.script_repository = ScriptRepository()
        self.voice_service = VoiceService()
        self.play_ht_service = PlayHtService()
        self.audio_repository = AudioRepository()

    def generate_audio(self, user_id, voice_id, script_id):

        try:

            audio_data = self.audio_repository.get_audio_by_script_and_voice_id(
                script_id, voice_id
            )

            if not audio_data:

                script_data = self.script_repository.get_script(script_id)

                voice_data = self.voice_repository.get_voice_by_id(voice_id)

                script = get_script_text(script_data["script"], "gpt4")

                print(colored(f"script:{script}", "yellow"))

                audio_ids = self.play_ht_service.create_voices(
                    script, voice_data["voice_id"]
                )

                audio_data = self.audio_repository.create_audio(
                    {
                        "audio_ids": audio_ids,
                        "voice_id": ObjectId(voice_id),
                        "script_id": ObjectId(script_id),
                        "user_id": ObjectId(user_id),
                        "status": Status.COMPLETED,
                        "script": script,
                    }
                )

            return self.audio_repository.to_dict(audio_data)

        except Exception as err:
            print(colored(f"[-] Error: generate_audio {str(err)}", "red"))
            raise

    def get_updated_audio(self, audio_data):
        if (
            audio_data.get("audio_links") is None
            or len(audio_data.get("audio_links")) == 0
        ):
            audio_links = self.play_ht_service.get_voice_audios(audio_data["audio_ids"])
            audio_data = self.audio_repository.update_audio(
                str(audio_data["_id"]),
                {
                    "audio_links": audio_links,
                },
            )
            return self.audio_repository.to_dict(audio_data)
        return audio_data

    def generate_audio_using_video(
        self, video_id, user_id, voice_id, script_id, video_url, script=None
    ):

        try:

            audio_data = self.audio_repository.get_audio_by_script(script_id)

            if not audio_data:

                path = f"./temp/{video_id}/audio.mp3"

                saved_video_path = save_video(
                    video_url,
                    video_id="avatar_0",
                    directory=f"./temp/{video_id}",
                )

                convert_video_to_audio(saved_video_path, path)

                audio_data = self.audio_repository.create_audio(
                    {
                        "audio_links": [path],
                        "voice_id": (
                            ObjectId(voice_id) if voice_id is not None else None
                        ),
                        "script_id": ObjectId(script_id),
                        "user_id": ObjectId(user_id),
                        "status": Status.COMPLETED,
                        "script": script,
                    }
                )
            else:

                path = f"./temp/{video_id}/audio.mp3"

                saved_video_path = save_video(
                    video_url,
                    video_id="avatar_0",
                    directory=f"./temp/{video_id}",
                )

                convert_video_to_audio(saved_video_path, path)

            return self.audio_repository.to_dict(audio_data)

        except Exception as err:
            print(colored(f"[-] Error: generate_audio_using_video {str(err)}", "red"))
            raise

    def get_audio_by_id(self, audio_id):

        audio_data = self.audio_repository.get_by_id(audio_id)

        return self.audio_repository.to_dict(audio_data)

    def generate_subtitles(self, video_id, audio_id):

        try:

            audio_data = self.audio_repository.get_audio_by_id(audio_id)

            tts_path = f"./temp/{video_id}/audio.mp3"

            audio_data = self.get_updated_audio(audio_data)

            audio_clips = []
            for index, audio_url in enumerate(audio_data["audio_links"]):
                if is_valid_url(audio_url):
                    # Download the audio files
                    file_path = os.path.join(
                        os.getcwd(),
                        f"./temp/{video_id}/temp_audio{index}.mp3",
                    )
                    urllib.request.urlretrieve(audio_url, file_path)
                else:
                    file_path = audio_url

                audio_clips.append(AudioFileClip(file_path))

            audio_clip = concatenate_audioclips(audio_clips)

            if not os.path.exists(tts_path):

                # Write the combined audio to the output file
                audio_clip.write_audiofile(tts_path)

            if audio_data.get("subtitles") is not None:
                return audio_data

            voice = "en_us_001"
            voice_prefix = voice[:2]

            # Split script into sentences
            sentences = audio_data["script"].split(". ")

            # Remove empty strings
            sentences = list(filter(lambda x: x != "", sentences))

            subtitles = generate_subtitles(
                audio_path=tts_path,
                sentences=sentences,
                audio_clips=[audio_clip],
                voice=voice_prefix,
            )

            return self.audio_repository.update_audio(
                audio_id, {"subtitles": subtitles}
            )
        except Exception as err:
            print(colored(f"[-] Error generate_subtitles: {str(err)}", "red"))
            raise
