from abc import ABC, abstractmethod


# LLM Provider Abstract Base Class
class LLMProvider(ABC):
    @abstractmethod
    def generate_response(self, prompt: str) -> str:
        pass


# Concrete LLM Provider Classes
class G4FProvider(LLMProvider):
    def generate_response(self, prompt: str) -> str:
        import g4f
        from g4f.client import Client

        client = Client()
        response = (
            client.chat.completions.create(
                model="gpt-3.5-turbo",
                provider=g4f.Provider.You,
                messages=[{"role": "user", "content": prompt}],
            )
            .choices[0]
            .message.content
        )
        return response


class OpenAIProvider(LLMProvider):
    def __init__(self, api_key: str, model: str):
        import openai
        openai.api_key = api_key
        self.model = model

    def generate_response(self, prompt: str) -> str:
        import openai
        response = (
            openai.chat.completions.create(
                model=self.model, messages=[{"role": "user", "content": prompt}]
            )
            .choices[0]
            .message.content
        )
        return response


class Gemini<PERSON>rovider(LLMProvider):
    def __init__(self, api_key: str):
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-pro")

    def generate_response(self, prompt: str) -> str:
        response_model = self.model.generate_content(prompt)
        return response_model.text
