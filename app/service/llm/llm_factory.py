from .llm_provider import OpenAIProvider, GeminiProvider, LLMProvider, G4FProvider


# LLM Factory
class LLMFactory:
    @staticmethod
    def create_provider(provider_type: str, **kwargs) -> LLMProvider:
        if provider_type == "g4f":
            return G4FProvider()
        elif provider_type in ["gpt-3.5-turbo", "gpt4"]:
            return OpenAIProvider(kwargs["api_key"], provider_type)
        elif provider_type == "gemini":
            return GeminiProvider(kwargs["api_key"])
        else:
            raise ValueError("Invalid LLM provider selected.")
