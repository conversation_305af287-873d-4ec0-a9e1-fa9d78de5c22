import re
import g4f
import json
import openai
import google.generativeai as genai

from g4f.client import Client
from termcolor import colored

from typing import Tuple, List

from config import OPENAI_API_KEY, GOOGLE_API_KEY

from .leonardo import image_generation

from .prompts import (
    build_script_prompt,
    build_search_terms_prompt,
    build_video_title_prompt,
    build_video_description_prompt,
    build_reel_captions_prompt,
    build_script_text_prompt,
    build_divide_script_prompt,
    build_intro_placement_prompt,
    build_shorts_title_prompt,
    build_shorts_description_prompt,
)

openai.api_key = OPENAI_API_KEY
genai.configure(api_key=GOOGLE_API_KEY)


def generate_image(prompt):
    response = openai.images.generate(
        model="dall-e-3",
        prompt=prompt,
        size="1024x1024",
        quality="standard",
        n=1,
    )

    image_url = response.data[0].url
    return image_url


def get_image_info(prompt, image_url):
    response = openai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url,
                        },
                    },
                ],
            }
        ],
        max_tokens=300,
    )

    token_used = response.usage.total_tokens

    print("token_used ===>>", token_used)

    response = response.choices[0].message.content

    return response


def generate_response(prompt: str, ai_model: str) -> str:
    """
    Generate a script for a video, depending on the subject of the video.
    Args:
        video_subject (str): The subject of the video.
        ai_model (str): The AI model to use for generation.

    Returns:

        str: The response from the AI model.

    """

    try:

        if ai_model == "g4f":
            # Newest G4F Architecture
            client = Client()
            response = (
                client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    provider=g4f.Provider.You,
                    messages=[{"role": "user", "content": prompt}],
                )
                .choices[0]
                .message.content
            )
        elif ai_model == "gpt4":

            response = openai.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": prompt}],
            )

            token_used = response.usage.total_tokens

            print("token_used ===>>", token_used)

            response = response.choices[0].message.content

        elif ai_model in ["gpt-3.5-turbo", "gpt4"]:

            model_name = (
                "gpt-3.5-turbo" if ai_model == "gpt3.5-turbo" else "gpt-4-1106-preview"
            )

            response = (
                openai.chat.completions.create(
                    model=model_name,
                    messages=[{"role": "user", "content": prompt}],
                )
                .choices[0]
                .message.content
            )
        elif ai_model == "gemmini":
            model = genai.GenerativeModel("gemini-pro")
            response_model = model.generate_content(prompt)
            response = response_model.text
        else:
            raise ValueError("Invalid AI model selected.")
        return response

    except Exception as e:
        print(colored(f"[-] Error: openai generate_response {str(e)}", "red"))
        raise


def generate_script(
    video_subject: str,
    paragraph_number: int,
    ai_model: str,
    voice: str,
    customPrompt: str,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video, the number of paragraphs, and the AI model.

    Args:

        video_subject (str): The subject of the video.

        paragraph_number (int): The number of paragraphs to generate.

        ai_model (str): The AI model to use for generation.

    Returns:

        str: The script for the video.

    """

    # Build prompt

    prompt = build_script_prompt(video_subject, paragraph_number, voice, customPrompt)

    # Generate script
    response = generate_response(prompt, ai_model)

    print(colored(response, "cyan"))

    # Return the generated script
    if response:
        # Clean the script
        # Remove asterisks, hashes
        response = response.replace("*", "")
        response = response.replace("#", "")

        # Remove markdown syntax
        response = re.sub(r"\[.*\]", "", response)
        response = re.sub(r"\(.*\)", "", response)

        # Split the script into paragraphs
        paragraphs = response.split("\n\n")

        # Select the specified number of paragraphs
        selected_paragraphs = paragraphs[:paragraph_number]

        # Join the selected paragraphs into a single string
        final_script = "\n\n".join(selected_paragraphs)

        # Print to console the number of paragraphs used
        print(
            colored(f"Number of paragraphs used: {len(selected_paragraphs)}", "green")
        )

        return final_script
    else:
        print(colored("[-] GPT returned an empty response.", "red"))
        return None


def get_search_terms(
    video_subject: str, amount: int, script: str, ai_model: str
) -> List[str]:
    """
    Generate a JSON-Array of search terms for stock videos,
    depending on the subject of a video.

    Args:
        video_subject (str): The subject of the video.
        amount (int): The amount of search terms to generate.
        script (str): The script of the video.
        ai_model (str): The AI model to use for generation.

    Returns:
        List[str]: The search terms for the video subject.
    """

    # Build prompt
    prompt = build_search_terms_prompt(video_subject, amount, script)

    # Generate search terms
    response = generate_response(prompt, ai_model)

    # Parse response into a list of search terms
    search_terms = []

    try:
        search_terms = json.loads(response)
        if not isinstance(search_terms, list) or not all(
            isinstance(term, str) for term in search_terms
        ):
            raise ValueError("Response is not a list of strings.")

    except (json.JSONDecodeError, ValueError):
        # Get everything between the first and last square brackets
        response = response[response.find("[") + 1 : response.rfind("]")]

        print(
            colored(
                "[*] GPT returned an unformatted response. Attempting to clean...",
                "yellow",
            )
        )

        # Attempt to extract list-like string and convert to list
        match = re.search(r'\["(?:[^"\\]|\\.)*"(?:,\s*"[^"\\]*")*\]', response)
        print(match.group())
        if match:
            try:
                search_terms = json.loads(match.group())
            except json.JSONDecodeError:
                print(colored("[-] Could not parse response.", "red"))
                return []

    # Let user know
    print(
        colored(
            f"\nGenerated {len(search_terms)} search terms: {', '.join(search_terms)}",
            "cyan",
        )
    )

    # Return search terms
    return search_terms


def generate_metadata(
    video_subject: str, script: str, ai_model: str, video_type="Video"
) -> Tuple[str, str, List[str]]:
    """
    Generate metadata for a YouTube video, including the title, description, and keywords.

    Args:
        video_subject (str): The subject of the video.
        script (str): The script of the video.
        ai_model (str): The AI model to use for generation.

    Returns:
        Tuple[str, str, List[str]]: The title, description, and keywords for the video.
    """
    if video_type == "Video":
        # Build prompt for title
        title_prompt = build_video_title_prompt(script)
        description_prompt = build_video_description_prompt(script)

    else:

        # Build prompt for title
        title_prompt = build_shorts_title_prompt(
            script,
        )
        description_prompt = build_shorts_description_prompt(script)

    # Generate title
    title = generate_response(title_prompt, ai_model).strip()

    # Generate description
    description = generate_response(description_prompt, ai_model).strip()

    # Generate keywords
    keywords = get_search_terms(video_subject, 5, script, ai_model)

    return title, description, keywords


def generate_similar_search_terms(input_text, num_terms=5):
    """
    Generate similar search terms for finding stock videos based on the given input text.

    Args:
        input_text (str): The input text describing the desired stock video.
        num_terms (int, optional): The number of similar search terms to generate. Default is 5.

    Returns:
        list: A list of similar search terms.
    """
    prompt = f"Generate {num_terms} similar search terms that help to finding stock videos related to the following text: \n\n{input_text} , don't need stock videos keyword remove it. YOU MUST ONLY RETURN THE JSON-ARRAY OF STRINGS. "

    response = openai.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=100,
        temperature=0.7,
    )

    return json.loads(response.choices[0].message.content)


def generate_reel_captions(subject, script, ai_model):
    Prompt = build_reel_captions_prompt(subject, script)

    return generate_response(Prompt, ai_model).strip()


def get_title(script, ai_model="gpt4"):

    Prompt = f"""
    Your task to write the title (5-10) words of the video using the video script.
    video Script is: {script}
    """

    return generate_response(Prompt, ai_model).strip()


def get_script_text(script, ai_model):

    Prompt = build_script_text_prompt(script)

    return generate_response(Prompt, ai_model).strip()


def divide_and_get_search_terms(script, ai_model="gpt4"):

    prompt = build_divide_script_prompt(script)

    return generate_response(prompt, ai_model).strip()


def get_intro_placement_time(script_with_subtitle, ai_model="gpt4"):

    Prompt = build_intro_placement_prompt(script_with_subtitle)

    return generate_response(Prompt, ai_model).strip()


def get_article_summary(data, ai_model="gpt4"):

    prompt = f"""
    Provide a concise summary of the following article:

    Content:
    {data}

    Please adhere to these guidelines:
    1. Capture the main ideas and key points of the article.
    2. Maintain a neutral tone.
    3. Keep the summary to in more details in more then 200 words. and Keep in paragraphs
    4. Do not include any personal opinions or additional information not present in the original text.
    5. Use clear and simple language.

    Return the summary in plain text format.
    """

    return generate_response(prompt, ai_model).strip()


def generate_prompt(script, ai_model="gpt4"):
    prompt = f"""
    Based on the following YouTube video script, create a detailed and compelling prompt for an AI image generator to produce an eye-catching thumbnail image:

    VIDEO SCRIPT:
    {script}

    Please generate an image prompt that includes:

    1. Main Subject: Identify and describe the central focus of the video.
    2. Action or State: Explain what the main subject is doing or how it appears.
    3. Keep the left side of the image dark and faded, while the right side showcases and add approx 20 chars video summary at left hand side in big text.
    4. Background: Describe the setting or context that best represents the video's content.
    5. Color Scheme: Propose a color palette that captures the video's mood and enhances visibility.
    6. Composition: Describe the layout, ensuring it's optimized for a YouTube thumbnail (16:9 aspect ratio).
    7. Technical Specifications: Mention "high resolution" and "16:9 aspect ratio" for YouTube compatibility.
    8. Unique Element: Add one distinctive feature that will make the thumbnail stand out.

    Combine these elements into a coherent, detailed prompt of 3-4 sentences that an AI image generation tool could use to create a compelling YouTube thumbnail image. 
    The prompt should be clear, specific, and designed to produce an image that accurately represents the video content while being visually striking and attention-grabbing.
    """

    return generate_response(prompt, ai_model).strip()


def get_image_search_topic(topic, ai_model="gpt4"):
    prompt = f"""
    Provide concise search terms for the following topic/content to help find relevant images:
    Topic: {topic}
    Return the search terms in plain text format give at most 3 search terms.
    """
    return generate_response(prompt, ai_model).strip()
