from flask_dance.contrib.google import google
import logging
from app.cache import get_cache_provider
from google.oauth2.credentials import Credentials
import time
import sys
import urllib
from datetime import datetime, timedelta
from app.constant.user_enums import AccountStatus, LoginEnums, EmailType
from werkzeug.exceptions import InternalServerError
from flask import make_response, redirect, url_for, session
from google.auth.transport.requests import Request
from .subscription.stripe import StripeService
from app.helper.mailer import EmailService
from app.repositories.user_repository import AuthRepository
from app.controller.auth.utils import encode_auth_token, encode_refresh_token
from config import (
    FRONTEND_ENDPOINT,
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    TOKEN_EXPIRY_IN_HOUR,
    USER_JWT_COOKIE,
    DOMAIN,
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,  # directing logs to the standard output
)
logger = logging.getLogger(__name__)


class GoogleProvider:
    def __init__(self):
        self.stripe_service = StripeService()
        self.user_repository = AuthRepository()
        self.cache_provider = get_cache_provider()
        self.email_service = EmailService()

    def send_email(self, user_data, email_type, metadata=None):
        try:
            link = f"{FRONTEND_ENDPOINT}/dashboard"
            self.email_service.send_email_with_template(
                user_data["name"], user_data["email"], link, email_type, metadata
            )
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")

    def login(self, request):
        try:

            logger.info("Attempting to refresh token if expired")
            if not self.refresh_token_if_expired():
                logger.warning("Token refresh failed, redirecting to login")
                return redirect(url_for("google.login"))

            logger.info("Fetching user info")
            resp = google.get("oauth2/v2/userinfo")
            if not resp.ok:
                logger.error(
                    f"Failed to fetch user info. Status: {resp.status_code}, Content: {resp.text}"
                )
                return "Failed to fetch user info", 400

            resp.raise_for_status()
            user_info = resp.json()
            logger.info(f"User info fetched for email: {user_info.get('email')}")

            user_data = self.user_repository.get_by_email(user_info["email"])
            if user_data:
                logger.info(f"Updating existing user: {user_info['email']}")
                user_info = self._update_existing_user(user_data, user_info)
            else:
                logger.info(f"Creating new user: {user_info['email']}")
                user_info = self._create_new_user(user_info)

            self.user_repository.update_or_create_user(user_info)
            user_data = self.user_repository.get_by_email(user_info["email"])
            user_data = self.user_repository.to_dict(user_data)

            logger.info("Generating auth and refresh tokens")
            token = self._generate_auth_token(user_data, request)
            refresh_token = self._generate_refresh_token(str(user_data["_id"]), request)

            logger.info("Starting to create response")
            session["current_user_token"] = token
            redirect_url = f"{FRONTEND_ENDPOINT}/dashboard"
            logger.info(f"Redirect URL set to: {redirect_url}")

            response = redirect(redirect_url)

            cookies = [
                (USER_JWT_COOKIE, token, 60 * 60 * int(TOKEN_EXPIRY_IN_HOUR)),
                (
                    "rt",
                    refresh_token,
                    30 * 60 * 60 * int(TOKEN_EXPIRY_IN_HOUR),
                ),
                (
                    "exp",
                    str(
                        int(datetime.now().timestamp())
                        + int(timedelta(days=1).total_seconds())
                    ),
                    60 * 60 * int(TOKEN_EXPIRY_IN_HOUR),
                ),
            ]

            for name, value, max_age in cookies:
                response.set_cookie(
                    name,
                    value,
                    domain=DOMAIN,
                    max_age=max_age,
                    httponly=True,
                    secure=True,
                    samesite="None",
                )
                logger.info(f"Cookie set: {name}")
            logger.info("Response created successfully")
            return response
        except Exception as e:
            logger.error(f"Error in login: {str(e)}", exc_info=True)
            error_message = f"Login failed: {str(e)}"
            return redirect(
                f"{FRONTEND_ENDPOINT}/error?message={urllib.parse.quote(error_message)}"
            )

    def _update_existing_user(self, user_data, user_info):
        updated_info = {
            "google_id": user_info["id"],
            "email": user_info["email"],
            "verified_email": True,
            "login_method": "google",
            "status": AccountStatus.ACTIVE.value,
        }

        if not user_data.get("customer_id"):
            customer_id = self.stripe_service.create_customer(
                {"email": user_info["email"], "name": user_info["email"]}
            )
            updated_info["customer_id"] = customer_id

        return updated_info

    def _create_new_user(self, user_info):
        customer_id = self.stripe_service.create_customer(
            {"email": user_info["email"], "name": user_info["email"]}
        )

        new_user = {
            "google_id": user_info["id"],
            "profile_picture": user_info["picture"],
            "name": user_info["name"],
            "email": user_info["email"],
            "verified_email": True,
            "login_method": "google",
            "role": "user",
            "status": AccountStatus.ACTIVE.value,
            "customer_id": customer_id,
        }

        self.send_email(new_user, EmailType.WELCOME.value)
        return new_user

    def _generate_auth_token(self, user_data, request):
        user_agent = request.headers.get("User-Agent")
        ip_address = request.remote_addr

        token = encode_auth_token(
            user_id=user_data["_id"],
            user_agent=user_agent,
            ip_address=ip_address,
            role="user",
        )

        if token:
            self.cache_provider.set(
                f"sess_{user_data['_id']}",
                token,
                LoginEnums.LOGIN_EXPIRE_TIME.value,
            )

        return token

    def _generate_refresh_token(self, user_id, request):

        # Generate refresh token with one month validity
        refresh_token = encode_refresh_token(
            user_id=user_id, exp=datetime.utcnow() + timedelta(days=30)
        )

        if refresh_token:
            self.cache_provider.set(
                f"refresh_{user_id}",
                refresh_token,
                LoginEnums.REFRESH_EXPIRE_TIME.value,
            )

        return refresh_token

    def refresh_token_if_expired(self):
        if not google.authorized:
            logger.warning("Google not authorized")
            return False

        token = google.token

        expiry = token.get("expires_at")

        current_time = int(time.time())

        # Check if token is expired or will expire in the next hour
        if expiry is None or current_time >= expiry - 60:
            try:
                # Create a Credentials object with all necessary fields
                creds = Credentials(
                    token=token.get("access_token"),
                    refresh_token=token.get("refresh_token"),
                    token_uri="https://oauth2.googleapis.com/token",
                    client_id=GOOGLE_CLIENT_ID,
                    client_secret=GOOGLE_CLIENT_SECRET,
                    scopes=[
                        "openid",
                        "https://www.googleapis.com/auth/userinfo.email",
                        "https://www.googleapis.com/auth/userinfo.profile",
                    ],
                )

                # Refresh the token
                creds.refresh(Request())

                # Calculate new expiry time (1 hour from now)
                new_expiry = int(time.time()) + 3600

                # Update the token in the flask-dance's google object
                google.token = {
                    "access_token": creds.token,
                    "refresh_token": creds.refresh_token,
                    "token_type": token["token_type"],
                    "expires_at": new_expiry,
                }

                logger.info("Token refreshed successfully")
                return True

            except Exception as e:
                logger.error(f"Token refresh failed: {str(e)}")
                return False

        logger.info("Token is still valid")
        return True

    def update_youtube_credentials(self, request, youtube_credentials):
        try:
            resp = google.get("oauth2/v2/userinfo")
            resp.raise_for_status()
            user_info = resp.json()

            data_to_update = {"youtube_credentials": youtube_credentials}
            self.user_repository.update_user_by_email(
                user_info["email"], data_to_update
            )

            redirect_url = f"{FRONTEND_ENDPOINT}/auth/youtube-success"
            return make_response(redirect(redirect_url))
        except Exception as e:
            logger.error(f"Error updating YouTube credentials: {str(e)}")
            raise InternalServerError("Failed to update YouTube credentials")

    def update_youtube_credentials_using_id(self, user_id, youtube_credentials):
        try:
            data_to_update = {"youtube_credentials": youtube_credentials}
            self.user_repository.update_user(user_id, **data_to_update)

            redirect_url = f"{FRONTEND_ENDPOINT}/auth/youtube-success"
            return make_response(redirect(redirect_url))
        except Exception as e:
            logger.error(f"Error updating YouTube credentials: {str(e)}")
            raise InternalServerError("Failed to update YouTube credentials")

    def update_youtube_credentials_using_email(self, email, youtube_credentials):
        try:
            data_to_update = {"youtube_credentials": youtube_credentials}
            self.user_repository.update_user_by_email(email, data_to_update)
        except Exception as e:
            logger.error(f"Error updating YouTube credentials by email: {str(e)}")
            raise InternalServerError("Failed to update YouTube credentials")


google_provider = GoogleProvider()
