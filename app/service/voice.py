from .play_ht import PlayHtService
from app.repositories.voice_repository import VoiceRepository
import logging
from bson import ObjectId

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


class VoiceService:

    def __init__(self):
        self.play_ht_service = PlayHtService()
        self.voice_repository = VoiceRepository()

    def clone_voice(
        self, user_id, sample_file_url, voice_name, language, gender, voice_person
    ):

        voice_id = self.play_ht_service.clone_voice_using_file(
            sample_file_url, voice_name
        )

        voice_data = self.voice_repository.create_voice(
            {
                "user_id": ObjectId(user_id),
                "voice_id": voice_id,
                "name": voice_name,
                "link": sample_file_url,
                "is_cloned": True,
                "language": language,
                "gender": gender,
                "voice_person": voice_person,
            }
        )

        voice_data = self.voice_repository.to_dict(voice_data)

        return voice_data

    def get_voices(self, user_id: str, page: int = 1, per_page: int = 10):
        result = self.voice_repository.get_voices_by_user_paginated(
            user_id, page, per_page
        )

        return {
            "voices": self.voice_repository.to_dict(result["voices"]),
            "page": result["page"],
            "per_page": result["per_page"],
            "total_count": result["total_count"],
            "total_pages": result["total_pages"],
        }

    def delete_voice(self, user_id, voice_id):

        voice = self.voice_repository.get_voice_by_id(voice_id)

        self.play_ht_service.delete_voice(voice["voice_id"])

        return self.voice_repository.delete_voice(user_id, voice_id)

    def get_default_voices(
        self,
        user_id,
        page: int,
        per_page: int,
        gender: str = None,
        is_cloned: bool = None,
    ):
        voice_count = self.voice_repository.get_voice_count()

        if voice_count < 10:
            voice_list, status_code = self.play_ht_service.get_list_of_voices()

            for voice in voice_list:
                self.voice_repository.create_voice(
                    {
                        "voice_id": voice["id"],
                        "name": voice["name"],
                        "link": voice["sample"],
                        "accent": voice["accent"],
                        "age": voice["age"],
                        "gender": voice["gender"],
                        "language": voice["language"],
                        "language_code": voice["language_code"],
                        "is_cloned": voice["is_cloned"],
                    }
                )

        result = self.voice_repository.get_voices_paginated(
            user_id, page, per_page, gender, is_cloned
        )

        return {
            "voices": result["voices"],
            "page": result["page"],
            "per_page": result["per_page"],
            "total_count": result["total_count"],
            "total_pages": result["total_pages"],
        }

    def process_playht_hook(self, payload):
        try:

            print("payload", payload)

            payload_id, status, url = (
                payload["id"],
                payload["status"],
                payload.get("url"),
            )

            if status == "queued":
                pass

            elif status == "completed":
                print("audio generated")
                # audio_data = self.video_repo.find_audio(
                #     {"audioIds": {"$in": [payload_id]}}
                # )
                # if not audio_data:
                #     raise ValueError("Audio does not exist")

                # a_uuid, audio_ids, audio_links = (
                #     audio_data["_id"],
                #     audio_data["audioIds"],
                #     audio_data["audioLinks"],
                # )

                # audio_index = (
                #     audio_ids.index(payload_id) if payload_id in audio_ids else -1
                # )
                # if audio_index != -1:
                #     audio_links[audio_index] = url
                #     valid_values_length = sum(1 for item in audio_links if item)
                #     update_data = {"audioLinks": audio_links}

                #     if len(audio_ids) == valid_values_length:
                #         update_data["status"] = "completed"

                #         avatar_res = self.msc_handler.generate_avatar_video(
                #             {
                #                 "avatar_id": "re47d944a0",  # TODO: make this dynamic
                #                 "audio_links": audio_links,
                #             }
                #         )
                #         avatar_videos = avatar_res["data"]
                #         video_ids = [obj["video_id"] for obj in avatar_videos]

                #         avatar_video_res = self.avatar_repo.save_avatar_video(
                #             {"videoIds": video_ids}
                #         )

                #         self.video_repo.update_video(
                #             {"audioId": a_uuid},
                #             {"avatarVideoId": avatar_video_res["_id"]},
                #         )

                #     self.video_repo.update_audio({"_id": a_uuid}, update_data)
                # else:
                #     logger.warning("Audio ID not found")
            elif status == "error":
                logger.error("Conversion job failed: %s", payload_id)
            else:
                raise ValueError("Unknown webhook status")

            return {"message": "Webhook received successfully"}
        except Exception as error:
            logger.exception("Error processing webhook: %s", str(error))
