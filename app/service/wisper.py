import os
from pathlib import Path
import whisper
from moviepy.editor import AudioFile<PERSON>lip


def extract_audio(file_path):
    """Extract audio from video file if necessary."""
    if file_path.endswith((".mp4", ".avi", ".mov")):
        audio = AudioFileClip(file_path)
        audio_path = file_path.rsplit(".", 1)[0] + ".mp3"
        audio.write_audiofile(audio_path)
        return audio_path
    return file_path


def segment_audio(audio_path, output_dir):
    """Segment audio into 20-minute chunks."""
    audio = AudioFileClip(audio_path)
    segment_duration = 1200  # 20 minutes in seconds
    segments = []

    for start in range(0, int(audio.duration), segment_duration):
        end = min(start + segment_duration, audio.duration)
        segment = audio.subclip(start, end)
        segment_path = os.path.join(output_dir, f"segment_{start}.mp3")
        segment.write_audiofile(segment_path)
        segments.append(segment_path)

    audio.close()
    return segments


def transcribe_audio(audio_path, model):
    """Transcribe audio using Whisper model."""
    result = model.transcribe(audio_path)
    return result["text"]


def format_timestamp(seconds):
    """Format timestamp for SRT file."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    milliseconds = int((seconds - int(seconds)) * 1000)
    return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"


def generate_subtitles(segments, model, output_file):
    """Generate subtitles in SRT format."""
    with open(output_file, "w", encoding="utf-8") as f:
        index = 1
        for segment_path in segments:
            result = model.transcribe(segment_path)
            for segment in result["segments"]:
                start = format_timestamp(segment["start"])
                end = format_timestamp(segment["end"])
                text = segment["text"].strip()
                f.write(f"{index}\n{start} --> {end}\n{text}\n\n")
                index += 1


def process_file(file_path, output_dir="./temp"):
    """Process video or audio file and generate transcription and subtitles."""
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # Extract audio if it's a video file
    audio_path = extract_audio(file_path)

    # Load Whisper model
    model = whisper.load_model("tiny.en")

    # Check file size and segment if necessary
    file_size = os.path.getsize(audio_path)
    whisper_default_size = 25 * 1024 * 1024  # 25 MB

    if file_size < whisper_default_size:
        transcription = transcribe_audio(audio_path, model)
        segments = [audio_path]
    else:
        segments = segment_audio(audio_path, output_dir)
        transcription = " ".join(
            [transcribe_audio(segment, model) for segment in segments]
        )

    # Generate subtitles
    subtitle_file = os.path.join(output_dir, "subtitles.srt")
    generate_subtitles(segments, model, subtitle_file)

    # Clean up temporary files
    if audio_path != file_path:
        os.remove(audio_path)
    for segment in segments:
        if segment != audio_path:
            os.remove(segment)

    # Save transcription to file
    transcription_file = os.path.join(output_dir, "transcription.txt")
    with open(transcription_file, "w", encoding="utf-8") as f:
        f.write(transcription)

    return transcription, subtitle_file
