import logging
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.executors.pool import ThreadPoolExecutor

from app.database.connection import client

from .video import upload_video
import atexit
import threading
import signal
import sys


# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


jobstores = {
    "default": MongoDBJobStore(
        database="youtube_scheduler", collection="jobs", client=client
    )
}
executors = {"default": ThreadPoolExecutor(20)}
job_defaults = {"coalesce": False, "max_instances": 3}


class YouTubeScheduler:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(YouTubeScheduler, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores, executors=executors, job_defaults=job_defaults
        )
        self.is_shutting_down = threading.Event()
        self.scheduler.start()
        logger.info("Scheduler initialized and started.")
        self._initialized = True

    @classmethod
    def upload_schedule_video(cls, job_data):
        instance = cls()
        if instance.is_shutting_down.is_set():
            logger.warning("Scheduler is shutting down. Skipping job execution.")
            return

        logger.info(f"Starting upload_schedule_video at {datetime.now()}")
        logger.info(f"Job data: {job_data}")
        try:
            upload_video_service = upload_video.UploadVideoService()
            video_data = upload_video_service.upload_schedule_video(job_data)
            logger.info(f"Video upload completed. Video data: {video_data}")
        except Exception as e:
            logger.error(f"Error in upload_schedule_video: {str(e)}", exc_info=True)
        logger.info(f"Finished upload_schedule_video at {datetime.now()}")

    def create_job_to_upload(self, schedule_video_id, scheduled_time):
        if self.is_shutting_down.is_set():
            logger.warning("Scheduler is shutting down. Cannot create new jobs.")
            return None

        logger.info(
            f"Creating job for video ID {schedule_video_id} at {scheduled_time}"
        )
        try:
            job = self.scheduler.add_job(
                YouTubeScheduler.upload_schedule_video,
                trigger="date",
                run_date=scheduled_time,
                args=[{"schedule_video_id": str(schedule_video_id)}],
                id=str(schedule_video_id),
            )
            logger.info(
                f"Job created successfully: {job.id}, scheduled for {scheduled_time}"
            )
            return job
        except Exception as e:
            logger.error(f"Error creating job: {str(e)}", exc_info=True)

    def get_jobs(self):
        jobs = self.scheduler.get_jobs()
        job_info = [
            {"job_id": job.id, "run_date": str(job.next_run_time)} for job in jobs
        ]
        logger.info(f"Current jobs: {job_info}")
        return job_info

    def delete_job(self, job_id):
        logger.info(f"Attempting to delete job {job_id}")
        job = self.scheduler.get_job(job_id)
        if job:
            self.scheduler.remove_job(job_id)
            logger.info(f"Job {job_id} removed successfully")
            return {"message": f"Job {job_id} removed successfully"}, 200
        logger.warning(f"Job {job_id} not found")
        return {"message": "Job not found"}, 404

    def shutdown(self):
        if self.is_shutting_down.is_set():
            logger.info("Shutdown already in progress")
            return

        logger.info("Initiating scheduler shutdown...")
        self.is_shutting_down.set()
        self.scheduler.shutdown(wait=False)
        logger.info("Scheduler shut down complete.")


# Create a single instance of YouTubeScheduler
scheduler = YouTubeScheduler()


def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}")
    scheduler.shutdown()
    sys.exit(0)


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Register the shutdown function to be called when the interpreter is exiting
atexit.register(scheduler.shutdown)
