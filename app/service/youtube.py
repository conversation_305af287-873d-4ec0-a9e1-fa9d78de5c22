from typing import Annotated
from langchain_community.document_loaders import YoutubeLoader
from pytube import YouTube
from werkzeug.exceptions import NotFound, BadRequest
from datetime import timedelta
import logging
import os
import re
import yt_dlp
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound
import time
import random
import httplib2
import json
from fake_useragent import UserAgent
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from termcolor import colored
from apiclient.errors import HttpError
from google.oauth2.credentials import Credentials  # Add this import
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from .google import google_provider
from .proxy import proxy_list

from config import (
    GOOGLE_CONSOLE_API_KEY,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_CLIENT_ID,
)

output_path = "./temp/"

# Explicitly tell the underlying HTTP transport library not to retry, since
# we are handling retry logic ourselves.
httplib2.RETRIES = 1

# Maximum number of times to retry before giving up.
MAX_RETRIES = 10

# Always retry when these exceptions are raised.
RETRIABLE_EXCEPTIONS = (httplib2.HttpLib2Error, IOError, httplib2.ServerNotFoundError)

# Always retry when an apiclient.errors.HttpError with one of these status
# codes is raised.
RETRIABLE_STATUS_CODES = [500, 502, 503, 504]

# The CLIENT_SECRETS_FILE variable specifies the name of a file that contains
# the OAuth 2.0 information for this application, including its client_id and
# client_secret.
CLIENT_SECRETS_FILE = "./client_secret.json"

# This OAuth 2.0 access scope allows an application to upload files to the
# authenticated user's YouTube channel, but doesn't allow other types of access.
# YOUTUBE_UPLOAD_SCOPE = "https://www.googleapis.com/auth/youtube.upload"
SCOPES = [
    "https://www.googleapis.com/auth/youtube.upload",
    # "https://www.googleapis.com/auth/youtube",
    # "https://www.googleapis.com/auth/youtubepartner",
]
YOUTUBE_API_SERVICE_NAME = "youtube"
YOUTUBE_API_VERSION = "v3"

# This variable defines a message to display if the CLIENT_SECRETS_FILE is
# missing.
MISSING_CLIENT_SECRETS_MESSAGE = f"""
WARNING: Please configure OAuth 2.0

To make this sample run you will need to populate the client_secrets.json file
found at:
{os.path.abspath(os.path.join(os.path.dirname(__file__), CLIENT_SECRETS_FILE))}

with information from the API Console
https://console.cloud.google.com/

For more information about the client_secrets.json file format, please visit:
https://developers.google.com/api-client-library/python/guide/aaa_client_secrets
"""

VALID_PRIVACY_STATUSES = ("public", "private", "unlisted")

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


def extract_video_id(url):
    try:
        parsed_url = urlparse(url)
        if parsed_url.hostname in ("youtu.be", "www.youtu.be"):
            return parsed_url.path[1:]
        if parsed_url.hostname in ("youtube.com", "www.youtube.com"):
            if parsed_url.path == "/watch":
                return parse_qs(parsed_url.query)["v"][0]
            if parsed_url.path.startswith(("/embed/", "/v/")):
                return parsed_url.path.split("/")[2]
        return None
    except ValueError as e:
        logger.error(f"Error extract_video_id: {str(e)}")
        return None


def validate_youtube_link(link):
    youtube_regex = r"^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$"
    if not re.match(youtube_regex, link):
        raise BadRequest("Invalid YouTube link")
    video_id = extract_video_id(link)
    if not video_id:
        logger.error("YouTube video ID not found")
        raise NotFound("YouTube video ID not found")
    duration = get_video_duration(video_id)
    logger.info(f"video duration {duration}")
    if duration > 1800:  # 30 minutes = 1800 seconds
        raise BadRequest("Video length should be less than 30 minutes")
    return link, video_id


def get_transcription_from_yt_video(video_url, max_retries=2):
    """
    Fetches the transcription of a YouTube video.
    Args:
    video_url (str): The URL of the YouTube video.
    max_retries (int): Maximum number of retry attempts (default is 3).

    Returns:
    str: The transcription text of the video.

    Raises:
    ValueError: If the YouTube link is invalid.
    NotFound: If the YouTube video ID is not found.
    TranscriptsDisabled: If transcripts are disabled for the video.
    NoTranscriptFound: If no transcript is available for the video.
    Exception: For other unexpected errors.
    """
    link, video_id = validate_youtube_link(video_url)
    video_title = None

    proxy_lists = proxy_list()
    for attempt in range(max_retries):
        try:
            transcript = YouTubeTranscriptApi.get_transcript(
                video_id,
                proxies={
                    "https": random.choice(proxy_lists),
                    "http": random.choice(proxy_lists),
                },
                cookies="./asset/www.youtube.com_cookies.txt",
            )
            transcript_text = " ".join(entry["text"] for entry in transcript)
            return video_title, transcript_text
        # except (TranscriptsDisabled, NoTranscriptFound) as e:
        #     logger.error(f"Transcript error for video {video_id}: {str(e)}")
        #     return None
        except Exception as e:
            logger.error(f"Attempt {attempt + 1} failed: {str(e)}")
            if attempt < max_retries - 1:
                wait_time = 2  # Exponential backoff
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error("Max retries reached. Unable to fetch transcript.")

    try:
        # Create a YouTube object
        yt = YouTube(link)
        # Extract and return the title
        video_title = yt.title
    except Exception as e:
        logger.error(f"title Attempt failed: {str(e)}")
        return None, None

    return video_title, None


def get_transcription_from_yt_video1(video_url):
    # Get the video ID from the URL
    video_id = video_url.split("v=")[-1]

    # Fetch the transcript from the YouTube Transcript API
    transcript = YouTubeTranscriptApi.get_transcript(video_id)

    # Initialize an empty list to store the transcript with timestamps
    transcript_with_timestamps = []

    # Iterate through the transcript and append each text chunk with its start and end time
    for chunk in transcript:
        text = chunk["text"]
        start_time = chunk["start"]
        end_time = chunk["start"] + chunk["duration"]
        timestamp = (start_time, end_time)
        transcript_with_timestamps.append((text, timestamp))

    return transcript_with_timestamps


def download_youtube_video(
    url, output_path="./temp/", max_retries=4, delay_range=(3, 10)
):
    ydl_opts = {
        "format": "bestvideo[height=720][width=1280][ext=mp4]+bestaudio[ext=m4a]/best[height=720][width=1280][ext=mp4]/best[ext=mp4]/best",
        "outtmpl": os.path.join(output_path, "%(title)s.%(ext)s"),
        "quiet": False,
        "no_warnings": True,
        "ignoreerrors": False,
        "cookiefile": "./asset/www.youtube.com_cookies.txt",
    }

    retries = 0
    while retries < max_retries:
        try:
            # Refresh cookies before each attempt
            ydl_opts["proxy"] = random.choice(proxy_list())
            ydl_opts["user-agent"] = UserAgent().random
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                available_formats = info.get("formats", [])

                # Find the exact 1920x1080 format if available
                target_format = next(
                    (
                        f
                        for f in available_formats
                        if f.get("width") == 1280 and f.get("height") == 720
                    ),
                    None,
                )

                if target_format:
                    ydl_opts["format"] = (
                        f"{target_format['format_id']}+bestaudio[ext=m4a]/best[ext=mp4]/best"
                    )
                    print(f"Found 1280x720 format: {target_format['format_id']}")
                else:
                    print(
                        "Exact 1280x720 format not found. Using best available format."
                    )

                # Perform the actual download
                info = ydl.extract_info(url, download=True)
                filename = ydl.prepare_filename(info)

            print(f"Download completed: {filename}")
            return filename
        except yt_dlp.utils.DownloadError as e:
            print(f"Download error: {str(e)}")
            retries += 1
            delay = random.uniform(*delay_range)
            print(f"Retrying in {delay:.2f} seconds...")
            time.sleep(delay)
        except Exception as e:
            print(f"An unexpected error occurred: {str(e)}")
            retries += 1
            delay = random.uniform(*delay_range)
            print(f"Retrying in {delay:.2f} seconds...")
            time.sleep(delay)

    print("Maximum number of retries reached. Unable to download the video.")
    return None


def get_transcription_from_video(video_url: Annotated[str, "youtube video url"]):
    print("video_url", video_url)
    try:

        # Transcribe the videos to text
        loader = YoutubeLoader.from_youtube_url(video_url, add_video_info=False)

        docs = loader.load()

        # Combine doc
        combined_docs = [doc.page_content for doc in docs]

        text = " ".join(combined_docs)

        return text
    except Exception as e:
        print(f"An error occurred in get_transcription_from_video: {str(e)}")
        logging.exception("An error occurred while loading the transcript")
        raise


def get_authenticated_service():

    credentials = None
    if os.path.exists("token.json"):
        with open("token.json", "r") as token_file:
            info = json.load(token_file)
        credentials = Credentials.from_authorized_user_info(info=info, scopes=SCOPES)

    if not credentials or not credentials.valid:
        if credentials and credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                CLIENT_SECRETS_FILE, SCOPES
            )
            credentials = flow.run_local_server(port=0)

        with open("token.json", "w") as token:
            token.write(credentials.to_json())

    return build(YOUTUBE_API_SERVICE_NAME, YOUTUBE_API_VERSION, credentials=credentials)


def initialize_upload(youtube: any, options: dict):
    """
    This method uploads a video to YouTube.

    Args:
        youtube (any): The authenticated YouTube service.
        options (dict): The options to upload the video with.

    Returns:
        response: The response from the upload process.
    """

    tags = None
    if options["keywords"]:
        tags = options["keywords"].split(",")

    body = {
        "snippet": {
            "title": options["title"],
            "description": options["description"],
            "tags": tags,
            "categoryId": options["category"],
        },
        "status": {
            "privacyStatus": options["privacyStatus"],
            "madeForKids": False,  # Video is not made for kids
            "selfDeclaredMadeForKids": False,  # You declare that the video is not made for kids
        },
    }

    # Call the API's videos.insert method to create and upload the video.
    insert_request = youtube.videos().insert(
        part=",".join(body.keys()),
        body=body,
        media_body=MediaFileUpload(options["file"], chunksize=-1, resumable=True),
    )

    return resumable_upload(insert_request)


def resumable_upload(insert_request: MediaFileUpload):
    """
    This method implements an exponential backoff strategy to resume a
    failed upload.

    Args:
        insert_request (MediaFileUpload): The request to insert the video.

    Returns:
        response: The response from the upload process.
    """
    response = None
    error = None
    retry = 0
    while response is None:
        try:
            print(colored(" => Uploading file...", "magenta"))
            status, response = insert_request.next_chunk()
            if "id" in response:
                print(f"Video id '{response['id']}' was successfully uploaded.")
                return response
        except HttpError as e:
            if e.resp.status in RETRIABLE_STATUS_CODES:
                error = f"A retriable HTTP error {e.resp.status} occurred:\n{e.content}"
            else:
                raise
        except RETRIABLE_EXCEPTIONS as e:
            error = f"A retriable error occurred: {str(e)}"

        if error is not None:
            print(colored(error, "red"))
            retry += 1
            if retry > MAX_RETRIES:
                raise Exception("No longer attempting to retry.")

            max_sleep = 2**retry
            sleep_seconds = random.random() * max_sleep
            print(
                colored(
                    f" => Sleeping {sleep_seconds} seconds and then retrying...", "blue"
                )
            )
            time.sleep(sleep_seconds)


def youtube_auth(email, youtube_credentials):

    old_credentials = youtube_credentials
    # # Get the authenticated YouTube service
    credentials = Credentials(
        token=youtube_credentials.get("token"),
        refresh_token=youtube_credentials.get("refresh_token"),
        token_uri=youtube_credentials.get("token_uri"),
        client_id=GOOGLE_CLIENT_ID,
        client_secret=GOOGLE_CLIENT_SECRET,
        scopes=youtube_credentials.get("scopes"),
    )

    # Calculate the refresh token expiration time
    # Assuming the refresh token doesn't expire, set it to 30 days from now
    refresh_token_expires_at = datetime.now() + timedelta(days=7)

    if not credentials.valid:
        if credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
            youtube_credentials = {
                "token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_uri": credentials.token_uri,
                "scopes": credentials.scopes,
                "expires_at": credentials.expiry.isoformat(),
                "refresh_token_expires_at": old_credentials.get(
                    "refresh_token_expires_at", refresh_token_expires_at.isoformat()
                ),
            }
            google_provider.update_youtube_credentials_using_email(
                email, youtube_credentials
            )
        else:
            return {"message": "Token expired. Please reauthenticate."}, 401

    youtube = build(
        "youtube",
        "v3",
        credentials=credentials,
    )

    return youtube


def upload_video(
    email,
    youtube_credentials,
    video_path,
    title,
    description,
    category,
    keywords,
    privacy_status,
):
    try:

        youtube = youtube_auth(email, youtube_credentials)

        # Retrieve and print the channel ID for the authenticated user
        channels_response = youtube.channels().list(mine=True, part="id").execute()

        for channel in channels_response["items"]:
            print(colored(f" => Channel ID: {channel['id']}", "blue"))

        # Initialize the upload process
        video_response = initialize_upload(
            youtube,
            {
                "file": video_path,  # The path to the video file
                "title": title,
                "description": description,
                "category": category,
                "keywords": keywords,
                "privacyStatus": privacy_status,
            },
        )
        return video_response  # Return the response from the upload process
    except HttpError as e:
        print(
            colored(f"[-] An HTTP error {e.resp.status} occurred:\n{e.content}", "red")
        )
        if e.resp.status in [401, 403]:
            # Here you could refresh the credentials and retry the upload
            youtube = (
                get_authenticated_service()
            )  # This will prompt for re-authentication if necessary
            video_response = initialize_upload(
                youtube,
                {
                    "file": video_path,
                    "title": title,
                    "description": description,
                    "category": category,
                    "keywords": keywords,
                    "privacyStatus": privacy_status,
                },
            )
            return video_response
        else:
            raise e


def get_trending_videos_and_topics(youtube_credentials, email, per_page):
    youtube = youtube_auth(email, youtube_credentials)

    try:
        # Get trending videos
        videos_request = youtube.videos().list(
            part="snippet,statistics",
            chart="mostPopular",
            regionCode="US",  # Change this to get trending videos for a specific country
            maxResults=10,  # Adjust this to get more or fewer results
            videoCategoryId=28,
        )
        videos_response = videos_request.execute()

        # Get trending topics (guideCategories)
        topics_request = youtube.videoCategories().list(
            part="snippet",
            regionCode="US",  # Change this to get trending topics for a specific country
            hl="en",  # Change this for different languages
        )

        topics_response = topics_request.execute()

        # Process and return the results
        trending_videos = [
            {
                "title": item["snippet"]["title"],
                "description": item["snippet"]["description"],
                "publish_time": item["snippet"]["publishedAt"],
                "channel_title": item["snippet"]["channelTitle"],
                "view_count": item["statistics"]["viewCount"],
                "like_count": item["statistics"].get("likeCount", None),
                "comment_count": item["statistics"].get("commentCount", None),
                "video_id": item["id"],
                "thumbnail": item["snippet"]["thumbnails"]
                .get("maxres", None)
                .get("url", None),
            }
            for item in videos_response["items"]
        ]

        trending_topics = [
            {"title": item["snippet"]["title"], "id": item["id"]}
            for item in topics_response["items"]
        ]

        return {"trending_videos": trending_videos, "trending_topics": trending_topics}

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")
        return None


def update_thumbnail_image(email, youtube_credentials, video_id, thumbnail_path):

    try:
        youtube = youtube_auth(email, youtube_credentials)
        youtube.thumbnails().set(
            videoId=video_id, media_body=MediaFileUpload(thumbnail_path)
        ).execute()

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")
        return None


def get_video_duration(video_id):
    try:
        # Set up YouTube API client
        youtube = build("youtube", "v3", developerKey=GOOGLE_CONSOLE_API_KEY)
        # Call the videos().list method to retrieve video details
        request = youtube.videos().list(part="contentDetails", id=video_id)
        response = request.execute()
        # Extract duration from the response
        if "items" in response and len(response["items"]) > 0:
            duration = response["items"][0]["contentDetails"]["duration"]
            # Convert duration from ISO 8601 format to more readable format
            duration_obj = re.match(r"PT(\d+H)?(\d+M)?(\d+S)?", duration).groups()
            hours = int(duration_obj[0][:-1]) if duration_obj[0] else 0
            minutes = int(duration_obj[1][:-1]) if duration_obj[1] else 0
            seconds = int(duration_obj[2][:-1]) if duration_obj[2] else 0

            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds
        else:
            return 0

    except HttpError as e:
        print(
            f"An HTTP error get_video_duration {e.resp.status} occurred:\n{e.content}"
        )
        return 0
