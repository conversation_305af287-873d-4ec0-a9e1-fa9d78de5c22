import http.client
import json
import os
from serpapi import GoogleSearch
from typing import Annotated
from .web_scrap import extract_article
from langchain_community.adapters.openai import convert_openai_messages
from langchain_openai import ChatOpenAI


from config import SERPER_API_KEY

client = GoogleSearch({"api_key": SERPER_API_KEY})


def web_search(
    query: Annotated[str, "The search query"],
) -> Annotated[str, "The search results"]:
    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps({"q": query, "hl": "en", "gl": "us"})
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json",
    }
    conn.request("POST", "/search", payload, headers)
    res = conn.getresponse()
    data = res.read()
    print(data.decode("utf-8"))
    return data.decode("utf-8")


def images_search(
    query: Annotated[str, "The search query"], num=10
) -> Annotated[str, "The search results"]:
    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps({"q": query, "hl": "en", "gl": "us", "num": num})
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json",
    }
    conn.request("POST", "/images", payload, headers)
    res = conn.getresponse()
    data = res.read()

    response_data = json.loads(data.decode("utf-8"))

    # Get the list of image results
    images = response_data.get("images", [])

    # Initialize an empty list to store image URLs
    image_urls = []

    # Iterate over the image results and extract the 'imageUrl' for each image
    for image in images:
        image_url = image.get("imageUrl")
        if image_url:
            image_urls.append(image_url)

    # Print the list of image URLs
    print(image_urls)
    return image_urls


def video_search(
    query: Annotated[str, "The search query"], num=10
) -> Annotated[str, "The search results"]:
    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps({"q": query, "hl": "en", "gl": "us", "num": num})
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json",
    }
    conn.request("POST", "/videos", payload, headers)
    res = conn.getresponse()
    data = res.read()

    response_data = json.loads(data.decode("utf-8"))

    # Get the list of image results
    images = response_data.get("videos", [])

    # Initialize an empty list to store image URLs
    video_urls = []

    # Iterate over the image results and extract the 'imageUrl' for each image
    for image in images:
        video_url = image.get("link")
        if video_url:
            video_urls.append(video_url)

    # Print the list of image URLs
    print(video_urls)
    return video_urls


def search_images_videos(
    query,
    api_key=SERPER_API_KEY,
    engine="google",
    location="Austin,Texas",
    google_domain="google.com",
    gl="us",
    hl="en",
):
    """
    Performs a search on Google for images and videos related to the given query.

    Args:
        query (str): The search query string.
        api_key (str): Your SerpAPI API key.
        engine (str, optional): The search engine to use (e.g., 'google', 'bing'). Defaults to 'google'.
        location (str, optional): The location to use for the search. Defaults to 'Austin,Texas'.
        google_domain (str, optional): The Google domain to use for the search. Defaults to 'google.com'.
        gl (str, optional): The Google country code for the search. Defaults to 'us'.
        hl (str, optional): The Google language code for the search. Defaults to 'en'.

    Returns:
        dict: A dictionary containing the search results for images and videos from SerpAPI.
    """
    params = {
        "q": query,
        "api_key": api_key,
        "engine": engine,
        "location": location,
        "google_domain": google_domain,
        "gl": gl,
        "hl": hl,
        # "tbm": "isch,vid",  # Search for images and videos
    }

    results = client.search(params)

    return results


def search_from_serper(
    query: Annotated[str, "The search query"]
) -> Annotated[str, "The search results"]:
    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps({"q": query, "hl": "en", "gl": "us", "tbs": "qdr:w", "num": 5})
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json",
    }
    conn.request("POST", "/news", payload, headers)
    res = conn.getresponse()
    data = res.read()

    response_data = json.loads(data.decode("utf-8"))

    # Get the list of image results
    news = response_data.get("news", [])

    print("news", news)

    data = []
    for result in news:
        title = result.get("title")
        url = result.get("link")
        if title and url:
            content = extract_article(url)
            data.append({"title": title, "content": content})

    prompt = [
        {
            "role": "system",
            "content": (
                "You are an AI critical thinker and research assistant. Your purpose is to write "
                "well-written, critically acclaimed, objective, and structured reports on given text. "
                "Your reports must adhere to the following guidelines:\n"
                "1. Include thoughts and insights from industry leaders (mandatory).\n"
                "2. Analyze economic and market growth impacts.\n"
                "3. Provide detailed information with sources clearly mentioned.\n"
                "4. Ensure the content is extremely informative and comprehensive.\n"
                "5. Include relevant examples to support facts and statements.\n"
                "6. Maintain a critical and analytical perspective throughout the report."
            ),
        },
        {
            "role": "user",
            "content": (
                f'Information: """{data}"""\n\n'
                f'Using the above information, please provide a detailed report answering the following query: "{query}"\n'
                "Note: Do not include a conclusion, references, or summary section in your report."
            ),
        },
    ]

    lc_messages = convert_openai_messages(prompt)

    response = ChatOpenAI(
        model="gpt-4o", openai_api_key=os.environ["OPENAI_API_KEY"]
    ).invoke(lc_messages)
    return response.content


def news_search(
    queries,
):
    q = []
    for query in queries:
        q.append(
            {
                "q": query,
                "gl": "us",
                "location": "United States",
                "hl": "en",
                "tbs": "qdr:d",
                "num": 20,
            }
        )

    conn = http.client.HTTPSConnection("google.serper.dev")
    payload = json.dumps(q)
    headers = {
        "X-API-KEY": SERPER_API_KEY,
        "Content-Type": "application/json",
    }
    conn.request("POST", "/news", payload, headers)
    res = conn.getresponse()
    data = res.read()
    return json.loads(data.decode("utf-8"))
