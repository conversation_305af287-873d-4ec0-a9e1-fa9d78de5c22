import stripe
from typing import Dict, Any, Optional, List
import logging
from app.exceptions.subscription_exceptions import PaymentError

from app.constant.subscription import Currencies


from config import STRIPE_PRIVATE_KEY

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


class StripeService:
    def __init__(self):
        self.stripe = stripe
        self.stripe.api_key = STRIPE_PRIVATE_KEY

    def instance(self):
        return self.stripe

    def create_product(
        self, name: str, description: Optional[str] = None
    ) -> stripe.Product:
        """
        Create a new Stripe product.

        :param name: Product's name
        :param description: Product's description (optional)
        :return: The created Stripe product object
        """
        product = stripe.Product.create(
            name=name,
            description=description,
        )
        return product

    def create_price(
        self,
        product_id: str,
        unit_amount: int,
        currency: str,
        recurring_interval: Optional[str] = None,
    ) -> stripe.Price:
        """
        Create a new Stripe price for a product.

        :param product_id: The ID of the product
        :param unit_amount: The unit amount in cents (e.g., $10.00 is 1000)
        :param currency: The currency code (e.g., 'usd')
        :param recurring_interval: The billing interval (e.g., 'month', 'year') (optional)
        :return: The created Stripe price object
        """
        price_data = {
            "unit_amount": unit_amount,
            "currency": currency,
            "product": product_id,
        }
        if recurring_interval:
            price_data["recurring"] = {"interval": recurring_interval}

        price = stripe.Price.create(**price_data)
        return price

    def create_checkout_session(
        self, customer_id: str, price_id: str, success_url: str, cancel_url: str
    ) -> stripe.checkout.Session:
        """
        Create a new Stripe checkout session for a subscription.

        :param customer_id: The ID of the customer
        :param price_id: The ID of the price
        :param success_url: The URL to which Stripe should redirect when checkout is complete
        :param cancel_url: The URL to which Stripe should redirect if checkout is canceled
        :return: The created Stripe checkout session object
        """

        try:
            session = stripe.checkout.Session.create(
                customer=customer_id,
                payment_method_types=["card"],
                line_items=[
                    {
                        "price": price_id,
                        "quantity": 1,
                    }
                ],
                mode="subscription",
                success_url=success_url,
                cancel_url=cancel_url,
            )
            return session

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {str(e)}")
            raise PaymentError(f"Error creating Stripe checkout session: {str(e)}")
        except Exception as e:
            logger.exception(
                f"Unexpected error creating Stripe checkout session: {str(e)}"
            )
            raise PaymentError(
                f"Unexpected error creating Stripe checkout session: {str(e)}"
            )

    def cancel_subscription(self, subscription_id: str) -> stripe.Subscription:
        """
        Cancel a Stripe subscription.
        :param subscription_id: The ID of the subscription to be canceled
        :return: The canceled Stripe subscription object
        """
        try:
            subscription = stripe.Subscription.delete(subscription_id)
            return subscription
        except Exception as e:
            logger.error(f"cancel subscription: {str(e)}")
            raise

    def update_subscription(
        self, subscription_id: str, new_price_id: str
    ) -> stripe.Subscription:
        """
        Update a Stripe subscription to a new price.

        :param subscription_id: The ID of the subscription to be updated
        :param new_price_id: The ID of the new price
        :return: The updated Stripe subscription object
        """
        subscription = stripe.Subscription.retrieve(subscription_id)
        updated_subscription = stripe.Subscription.modify(
            subscription_id,
            cancel_at_period_end=False,
            items=[
                {
                    "id": subscription["items"]["data"][0].id,
                    "price": new_price_id,
                }
            ],
        )
        return updated_subscription

    def create_customer(self, customer_data: Dict[str, Any]) -> str:
        try:
            customer = self.stripe.Customer.create(**customer_data)
            return customer.id
        except Exception as e:
            print("Error creating customer error: " + str(e))
            logger.error(f"Stripe creating customer: {str(e)}")
            raise

    def create_plan(self, plan_data: Dict[str, Any]) -> Dict[str, str]:
        try:
            stripe_create_product = {
                "name": plan_data["name"],
                "active": plan_data["active"],
                "description": plan_data["description"],
                "images": [plan_data.get("image", "DEFAULT_PLAN_IMAGE")],
                "default_price_data": {
                    "currency": Currencies.USD.value,
                    "unit_amount": int(plan_data["price"] * 100),
                    "recurring": {
                        "interval": plan_data["renewal_period"],
                        "interval_count": plan_data["renewal_number"],
                    },
                },
            }
            product = self.stripe.Product.create(**stripe_create_product)
            return {
                "stripe_product_id": product.id,
                "stripe_price_id": product.default_price,
            }
        except Exception as e:
            logger.error(f"Stripe creating plan: {str(e)}")
            raise

    def retrieve_invoice_link(self, invoice_id: str) -> str:
        try:
            invoice_data = self.stripe.Invoice.retrieve(invoice_id)
            return invoice_data.invoice_pdf
        except Exception as e:
            logger.error(f"Stripe retrieving invoice link: {str(e)}")
            raise

    def update_product(self, product_id, product_data):
        try:
            product_data = self.stripe.Product.modify(product_id, **product_data)
            return product_data.id
        except Exception as e:
            logger.error(f"update product error: {str(e)}")
            raise

    def delete_product(self, product_id, price_id):
        try:

            # # Remove the default price from the product
            # self.stripe.Product.modify(product_id, default_price=None)

            # # Delete each price
            # self.stripe.Price.modify(price_id, active=False)

            product_data = self.stripe.Product.delete(product_id)
            return product_data.id
        except Exception as e:
            logger.error(f"delete product error: {str(e)}")
            raise

    def create_new_price(self, amount: int, interval, prod_id: str) -> str:
        try:
            price_data = self.stripe.Price.create(
                unit_amount=amount,
                currency=Currencies.USD.value,
                product=prod_id,
                recurring={
                    "interval": interval,
                    "interval_count": 1,
                },
            )

            # Update the product with the new default price
            stripe.Product.modify(prod_id, default_price=price_data.id)
            return price_data.id
        except Exception as e:
            logger.error(f"Stripe creating new price: {str(e)}")
            raise

    def list_payment_methods(self, stripe_customer_id: str) -> List[Dict[str, Any]]:
        try:
            return self.stripe.PaymentMethod.list(
                customer=stripe_customer_id,
                type="card",
            )
        except Exception as e:
            logger.error(f"Stripe listing payment methods: {str(e)}")
            raise

    def add_payment_methods(
        self, card_data: Dict[str, Any], stripe_customer_id: str
    ) -> Dict[str, str]:
        try:
            payment_method = self.stripe.PaymentMethod.create(
                type="card",
                card=card_data,
            )

            self.stripe.PaymentMethod.attach(
                payment_method.id,
                customer=stripe_customer_id,
            )

            return {
                "payment_method_id": payment_method.id,
                "card_brand": payment_method.card.brand,
            }
        except Exception as e:
            logger.error(f"Stripe adding payment method: {str(e)}")
            raise

    def get_customer_subscription_by_id(self, sub_id: str) -> Dict[str, Any]:
        try:
            return self.stripe.Subscription.retrieve(sub_id)
        except Exception as e:
            logger.error(f"Stripe retrieving customer subscription: {str(e)}")
            raise

    def get_subscription_item_by_id(self, item_id: str) -> Dict[str, Any]:
        try:
            return self.stripe.SubscriptionItem.retrieve(item_id)
        except Exception as e:
            logger.error(f"Stripe retrieving customer subscription: {str(e)}")
            raise

    def delete_subscription_schedule(
        self, stripe_subscription_schedule_id: str
    ) -> Dict[str, Any]:
        try:
            subscription = self.stripe.SubscriptionSchedule.retrieve(
                stripe_subscription_schedule_id
            )

            if subscription.status != "canceled":
                return self.stripe.SubscriptionSchedule.cancel(
                    stripe_subscription_schedule_id
                )
            return subscription
        except Exception as e:
            logger.error(f"Stripe deleting subscription schedule: {str(e)}")
            raise

    def create_subscription_schedule(
        self,
        cus_id: str,
        time: int,
        price_id: str,
        payment_resource_id: str,
    ) -> Dict[str, Any]:
        try:
            return self.stripe.SubscriptionSchedule.create(
                customer=cus_id,
                start_date=time + 300,
                end_behavior="release",
                phases=[
                    {
                        "items": [{"price": price_id}],
                        "default_payment_method": payment_resource_id,
                        "collection_method": "charge_automatically",
                    }
                ],
            )
        except Exception as e:
            logger.error(f"Stripe creating subscription schedule: {str(e)}")
            raise

    def cancel_subscription_at_end(self, sub_id: str) -> Dict[str, Any]:
        try:
            cancellation_response = self.stripe.Subscription.modify(
                sub_id,
                cancel_at_period_end=True,
            )
            return {
                "is_cancelled_success": cancellation_response.cancel_at_period_end,
                "cancel_at": cancellation_response.cancel_at,
            }
        except Exception as e:
            logger.error(f"Stripe cancelling subscription: {str(e)}")
            raise

    def cancel_subscription_now(self, sub_id: str) -> Dict[str, Any]:
        try:
            return self.stripe.Subscription.cancel(sub_id)
        except Exception as e:
            logger.error(f"Stripe cancelling subscription now: {str(e)}")
            raise

    def set_default_payment_method(
        self, stripe_customer_id: str, stripe_payment_resource_id: str
    ) -> Dict[str, Any]:
        try:
            return self.stripe.Customer.modify(
                stripe_customer_id,
                invoice_settings={"default_payment_method": stripe_payment_resource_id},
            )
        except Exception as e:
            logger.error(f"Stripe setting default payment method: {str(e)}")
            raise

    def find_user(self, customer_id: str) -> Dict[str, Any]:
        try:
            return self.stripe.Customer.retrieve(customer_id)
        except Exception as e:
            logger.error(f"Stripe finding user: {str(e)}")
            raise

    def fetch_payment_intent(self, payment_intent_id: str) -> Dict[str, Any]:
        try:
            return self.stripe.PaymentIntent.retrieve(payment_intent_id)
        except Exception as e:
            logger.error(f"Stripe fetching payment intent: {str(e)}")
            raise

    def activate_free_plan(
        self,
        free_plan_price_id: str,
        user_stripe_customer_id: str,
        stripe_subscription_id: str = None,
    ) -> None:
        """
        Used to move the user to free plan

        Args:
        free_plan_price_id (str): free plan price id of stripe
        user_stripe_customer_id (str): customer stripe account id
        stripe_subscription_id (str, optional): existing subscription id, if any

        Returns:
        None
        """
        if stripe_subscription_id:
            sub = self.stripe.Subscription.retrieve(stripe_subscription_id)
            price_id = sub.plan.id

            if price_id == free_plan_price_id:
                return self.stripe.Subscription.modify(
                    stripe_subscription_id,
                    items=[{"price": free_plan_price_id}],
                    payment_settings={
                        "payment_method_types": ["card"],
                        "save_default_payment_method": "on_subscription",
                    },
                    expand=["latest_invoice.payment_intent"],
                )

        return self.create_free_subscription(
            free_plan_price_id, user_stripe_customer_id
        )

    def create_free_subscription(
        self, free_plan_price_id: str, user_stripe_customer_id: str
    ) -> None:

        try:
            self.stripe.Subscription.create(
                customer=user_stripe_customer_id,
                items=[{"price": free_plan_price_id}],
                payment_settings={
                    "payment_method_types": ["card"],
                    "save_default_payment_method": "on_subscription",
                },
                expand=["latest_invoice.payment_intent"],
            )

        except Exception as e:
            logger.error(f"Stripe create free subscription: {str(e)}")
            raise
