import logging
from bson import ObjectId
from werkzeug.exceptions import BadRequest

from app.constant.subscription import SubscriptionStatus
from .stripe import StripeService
from app.repositories.subscription_plan_repository import SubscriptionPlanRepository
from app.repositories.transition_repository import TransactionRepository
from app.repositories.user_repository import AuthRepository
from app.repositories.subscription_repository import SubscriptionRepository
from app.exceptions.subscription_exceptions import (
    SubscriptionError,
    PaymentError,
    SubscriptionActiveError,
)

from config import FRONTEND_ENDPOINT, STRIPE_WEBHOOK_SECRET


# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


class PaymentService:
    def __init__(self):
        self.subscription_plan_repository = SubscriptionPlanRepository()
        self.subscription_repository = SubscriptionRepository()
        self.stripe_service = StripeService()
        self.user_repository = AuthRepository()
        self.transaction_repository = TransactionRepository()

    def get_user_subscription(self, user_id):
        user_subscription = (
            self.subscription_repository.get_populated_user_subscription(user_id)
        )
        return self.subscription_repository.to_dict(user_subscription)

    def update_credits(self, subscription_id, credits):
        self.subscription_repository.update_subscription(
            subscription_id, {"remaining_credits": credits}
        )

    def activate_free_plan(self, user_id):
        try:

            user_data = self.user_repository.find_user_by_id(user_id)

            user_subscription = (
                self.subscription_repository.get_subscription_by_user_id(user_id)
            )

            if (
                user_subscription
                and user_subscription.get("subscription_status")
                == SubscriptionStatus.ACTIVE.value
            ):
                raise SubscriptionActiveError("Subscription is already active")

            free_plan = self.subscription_plan_repository.get_free_subscription_plan()

            self.stripe_service.activate_free_plan(
                free_plan["stripe_price_id"], user_data["customer_id"]
            )

            return True

        except Exception as e:
            raise PaymentError(f"Error activate free plan subscription: {str(e)}")

    def purchase_subscription(self, user_id: str, plan_id: str):
        try:
            # Fetch the subscription plan
            plan = self.subscription_plan_repository.get_subscription_plan_by_id(
                plan_id
            )

            if not plan:
                raise SubscriptionError("Invalid subscription plan")

            # Create or retrieve Stripe customer
            user = self.user_repository.find_user_by_id(user_id)

            # Create Stripe Checkout session
            session = self.stripe_service.create_checkout_session(
                customer_id=user["customer_id"],
                price_id=plan["stripe_price_id"],
                success_url=f"{FRONTEND_ENDPOINT}/dashboard",
                cancel_url=f"{FRONTEND_ENDPOINT}/subscription",
            )

            return {"checkout_session_id": session["id"], "checkout_url": session.url}
        except SubscriptionError as e:
            logger.error(f"Subscription error for user {user_id}: {str(e)}")
            raise
        except PaymentError as e:
            logger.error(f"Payment error for user {user_id}: {str(e)}")
            raise
        except Exception as e:
            logger.exception(
                f"Unexpected error creating subscription for user {user_id}: {str(e)}"
            )
            raise PaymentError(f"Error creating subscription: {str(e)}")

    def update_subscription(self, user_id: str, new_plan_id: str):
        try:
            # Fetch the new subscription plan
            new_plan = self.subscription_plan_repository.get_plan_by_id(new_plan_id)
            if not new_plan:
                raise SubscriptionError("Invalid subscription plan")

            # Get the user's current subscription
            current_subscription = self.subscription_repository.get_active_subscription(
                user_id
            )
            if not current_subscription:
                raise SubscriptionError("No active subscription found")

            # Update the subscription in Stripe
            updated_subscription = self.stripe_service.update_subscription(
                subscription_id=current_subscription.stripe_subscription_id,
                new_price_id=new_plan.stripe_price_id,
            )

            # Update the subscription in the database
            self.subscription_repository.update_subscription(
                subscription_id=current_subscription["id"],
                update_data={
                    "plan_id": new_plan_id,
                    "stripe_subscription_id": updated_subscription["id"],
                },
            )

            return {"message": "Subscription updated successfully"}
        except Exception as e:
            raise PaymentError(f"Error updating subscription: {str(e)}")

    def cancel_subscription(self, user_id: str):
        try:
            # Get the user's current subscription
            current_subscription = self.subscription_repository.get_active_subscription(
                user_id
            )
            if not current_subscription:
                raise SubscriptionError("No active subscription found")

            # Cancel the subscription in Stripe
            cancelled_subscription = self.stripe_service.cancel_subscription(
                current_subscription.stripe_subscription_id
            )

            # Update the subscription status in the database
            self.subscription_repository.cancel_subscription(
                subscription_id=current_subscription["id"],
                cancellation_date=cancelled_subscription["cancel_at"],
            )

            return {"message": "Subscription cancelled successfully"}
        except Exception as e:
            raise PaymentError(f"Error cancelling subscription: {str(e)}")

    def process_web_hook(self, raw_body, signature):
        try:
            event = self.stripe_service.instance().Webhook.construct_event(
                raw_body, signature, STRIPE_WEBHOOK_SECRET
            )
        except ValueError as e:
            print("Error constructing event", str(e))
            raise BadRequest("Invalid payload")
        except self.stripe_service.instance().error.SignatureVerificationError as e:
            print("Error signature constructing event", str(e))
            raise BadRequest("Invalid signature")

        object = event["data"]["object"]

        # Handle the event
        if event["type"] == "invoice.payment_succeeded":
            self.handle_payment_succeeded(object)
        elif event["type"] == "invoice.payment_failed":
            self.handle_payment_failed(object)
        elif event["type"] == "customer.subscription.deleted":
            self.handle_subscription_deleted(object)
        else:
            print(f'Unhandled event type {event["type"]}')

    def handle_payment_succeeded(self, invoice):
        if invoice.status == "paid":
            # Process successful payment
            print(f"Payment succeeded for invoice {invoice["id"]}")
            # Add your business logic here

            try:

                user = self.user_repository.find_user_by_stripe_customer_id(
                    invoice["customer"]
                )

                if user is None:
                    logger.log("Stripe customer not found")
                    return

                invoice_id = invoice["id"]

                stripe_subscription_data = (
                    self.stripe_service.get_customer_subscription_by_id(
                        invoice["subscription"]
                    )
                )

                stripe_product_data = self.stripe_service.get_subscription_item_by_id(
                    invoice["lines"]["data"][0]["subscription_item"]
                )

                plan_data = self.subscription_plan_repository.get_subscription_plan_by_stripe_product_id(
                    stripe_product_data["price"]["product"]
                )

                user_subscription_data = (
                    self.subscription_repository.get_subscription_by_user_id(
                        str(user["_id"])
                    )
                )

                if not user_subscription_data:

                    data_to_create = {
                        "user_id": ObjectId(str(user["_id"])),
                        "plan_id": ObjectId(str(plan_data["_id"])),
                        "amount_paid": invoice["total"] / 100,
                        "subscription_start_date": stripe_subscription_data[
                            "current_period_start"
                        ],
                        "subscription_end_date": stripe_subscription_data[
                            "current_period_end"
                        ],
                        "currency": invoice["lines"]["data"][0]["price"]["currency"],
                        "next_cycle": stripe_subscription_data["current_period_end"]
                        + 500,
                        "subscription_status": SubscriptionStatus.ACTIVE.value,
                        "interval": stripe_product_data["price"]["recurring"][
                            "interval"
                        ],
                        "intervalCount": stripe_product_data["price"]["recurring"][
                            "interval_count"
                        ],
                        "stripe_subscription_id": invoice["subscription"],
                        "stripe_subscription_item_id": invoice["lines"]["data"][0][
                            "subscription_item"
                        ],
                        "status": "active",
                        "cancel_at": None,
                        "used_video_time": 0,
                        "total_credits": plan_data.get("credits"),
                        "remaining_credits": plan_data.get("credits"),
                    }

                    self.subscription_repository.create_subscription(data_to_create)

                else:

                    if (
                        user_subscription_data["stripe_subscription_id"]
                        != invoice["subscription"]
                    ):
                        self.stripe_service.cancel_subscription(
                            user_subscription_data["stripe_subscription_id"]
                        )

                    data_to_update = {
                        "plan_id": ObjectId(str(plan_data["_id"])),
                        "amount_paid": invoice["total"] / 100,
                        "subscription_start_date": stripe_subscription_data[
                            "current_period_start"
                        ],
                        "subscription_end_date": stripe_subscription_data[
                            "current_period_end"
                        ],
                        "next_cycle": stripe_subscription_data["current_period_end"]
                        + 500,
                        "subscription_status": SubscriptionStatus.ACTIVE.value,
                        "currency": invoice["lines"]["data"][0]["price"]["currency"],
                        "interval": stripe_product_data["price"]["recurring"][
                            "interval"
                        ],
                        "intervalCount": stripe_product_data["price"]["recurring"][
                            "interval_count"
                        ],
                        "stripe_subscription_id": invoice["subscription"],
                        "stripe_subscription_item_id": invoice["lines"]["data"][0][
                            "subscription_item"
                        ],
                        "status": "active",
                        "cancel_at": None,
                        "used_video_time": 0,
                        "total_credits": plan_data.get("credits"),
                        "remaining_credits": plan_data.get("credits"),
                    }

                    self.subscription_repository.update_subscription(
                        str(user_subscription_data["_id"]), data_to_update
                    )

                # 1. Extract Data from Invoice
                subscription_id = invoice["subscription"]
                customer_id = invoice["customer"]
                product_id = invoice["lines"]["data"][0]["price"]["product"]
                currency = invoice["lines"]["data"][0]["price"]["currency"]
                amount_total = invoice["amount_paid"] / 100
                hosted_invoice_url = invoice["hosted_invoice_url"]
                invoice_pdf = invoice["invoice_pdf"]

                # 3. Create Transaction
                transaction_data = {
                    "user_id": ObjectId(str(user["_id"])),
                    "plan_id": ObjectId(str(plan_data["_id"])),
                    "subscription_id": subscription_id,  # Use the correct subscription ID
                    "invoice_id": invoice_id,
                    "customer_id": customer_id,
                    "amount": amount_total,
                    "currency": currency,
                    "status": "success",
                    "hosted_invoice_url": hosted_invoice_url,
                    "invoice_pdf": invoice_pdf,
                    "product_id": product_id,
                }
                self.transaction_repository.create_transaction(transaction_data)

            except Exception as e:
                logging.error(
                    f"Error processing invoice.payment_succeeded event: {str(e)}"
                )

    def handle_payment_failed(self, invoice):
        # Process failed payment
        print(f"Payment failed for invoice {invoice["id"]}")
        # Add your business logic here
        try:

            user = self.user_repository.find_user_by_stripe_customer_id(
                invoice["customer"]
            )

            if user is None:
                logger.log("Stripe customer not found")
                return

            user_subscription = (
                self.subscription_repository.get_subscription_by_stripe_id(
                    invoice["subscription"]
                )
            )

            if user_subscription:

                cancel_subscription = self.stripe_service.cancel_subscription_now()

                if cancel_subscription["status"] == "canceled":

                    self.subscription_repository.update_subscription(
                        str(user_subscription["_id"]),
                        {"subscription_status": SubscriptionStatus.CANCELED.value},
                    )

                    free_plan = (
                        self.subscription_plan_repository.get_free_subscription_plan()
                    )

                    if free_plan:
                        self.stripe_service.activate_free_plan(
                            free_plan["stripe_price_id"],
                            user["customer_id"],
                            user_subscription["stripe_subscription_id"],
                        )

                    invoice_id = invoice["id"]
                    subscription_id = invoice["subscription"]
                    customer_id = invoice["customer"]
                    amount_total = invoice["amount_due"] / 100
                    currency = invoice["currency"]
                    hosted_invoice_url = invoice["hosted_invoice_url"]
                    invoice_pdf = invoice["invoice_pdf"]
                    product_id = invoice["lines"]["data"][0]["price"]["product"]

                    transaction_data = {
                        "user_id": user["_id"],
                        "plan_id": user_subscription["plan_id"],
                        "subscription_id": subscription_id,
                        "invoice_id": invoice_id,
                        "customer_id": customer_id,
                        "amount": amount_total,
                        "currency": currency,
                        "status": "failed",
                        "hosted_invoice_url": hosted_invoice_url,
                        "invoice_pdf": invoice_pdf,
                        "product_id": product_id,
                    }
                    self.transaction_repository.create_transaction(transaction_data)

        except Exception as e:
            logging.error(f"Error processing invoice.payment_failed event: {e}")

    def handle_subscription_deleted(self, subscription):
        if subscription.status == "canceled":
            # Process subscription cancellation
            print(f"Subscription {subscription["id"]} was cancelled")
            # Add your business logic here
            try:

                user = self.user_repository.find_user_by_stripe_customer_id(
                    subscription["customer"]
                )

                if user is None:
                    logger.log("Stripe customer not found")
                    return

                user_subscription = (
                    self.subscription_repository.get_subscription_by_stripe_id(
                        subscription["id"]
                    )
                )

                if user_subscription is None:
                    logger.log("Stripe user subscription not found")
                    return

                stripe_subscription_data = (
                    self.stripe_service.get_customer_subscription_by_id(
                        subscription["id"]
                    )
                )

                if (
                    stripe_subscription_data["status"]
                    == SubscriptionStatus.CANCELED.value
                ):

                    self.subscription_repository.update_subscription(
                        str(user_subscription["_id"]),
                        {"subscription_status": SubscriptionStatus.CANCELED.value},
                    )

                    free_plan = (
                        self.subscription_plan_repository.get_free_subscription_plan()
                    )

                    if free_plan:
                        self.stripe_service.activate_free_plan(
                            free_plan["stripe_price_id"],
                            user["customer_id"],
                            user_subscription["stripe_subscription_id"],
                        )

                    transaction_data = {
                        "user_id": user["_id"],
                        "subscription_id": subscription["customer"],
                        "plan_id": user_subscription["plan_id"],
                        "amount": 0,
                        "status": subscription["status"],
                    }
                    self.transaction_repository.create_transaction(transaction_data)

            except Exception as e:
                logging.error(
                    f"Error processing customer.subscription.deleted event: {e}"
                )
