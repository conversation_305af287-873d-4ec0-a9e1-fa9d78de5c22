from app.repositories.subscription_plan_repository import SubscriptionPlanRepository
from typing import Dict, Any, List
from .stripe import StripeService

from flask import abort

from app.modules.subscription_plan import SubscriptionPlan


class PlanService:
    def __init__(self):
        self.subscription_plan_repository = SubscriptionPlanRepository()
        self.stripe_service = StripeService()

        free_plan = self.subscription_plan_repository.get_free_subscription_plan()

        if not free_plan:
            self.create_subscription_plan(
                {
                    "name": "Free Plan",
                    "description": "Zero dollar subscription",
                    "price": 0,
                    "currency": "usd",
                    "interval": "month",
                    "interval_count": 1,
                    "video_time": 20 * 60 * 60,
                    "max_video_duration": 5 * 60 * 60,
                    "credit": 5,
                    "features": [
                        "20 minutes of video per month",
                        "2 minutes max video length",
                        "5 credits per month",
                        "Export up to 720p",
                    ],
                    "upto_video_quality": "720p",
                    "image": "https://www.shutterstock.com/image-vector/tariff-plans-subscription-color-icon-260nw-1913057161.jpg",
                }
            )

    def create_subscription_plan(self, plan_data: Dict[str, Any]):
        try:
            # Validate required fields
            required_fields = [
                "name",
                "description",
                "price",
                "currency",
                "interval",
                "interval_count",
                "video_time",
                "max_video_duration",
                "features",
                "upto_video_quality",
            ]
            for field in required_fields:
                if field not in plan_data:
                    raise ValueError(f"Missing required field: {field}")

            # Create plan in Stripe
            stripe_plan_data = {
                "name": plan_data["name"],
                "description": plan_data["description"],
                "active": plan_data.get("active", True),
                "price": plan_data["price"],  # Convert to cents
                "currency": plan_data["currency"],
                "renewal_period": plan_data["interval"],
                "renewal_number": plan_data["interval_count"],
                "image": plan_data.get("image", None),
            }

            stripe_ids = self.stripe_service.create_plan(stripe_plan_data)

            # Create plan in database
            new_plan = {
                "name": plan_data["name"],
                "description": plan_data["description"],
                "price": plan_data["price"],
                "currency": plan_data["currency"],
                "interval": plan_data["interval"],
                "interval_count": plan_data["interval_count"],
                "stripe_product_id": stripe_ids["stripe_product_id"],
                "stripe_price_id": stripe_ids["stripe_price_id"],
                "active": plan_data.get("active", True),
                "image": plan_data.get("image", None),
                "video_time": plan_data.get("video_time"),
                "max_video_duration": plan_data.get("max_video_duration", None),
                "features": plan_data.get("features", None),
                "upto_video_quality": plan_data.get("upto_video_quality", None),
            }

            created_plan = self.subscription_plan_repository.create_subscription_plan(
                new_plan
            )
            return self.subscription_plan_repository.to_dict(created_plan)

        except ValueError as ve:
            # Handle validation errors
            abort(400, description=str(ve))
        except Exception as e:
            # Handle other errors (e.g., database errors, Stripe API errors)
            abort(
                500,
                description=f"An error occurred while creating the subscription plan: {str(e)}",
            )

    def get_subscription_plan(self, plan_id: str) -> SubscriptionPlan:
        try:
            plan = self.subscription_plan_repository.get_subscription_plan_by_id(
                plan_id
            )
            if not plan:
                abort(404, description=f"Subscription plan with id {plan_id} not found")
            return self.subscription_plan_repository.to_dict(plan)
        except Exception as e:
            abort(
                500,
                description=f"An error occurred while fetching the subscription plan: {str(e)}",
            )

    def get_subscription_plans(
        self,
        role,
        interval,
        active,
    ) -> List[SubscriptionPlan]:
        try:
            if role == "user":
                active = True

            plans = self.subscription_plan_repository.get_subscription_plans(
                interval, active
            )

            return self.subscription_plan_repository.to_dict(plans)
        except Exception as e:
            abort(
                500,
                description=f"An error occurred while fetching subscription plans: {str(e)}",
            )

    def update_subscription_plan(
        self, plan_id: str, plan_data: Dict[str, Any]
    ) -> SubscriptionPlan:
        try:
            existing_plan = (
                self.subscription_plan_repository.get_subscription_plan_by_id(plan_id)
            )
            if not existing_plan:
                abort(404, description=f"Subscription plan with id {plan_id} not found")

            # Update plan in Stripe
            stripe_plan_data = {
                "name": plan_data.get("name", existing_plan["name"]),
                "description": plan_data.get(
                    "description", existing_plan["description"]
                ),
                "active": plan_data.get("active", existing_plan["active"]),
                "images": [plan_data.get("image", existing_plan["image"])],
            }

            self.stripe_service.update_product(
                existing_plan["stripe_product_id"], stripe_plan_data
            )

            # If price has changed, create a new price in Stripe
            if "price" in plan_data and plan_data["price"] != existing_plan["price"]:
                new_price_id = self.stripe_service.create_new_price(
                    amount=int(plan_data["price"] * 100),
                    interval=existing_plan["interval"],
                    prod_id=existing_plan["stripe_product_id"],
                )
                plan_data["stripe_price_id"] = new_price_id

                # TODO: update and notify the plan price to subscriber

            # Update plan in database
            updated_plan = self.subscription_plan_repository.update_subscription_plan(
                plan_id, plan_data
            )
            return self.subscription_plan_repository.to_dict(updated_plan)

        except Exception as e:
            abort(
                500,
                description=f"An error occurred while updating the subscription plan: {str(e)}",
            )

    def delete_subscription_plan(self, plan_id: str) -> None:
        try:
            existing_plan = (
                self.subscription_plan_repository.get_subscription_plan_by_id(plan_id)
            )
            if not existing_plan:
                abort(404, description=f"Subscription plan with id {plan_id} not found")

            self.stripe_service.update_product(
                existing_plan["stripe_product_id"], {"active": False}
            )

            #  TODO: Deactivate this plan of all subscriber

            # Delete plan in database
            self.subscription_plan_repository.delete_subscription_plan(plan_id)

        except Exception as e:
            abort(
                500,
                description=f"An error occurred while deleting the subscription plan: {str(e)}",
            )
