import logging
import os
import requests


from app.repositories.media_repository import MediaRepository
from moviepy.editor import AudioFileClip, CompositeAudioClip, concatenate_videoclips

# Configure logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MusicService:

    def __init__(self):
        self.media_repository = MediaRepository()

    def download_music_file(self, url, save_path, filename=None):
        """
        Download a music file from a given URL and save it to a specific path.

        Args:
            url (str): The direct URL of the music file to download
            save_path (str): The directory path where the file will be saved
            filename (str, optional): Custom filename for the downloaded file.
                                    If None, uses the original filename from the URL.

        Returns:
            str: Full path of the downloaded file

        Raises:
            ValueError: If the URL is invalid or download fails
            IOError: If there are issues creating directories or saving the file
        """
        try:
            # Ensure the save directory exists
            os.makedirs(save_path, exist_ok=True)

            # Send a GET request to download the file
            response = requests.get(url, stream=True)

            # Raise an exception for bad HTTP responses
            response.raise_for_status()

            # Determine filename
            if filename is None:
                # Extract filename from URL if not provided
                filename = url.split("/")[-1]

            # Full path for saving the file
            full_save_path = os.path.join(save_path, filename)

            # Save the file
            with open(full_save_path, "wb") as file:
                for chunk in response.iter_content(chunk_size=8192):
                    file.write(chunk)

            print(f"Successfully downloaded: {full_save_path}")
            return full_save_path

        except requests.RequestException as e:
            print(f"Download failed: {e}")
            raise ValueError(f"Could not download file from {url}")
        except IOError as e:
            print(f"File save error: {e}")
            raise IOError(f"Could not save file to {save_path}")

    def add_music(self, video_id, video_clip, music_media_id):

        try:

            media_data = self.media_repository.get_media_by_id(music_media_id)

            # Select a random song
            song_path = self.download_music_file(
                media_data["link"], f"./temp/{video_id}"
            )

            # Add song to video at 30% volume using moviepy
            original_duration = video_clip.duration
            original_audio = video_clip.audio
            song_clip = AudioFileClip(song_path).set_fps(44100)

            # Set the volume of the song to 10% of the original volume
            song_clip = song_clip.volumex(0.05).set_fps(44100)

            # Get the original video duration
            original_duration = song_clip.duration

            # Calculate how many times we need to loop the video
            loops_needed = int(video_clip.duration / original_duration) + 1

            # Create a list of the video repeated the necessary number of times
            looped_clips = [song_clip] * loops_needed

            # Concatenate the clips
            song_clip = concatenate_videoclips(looped_clips)

            # Add the song to the video
            comp_audio = CompositeAudioClip([original_audio, song_clip])
            video_clip = video_clip.set_audio(comp_audio)
            video_clip = video_clip.set_duration(original_duration)

            return video_clip

        except Exception as e:
            print("error music addition", str(e))
            return video_clip


music_service = MusicService()
