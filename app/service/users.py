from app.helper.jwt import JwtService
from app.helper.redis import RedisService

from app.constant.user_enums import AccountStatus
from app.exceptions.auth_exceptions import SamePassword

from app.repositories.user_repository import AuthRepository


class UserService:

    def __init__(self):
        self.jwt_service = JwtService()
        self.redis_service = RedisService()
        self.user_repository = AuthRepository()
        pass

    def check_otp_token_validity(self, token, token_type):
        # Decrypt the provided token and obtain the tokenObject.
        token_object = self.jwt_service.decrypt_otp_token(token)

        # If the token cannot be decrypted, raise an exception.
        if not token_object:
            raise Exception("Invalid OTP")

        # Build the Redis key for the specified verification type and user's email.
        key = f"{token_type}:{token_object['email']}"

        # Retrieve the OTP from Redis using the key.
        redis_otp_token = self.redis_service.get_data_from_redis_using_key(key)
        # If the OTP token does not exist in Redis, raise an exception.
        if not redis_otp_token:
            raise Exception("OTP expired")

        # Decrypt the OTP token from Redis and obtain the redisOTPObject.
        redis_otp_object = self.jwt_service.decrypt_otp_token(redis_otp_token)

        # Compare the OTP from the provided token with the OTP from Redis. If they don't match, raise an exception.
        if token_object["otp"] != redis_otp_object["otp"]:
            raise Exception("Incorrect OTP")

        # Return both the tokenObject and the redisOTPObject.
        return token_object, redis_otp_object

    def update_email_verified_details(self, update_email_verified_details_dto):
        try:
            # Check the validity of the OTP token and retrieve the tokenObject.
            token_object, redis_otp_object = self.check_otp_token_validity(
                update_email_verified_details_dto["token"], "signup_verification"
            )

            # Check if the user exists in the database.
            user = self.user_repository.find_user_by_email(token_object["email"])

            # If the user is already active, raise an exception.
            if user["status"] == AccountStatus.ACTIVE.value:
                raise Exception("User is already verified")

            # Update the user's status to ACTIVE in the database.
            self.user_repository.update_one(
                {"email": token_object["email"]},
                {
                    "$set": {
                        "status": AccountStatus.ACTIVE.value,
                        "verified_email": True,
                    }
                },
            )

            # Remove the encrypted OTP from Redis.
            key = f"signup_verification:{token_object['email']}"

            self.redis_service.delete_data_with_key(key)

            # Return a successful response with a message and user details.
            return {
                "message": "Email verified successfully",
                "email": user["email"],
                "name": user["name"],
            }
        except Exception as error:
            # If an error occurs, raise the exception to be handled by the route.
            raise error

    def verify_reset_token_and_update_password(self, token, password_hash):

        try:
            # Check the validity of the OTP token and retrieve the tokenObject.
            token_object, redis_otp_object = self.check_otp_token_validity(
                token, "reset_password_verification"
            )

            # Check if the user exists in the database.
            user = self.user_repository.find_user_by_email(token_object["email"])

            user_id = str(user["_id"])

            if user.get("password") == password_hash:
                raise SamePassword("The provided password same as previous.")

            self.user_repository.update_user_password(user_id, password_hash)

            # Remove the encrypted OTP from Redis.
            key = f"reset_password_verification:{token_object['email']}"

            self.redis_service.delete_data_with_key(key)

            return {"message": "Password has been reset successfully", "code": 200}, 200

        except Exception as error:
            # If an error occurs, raise the exception to be handled by the route.
            raise error

    def account_activate_verified_details(self, update_email_verified_details_dto):
        try:
            # Check the validity of the OTP token and retrieve the tokenObject.
            token_object, redis_otp_object = self.check_otp_token_validity(
                update_email_verified_details_dto["token"], "account_activation"
            )

            # Check if the user exists in the database.
            user = self.user_repository.find_user_by_email(token_object["email"])

            # If the user is already active, raise an exception.
            if user["account_status"] == AccountStatus.ACTIVE.value:
                raise Exception("User is already Activated.")

            # Update the user's status to ACTIVE in the database.
            self.user_repository.update_one(
                {"email": token_object["email"]},
                {
                    "$set": {
                        "account_status": AccountStatus.ACTIVE.value,
                    }
                },
            )

            # Remove the encrypted OTP from Redis.
            key = f"account_activation:{token_object['email']}"

            self.redis_service.delete_data_with_key(key)

            # Return a successful response with a message and user details.
            return {
                "message": "Account activate successfully.",
                "email": user["email"],
                "name": user["name"],
            }
        except Exception as error:
            # If an error occurs, raise the exception to be handled by the route.
            raise error
