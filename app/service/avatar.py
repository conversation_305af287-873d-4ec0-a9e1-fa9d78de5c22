from ..repositories.avatar_repository import AvatarRepository
from .tavus import TavusService


from datetime import datetime, timedelta


class AvatarService:

    def __init__(self):
        self.avatar_repository = AvatarRepository()
        self.tavus_service = TavusService()

    def get_default_avatars(
        self, user_id, page: int, per_page: int, is_cloned: bool = None
    ):
        avatar_count = self.avatar_repository.get_avatar_count({"status": "completed"})

        if avatar_count < 5:
            avatar_list = self.tavus_service.get_replicas()
            for avatar in avatar_list:
                avatar_data = self.avatar_repository.get_avatar_by_avatar_id(
                    avatar["replica_id"]
                )
                if not avatar_data:
                    self.avatar_repository.create_avatar(
                        {
                            "avatar_id": avatar["replica_id"],
                            "name": avatar["replica_name"],
                            "link": avatar["thumbnail_video_url"],
                            "is_cloned": (
                                True if avatar.get("replica_type") == "user" else False
                            ),
                            "status": avatar["status"],
                            "model_name": avatar["model_name"],
                            "replica_type": avatar.get("replica_type"),
                        }
                    )

        result = self.avatar_repository.get_avatars_paginated(
            user_id, page, per_page, is_cloned
        )

        if len(result["avatars"]) != 0:

            current_time = datetime.utcnow()

            if (
                datetime.fromisoformat(result["avatars"][0]["updated_at"])
                + timedelta(days=10)
                < current_time
            ):

                avatar_list = self.tavus_service.get_replicas()

                for avatar in avatar_list:
                    self.avatar_repository.update_avatar_by_avatar_id(
                        avatar["replica_id"],
                        {
                            "name": avatar["replica_name"],
                            "link": avatar["thumbnail_video_url"],
                            "status": avatar["status"],
                            "model_name": avatar["model_name"],
                        },
                    )

                result = self.avatar_repository.get_avatars_paginated(
                    user_id, page, per_page, is_cloned
                )

        return {
            "avatars": result["avatars"],
            "page": result["page"],
            "per_page": result["per_page"],
            "total_count": result["total_count"],
            "total_pages": result["total_pages"],
        }
