from app.repositories.media_repository import MediaRepository
from app.service.video.video_edit import create_video_metadata
from app.helper.s3_manager import S3Uploader
import uuid
from werkzeug.exceptions import NotFound


class MediaService:
    def __init__(self):
        self.media_repository = MediaRepository()
        self.s3_service = S3Uploader()

    def create_media(self, data):
        try:
            if data.get("type") == "video":
                thumbnail_path = "./temp/thumbnail.jpg"
                size, duration = create_video_metadata(data.get("link"), thumbnail_path)
                if size:
                    data["resolution"] = {"width": size[0], "height": size[1]}
                    data["duration"] = duration
                    thumbnail_link = self.s3_service.upload_file(
                        "videos", thumbnail_path, f"{uuid.uuid4()}.jpg"
                    )
                    data["thumbnail"] = thumbnail_link

            data = self.media_repository.create_media(data)
            return self.media_repository.to_dict(data)
        except Exception as e:
            print(f"Error occurred while choosing random song: {str(e)}")
            raise e

    def get_medias(self, user_id, page, per_page, media_type, category, folder):
        try:
            return self.media_repository.get_medias_paginated(
                user_id, page, per_page, media_type, category, folder
            )
        except Exception as e:
            print(f"Error occurred while choosing random song: {str(e)}")
            raise e

    def update_media(self, user_id, media_id, data):
        try:
            data = self.media_repository.update_media(user_id, media_id, data)
            if not data:
                raise NotFound("media not found")

            return self.media_repository.to_dict(data)
        except Exception as e:
            print(f"Error occurred while choosing random song: {str(e)}")
            raise e

    def get_media(self, user_id, media_id):
        try:
            data = self.media_repository.get_medias_by_user(user_id, media_id)
            if not data:
                raise NotFound("media not found")

            return self.media_repository.to_dict(data)
        except Exception as e:
            print(f"Error occurred while choosing random song: {str(e)}")
            raise e

    def delete_media(self, user_id, media_id):
        try:
            data = self.media_repository.delete_media(user_id, media_id)
            if not data:
                raise NotFound("media not found")
            return data
        except Exception as e:
            print(f"Error occurred while choosing random song: {str(e)}")
            raise e
