import logging
import os

from typing import Dict, Any
import requests
from bson import ObjectId
from app.service.utils import save_video
from app.helper.mailer import EmailService
from app.constant.video_enums import UploadDomain
from app.constant.user_enums import EmailType

from app.repositories.uploaded_video_repository import UploadedVideoRepository


class UploadError(Exception):
    """Custom exception for upload errors"""

    pass


class LinkedInUploader:
    """Handles LinkedIn specific upload logic"""

    def __init__(self, access_token: str, user_id: str):
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)

        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        self.access_token = access_token
        self.user_id = user_id
        self.base_url = "https://api.linkedin.com"
        self.headers = {
            "Authorization": f"Bearer {access_token}",
            "LinkedIn-Version": "202410",
            "X-RestLi-Protocol-Version": "2.0.0",
            "Content-Type": "application/json",
        }

        self.email_service = EmailService()
        self.upload_video_repo = UploadedVideoRepository()

    def send_email(self, user_data, video_id, email_type, metadata=None):

        link = f"https://www.linkedin.com/feed/update/urn:li:ugcPost:{video_id}"

        self.email_service.send_email_with_template(
            user_data["name"],
            user_data["email"],
            link,
            email_type,
            metadata,
            platform="Linkedin",
        )

    def _validate_video_file(self, video_path: str) -> None:
        """Validate that the video file exists and is accessible."""
        if not os.path.exists(video_path):
            raise UploadError(f"Video file not found: {video_path}")

        if not os.path.isfile(video_path):
            raise UploadError(f"Path is not a file: {video_path}")

        if not os.access(video_path, os.R_OK):
            raise UploadError(f"Video file is not readable: {video_path}")

    def read_chunk(self, video_path: str, start_byte: int, last_byte: int) -> bytes:
        """Reads a specific chunk of a video file."""
        with open(video_path, "rb") as file:
            file.seek(start_byte)
            return file.read(last_byte - start_byte + 1)

    def upload_video_content_chunked(
        self,
        video_id,
        video_path: str,
        chunk_data,
    ) -> None:
        """Upload video content in chunks"""
        etags = []

        try:
            for index, chunk in enumerate(chunk_data):

                # Calculate chunk boundaries
                start_byte = chunk["byteRange"]["firstByte"]
                last_byte = chunk["byteRange"]["lastByte"]

                # Read the chunk data
                upload_data = self.read_chunk(video_path, start_byte, last_byte)

                # Ensure upload URL is available
                upload_url = chunk.get("url")
                if not upload_url:
                    raise UploadError("Missing upload URL for chunk")

                # Define headers, possibly including Content-Range and Content-Length
                headers = {
                    "Content-Type": "application/octet-stream",
                    "Content-Length": str(len(upload_data)),
                    "Content-Range": f"bytes {start_byte}-{last_byte}/*",
                }

                # Upload with retry in case of temporary issues
                for attempt in range(3):
                    try:
                        response = requests.put(
                            upload_url, data=upload_data, headers=headers
                        )
                        response.raise_for_status()

                        if response.status_code in (200, 201):
                            etag = response.headers.get("ETag")
                            if not etag:
                                raise Exception("No ETag received")
                            etags.append(
                                {"headers": {"ETag": etag}, "httpStatusCode": 200}
                            )
                            break  # Break loop on success

                    except requests.exceptions.RequestException as e:
                        if attempt == 2:  # Final attempt failed
                            raise UploadError(
                                f"Chunk upload failed after 3 attempts: {str(e)}"
                            )
            return etags

        except Exception as e:
            self.logger.error(f"Error during video upload: {str(e)}")
            raise UploadError(f"Video upload failed: {str(e)}")

    def finalize_upload(
        self, video_id: str, etags, linkedin_mediaartifact, linkedin_metadata
    ) -> None:
        """Finalize the video upload"""
        finalize_url = (
            "https://api.linkedin.com/v2/assets?action=completeMultiPartUpload"
        )
        body = {
            "completeMultipartUploadRequest": {
                "mediaArtifact": linkedin_mediaartifact,
                "metadata": linkedin_metadata,
                "partUploadResponses": etags,
            }
        }
        headers = self.headers
        headers["LinkedIn-Version"] = "202410"
        response = requests.post(finalize_url, headers=headers, json=body)
        if response.status_code == 200:
            print("Finalization successful")
        else:
            print("Finalization failed:", response.content)

    def upload_video(
        self,
        user_data,
        video_data,
        in_chunks=False,
    ) -> bool:
        """
        Upload a video to LinkedIn using the official API.

        Args:
            video_path (str): Path to the video file
            description (str): Video description

        Returns:
            bool: True if upload successful, False otherwise
        """

        metadata = video_data.get("metadata", {})

        video_id = str(video_data.get("_id"))

        title = metadata.get("title")
        caption = metadata.get("caption")

        video_link = video_data.get("link")

        video_path = save_video(video_link)

        try:
            self.logger.info(f"Starting LinkedIn upload: {video_path}")

            self._validate_video_file(video_path)

            # Get file size for chunked upload
            file_size = os.path.getsize(video_path)

            # Step 1: Initialize upload
            init_response = self.initialize_upload(file_size, in_chunks)

            # Metadata and media artifacts to complete the linked the multipart upload
            linkedin_mediaartifact = init_response.get("value", {}).get(
                "mediaArtifact", ""
            )
            linkedin_metadata = (
                init_response.get("value", {})
                .get("uploadMechanism", {})
                .get("com.linkedin.digitalmedia.uploading.MultipartUpload", {})
                .get("metadata", "")
            )

            self.logger.debug(f"Initialize response: {init_response}")

            video_urn = init_response.get("value", {}).get("asset")

            print("====video_urn===", video_urn)

            if in_chunks:
                upload_data = (
                    init_response.get("value", {})
                    .get("uploadMechanism", {})
                    .get("com.linkedin.digitalmedia.uploading.MultipartUpload", {})
                    .get("partUploadRequests")
                )

            else:

                upload_data = (
                    init_response.get("value", {})
                    .get("uploadMechanism", {})
                    .get(
                        "com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest", {}
                    )
                    .get("uploadUrl")
                )

            if not video_urn or not upload_data:
                raise UploadError("Failed to get upload instructions from LinkedIn")

            # Step 2: Upload video content
            if in_chunks:
                etags = self.upload_video_content_chunked(
                    video_id,
                    video_path,
                    upload_data,
                )
                self.finalize_upload(
                    video_id, etags, linkedin_mediaartifact, linkedin_metadata
                )
            else:
                self.upload_video_content(
                    video_id,
                    video_path,
                    upload_data,
                )

            # Step 3: Create post with video
            success, linkedin_video_id = self.create_video_post(
                video_urn, title, caption
            )

            if success:
                self.logger.info("LinkedIn upload completed successfully")

                self.send_email(
                    user_data,
                    linkedin_video_id,
                    EmailType.VIDEO_UPLOADED.value,
                )

                self.upload_video_repo.create_uploaded_video(
                    {
                        "video_id": ObjectId(video_id),
                        "user_id": ObjectId(str(user_data["_id"])),
                        "upload_video_id": linkedin_video_id,
                        "upload_link": f"https://www.linkedin.com/feed/update/urn:li:ugcPost:{linkedin_video_id}",
                        "upload_domain": UploadDomain.LINKEDIN.value,
                    }
                )

                return {"id": linkedin_video_id}

            self.logger.error("Failed to create LinkedIn post")
            return {"id": None}

        except Exception as e:
            self.logger.error(f"LinkedIn upload failed: {str(e)}", exc_info=True)
            if hasattr(e, "message"):
                error_message = str(e.message)
            else:
                error_message = str(e)
            self.logger.error(f"Upload error: {error_message}")
            return {"id": None}

    def initialize_upload(
        self,
        file_size: int,
        in_chunks: bool = True,
    ) -> Dict[str, Any]:
        """Initialize video upload and get upload URLs"""
        url = f"{self.base_url}/v2/assets?action=registerUpload"

        if in_chunks:

            payload = {
                "registerUploadRequest": {
                    "recipes": ["urn:li:digitalmediaRecipe:feedshare-video"],
                    "owner": f"urn:li:person:{self.user_id}",
                    "serviceRelationships": [
                        {
                            "identifier": "urn:li:userGeneratedContent",
                            "relationshipType": "OWNER",
                        }
                    ],
                    "supportedUploadMechanism": ["MULTIPART_UPLOAD"],
                    "fileSize": file_size,
                }
            }
        else:

            payload = {
                "registerUploadRequest": {
                    "recipes": ["urn:li:digitalmediaRecipe:feedshare-video"],
                    "owner": f"urn:li:person:{self.user_id}",
                    "serviceRelationships": [
                        {
                            "identifier": "urn:li:userGeneratedContent",
                            "relationshipType": "OWNER",
                        }
                    ],
                }
            }

        self.logger.debug(f"Initializing upload with data: {payload}")

        # Send the payload directly without additional json.dumps()
        response = requests.post(url, headers=self.headers, json=payload)

        if response.status_code != 200:
            self.logger.error(f"Failed to initialize upload: {response.text}")
            raise UploadError(
                f"Upload initialization failed with status {response.status_code}"
            )

        return response.json()

    def upload_video_content(self, video_id, video_path: str, upload_url: str) -> None:
        """Upload video content using the provided upload URL"""
        with open(video_path, "rb") as video_file:
            video_data = video_file.read()

        self.logger.debug(f"Uploading video to {upload_url}")

        headers = {"Content-Type": "application/octet-stream"}

        response = requests.put(upload_url, headers=headers, data=video_data)

        if response.status_code not in (200, 201):
            raise UploadError(f"Video upload failed with status {response.status_code}")

    def create_video_post(self, video_urn: str, title: str, description: str) -> bool:
        """Create a post with the uploaded video"""

        url = f"{self.base_url}/v2/ugcPosts"

        payload = {
            "author": f"urn:li:person:{self.user_id}",
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {"text": description},
                    "shareMediaCategory": "VIDEO",
                    "media": [
                        {
                            "status": "READY",
                            "media": video_urn,
                            "title": {"text": title},
                            "description": {"text": "Refer : https://blockstar.com"},
                        }
                    ],
                }
            },
            "visibility": {"com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"},
        }

        self.logger.debug(f"Creating post with data: {payload}")
        response = requests.post(url, headers=self.headers, json=payload)

        if response.status_code not in (200, 201):
            self.logger.error(f"Failed to create post: {response.text}")
            return False, None

        response = response.json()

        start = response["id"].rfind(":") + 1

        return True, response["id"][start:]

    def test_linkedin_token(self):

        response = requests.get("https://api.linkedin.com/v2/me", headers=self.headers)

        if response.status_code == 200:
            data = response.json()
            print("User ID:", data.get("id"))
            return data.get("id")
        else:
            print("Error:", response.status_code, response.text)
            return None

    def get_user_id(access_token):

        url = "https://api.linkedin.com/v2/people?q=vanityName&vanityName=pratik-kumar-36774b274"

        payload = {}
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "Cookie": 'lidc="b=OB25:s=O:r=O:a=O:p=O:g=4129:u=1669:x=1:i=1729577947:t=1729591819:v=2:sig=AQGU7cTbiF9I2Pr2cKn21X6LUy7pvGBB"; bcookie="v=2&924407c6-2a42-411f-8cf5-937531464753"',
        }

        response = requests.request("GET", url, headers=headers, data=payload)

        print(response.text)
