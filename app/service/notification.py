from bson import ObjectId

from app.repositories.notification_repository import NotificationRepository
from app.helper.firebase import FirebaseAdmin
from ..repositories.user_repository import AuthRepository


class NotificationService:

    def __init__(self):
        self.notifications_repository = NotificationRepository()
        self.firebase_service = FirebaseAdmin()
        self.user_repository = AuthRepository()

    def get_user_notifications(self, user_id, page, per_page, type, is_read):

        return self.notifications_repository.get_notifications_paginated(
            user_id, page, per_page, type, is_read
        )

    def create_notification(self, user_id, title, description, type, data):

        user_data = self.user_repository.find_user_by_id(user_id)

        self.notifications_repository.create_notification(
            {
                "user_id": ObjectId(user_id),
                "title": title,
                "description": description,
                "type": type,
                "is_read": False,
                "data": data.get("data"),
            }
        )

        self.firebase_service.send_fcm_update(
            user_data.get("firebase_token"),
            data,
        )
