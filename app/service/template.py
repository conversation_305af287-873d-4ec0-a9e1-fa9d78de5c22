from app.repositories.template_repository import TemplateRepository
from app.exceptions.common import (
    InternalError,
    DatabaseError,
)

from werkzeug.exceptions import NotFound


class TemplateService:
    def __init__(self):
        self.template_repository = TemplateRepository()

    def create_template(self, user_id, template_data):
        try:
            self._validate_template_data(template_data)
            template_data["user_id"] = user_id
            template_data = self.template_repository.create_template(template_data)
            return self.template_repository.to_dict(template_data)
        except InternalError:
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to create template: {str(e)}")

    def get_templates(self, page, per_page, type):
        try:
            return self.template_repository.get_templates_paginated(
                page, per_page, type
            )

        except Exception as e:
            raise DatabaseError(f"Failed to retrieve templates: {str(e)}")

    def get_template(self, template_id):
        try:
            template = self.template_repository.get_template_by_id(template_id)
            if not template:
                raise NotFound(f"Template with id {template_id} not found")
            return self.template_repository.to_dict(template)
        except NotFound:
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to retrieve template: {str(e)}")

    def update_template(self, template_id, update_data):
        try:
            self._validate_template_data(update_data, is_update=True)
            updated_template = self.template_repository.update_template(
                template_id, update_data
            )
            if not updated_template:
                raise NotFound(f"Template with id {template_id} not found")
            return self.template_repository.to_dict(updated_template)
        except (InternalError, NotFound):
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to update template: {str(e)}")

    def delete_template(self, template_id):
        try:
            result = self.template_repository.delete_template(template_id)
            if not result:
                raise NotFound(f"Template with id {template_id} not found")
        except NotFound:
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to delete template: {str(e)}")

    def _validate_template_data(self, data, is_update=False):
        required_fields = (
            ["title", "template_id", "description"] if not is_update else []
        )
        for field in required_fields:
            if field not in data or not data[field]:
                raise InternalError(f"Missing required field: {field}")

        if "title" in data and len(data["title"]) > 100:
            raise InternalError("Template title must be 100 characters or less")
