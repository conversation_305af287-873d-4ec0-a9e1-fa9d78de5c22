import requests
import os

import time
import logging
from pyht import Client, TTSOptions, Format
from werkzeug.exceptions import Forbidden, Unauthorized
from config import PLAY_API_KEY, PLAY_USER_ID
from .utils import split_long_string
from app.exceptions.voice_exceptions import VoiceGenerationFailed

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


class PlayHtService:
    def __init__(self):
        pass

    def validate_secret_key(self, bearer_token):

        if bearer_token:
            token_parts = bearer_token.split(" ")
            if token_parts[0] != "Bearer":
                raise Forbidden("Service secret key is missing")

            token = token_parts[1]
            if PLAY_API_KEY != token:
                raise Unauthorized("Unauthorized")
        else:
            raise Unauthorized("Service secret key is missing")

        return True

    def create_voice(self, text, voice_id):
        try:
            url = "https://api.play.ht/api/v2/tts"
            payload = {
                "text": text,
                "voice": voice_id,
                "output_format": "mp3",
                "voice_engine": "PlayHT2.0",
                "quality": "high",
            }
            headers = {
                "accept": "application/json",
                "content-type": "application/json",
                "AUTHORIZATION": PLAY_API_KEY,
                "X-USER-ID": PLAY_USER_ID,
            }

            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()

            logger.info(f"Voice creation response: {response.text}")

            id = response.json()["id"]
            return id

        except requests.exceptions.RequestException as e:
            logger.error(f"Error in create_voice: {str(e)}")
            raise

    def get_voice(self, audio_id):
        try:
            request_url = f"https://api.play.ht/api/v2/tts/{audio_id}"
            headers = {
                "accept": "application/json",
                "AUTHORIZATION": PLAY_API_KEY,
                "X-USER-ID": PLAY_USER_ID,
            }

            max_retries = 10
            retry_count = 0

            while retry_count < max_retries:
                response = requests.get(url=request_url, headers=headers)
                response.raise_for_status()

                data = response.json()
                status = data["status"]

                if status == "failed":
                    raise VoiceGenerationFailed("Failed to generate voice")

                if status == "complete":
                    return data["output"]["url"]

                logger.info("Waiting for voice generation...")
                time.sleep(30)
                retry_count += 1

            raise TimeoutError("Voice generation timed out")

        except requests.exceptions.RequestException as e:
            logger.error(f"Error in get_voice: {str(e)}")
            raise
        except VoiceGenerationFailed as e:
            logger.error(f"Voice generation failed: {str(e)}")
            raise
        except TimeoutError as e:
            logger.error(f"Voice generation timed out: {str(e)}")
            raise

    def create_voices(self, text, voice_id):
        scripts = split_long_string(text)
        audio_ids = []
        for index, script in enumerate(scripts):
            try:
                audio_id = self.create_voice(script, voice_id)
                audio_ids.append(audio_id)
            except Exception as e:
                logger.error(f"Error creating voice for script {index}: {str(e)}")
        return audio_ids

    def get_voice_audios(self, audio_ids):
        audio_links = []
        for index, audio_id in enumerate(audio_ids):
            try:
                voice_link = self.get_voice(audio_id)
                audio_links.append(voice_link)
            except Exception as e:
                logger.error(f"Error getting voice for audio_id {audio_id}: {str(e)}")
        return audio_links

    def get_voices(self):

        url = "https://api.play.ht/api/v2/voices"

        headers = {
            "AUTHORIZATION": PLAY_API_KEY,
            "X-USER-ID": PLAY_USER_ID,
        }

        response = requests.get(url, headers=headers)

        print(response.text)

    def create_audio(self, text):
        try:
            client = Client(user_id=PLAY_USER_ID, api_key=PLAY_API_KEY)
            options = TTSOptions(
                voice="s3://voice-cloning-zero-shot/8f197327-3682-4004-92c1-e8549d36f53f/enhanced/manifest.json",
                sample_rate=44_100,
                format=Format.FORMAT_MP3,
                speed=1,
            )

            output_file = "../data/output_audio.mp3"

            with open(output_file, "wb") as f:
                for chunk in client.tts(
                    text=text, voice_engine="PlayHT2.0-turbo", options=options
                ):
                    f.write(chunk)

            logger.info(f"Audio saved to: {os.path.abspath(output_file)}")

        except Exception as e:
            logger.error(f"Error in create_audio: {str(e)}")
            raise

    def clone_voice_using_file(self, sample_file_url, voice_name):
        try:
            url = "https://api.play.ht/api/v2/cloned-voices/instant"
            boundary = "---011000010111000001101001"
            headers = {
                "accept": "application/json",
                "content-type": f"multipart/form-data; boundary={boundary}",
                "AUTHORIZATION": PLAY_API_KEY,
                "X-USER-ID": PLAY_USER_ID,
            }
            payload = (
                f"--{boundary}\r\n"
                f'Content-Disposition: form-data; name="sample_file_url"\r\n\r\n'
                f"{sample_file_url}\r\n"
                f"--{boundary}\r\n"
                f'Content-Disposition: form-data; name="voice_name"\r\n\r\n'
                f"{voice_name}\r\n"
                f"--{boundary}--"
            )

            response = requests.post(url, data=payload, headers=headers)
            response.raise_for_status()

            return response.json()["id"]

        except requests.exceptions.RequestException as e:
            logger.error(f"Error in clone_voice_using_file: {str(e)}")
            raise

    def delete_voice(self, voice_id):
        try:
            url = "https://api.play.ht/api/v2/cloned-voices/"
            payload = {"voice_id": voice_id}
            headers = {
                "accept": "application/json",
                "content-type": "application/json",
                "AUTHORIZATION": PLAY_API_KEY,
                "X-USER-ID": PLAY_USER_ID,
            }

            response = requests.delete(url, json=payload, headers=headers)
            response.raise_for_status()

            logger.info(f"Delete voice response: {response.text}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Error in delete_voice: {str(e)}")
            raise

    def get_list_of_voices(self):
        try:
            url = "https://api.play.ht/api/v2/voices"
            headers = {
                "accept": "application/json",
                "AUTHORIZATION": PLAY_API_KEY,
                "X-USER-ID": PLAY_USER_ID,
            }

            response = requests.get(url, headers=headers)
            response.raise_for_status()

            return response.json(), 200

        except requests.exceptions.RequestException as e:
            logger.error(f"Error in get_list_of_voices: {str(e)}")
            raise
