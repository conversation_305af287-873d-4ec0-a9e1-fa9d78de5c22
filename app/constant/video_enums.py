from enum import Enum


class VideoType(str, Enum):
    SHORT = "SHORT"
    LONG = "LONG"


class VideoViewType(str, Enum):
    LANDSCAPE = "LANDSCAPE"
    PORTRAIT = "PORTRAIT"


class VideoStatus(str, Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    FAILED = "FAILED"
    COMPLETED = "COMPLETED"
    TIMED_OUT = "TIMED_OUT"


class ScheduleVideoStatus(str, Enum):
    SCHEDULED = "SCHEDULED"
    UPLOADED = "UPLOADED"
    FAILED = "FAILED"


class VideoQuality(str, Enum):
    Q480p = "480p"
    Q720p = "720p"
    Q1080p = "1080p"
    Q4k = "4k"


class UploadDomain(str, Enum):
    YOUTUBE = "youtube"
    LINKEDIN = "linkedin"  #
    INSTAGRAM = "instagram"
