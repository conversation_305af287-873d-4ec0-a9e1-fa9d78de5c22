from enum import Enum
from config import SESSION_FOLDER


class Google(Enum):

    # Define login scope as a combination of 'profile', 'email', and SCOPES
    LOGIN_SCOPE = [
        "https://www.googleapis.com/auth/userinfo.profile",
        "https://www.googleapis.com/auth/userinfo.email",
        "openid",
    ]

    SESSION_FOLDER = "session"

    # Set CREDENTIALS_FILE_PATH constant as a formatted string
    CREDENTIALS_FILE_PATH = f"{SESSION_FOLDER}/credentials.json"

    # Set GOOGLE_LOGIN constant as a string value
    GOOGLE_LOGIN = "google.login"

    NOT_REPEAT = "No Repeat"
