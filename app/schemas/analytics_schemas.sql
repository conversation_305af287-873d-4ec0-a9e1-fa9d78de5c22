-- Analytics Service Database Schema
-- This file contains all table definitions for the analytics service

-- Applications Table (removed tags and environment)
CREATE TABLE IF NOT EXISTS applications (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    user_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    
    -- Status
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- API Keys Table (maintain API key IDs)
CREATE TABLE IF NOT EXISTS api_keys (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    application_id VARCHAR REFERENCES applications(id) NOT NULL,
    user_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    public_key VARCHAR NOT NULL UNIQUE,
    private_key_hash VARCHAR NOT NULL,
    scopes TEXT[],
    
    -- Status and expiration
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Analytics Events Table (maintain workflow IDs and agent IDs)
CREATE TABLE IF NOT EXISTS analytics_events (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Event information
    event_type VARCHAR NOT NULL,
    service_type VARCHAR NOT NULL,
    
    -- Entity information (maintain workflow IDs, agent IDs)
    entity_id VARCHAR NOT NULL,
    entity_type VARCHAR NOT NULL, -- workflow, agent, mcp, application
    user_id VARCHAR NOT NULL,
    
    -- Reference IDs for cross-service tracking
    workflow_id VARCHAR, -- Reference to workflow service
    agent_id VARCHAR,    -- Reference to agent service
    mcp_id VARCHAR,      -- Reference to MCP service
    api_key_id VARCHAR REFERENCES api_keys(id), -- Reference to API key
    
    -- Additional data
    event_metadata JSONB,
    
    -- Status
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Service Metrics Table (maintain workflow IDs and agent IDs)
CREATE TABLE IF NOT EXISTS service_metrics (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Service information
    service_type VARCHAR NOT NULL,
    entity_id VARCHAR NOT NULL,
    entity_type VARCHAR NOT NULL, -- workflow, agent, mcp, application
    
    -- Reference IDs for cross-service tracking
    workflow_id VARCHAR, -- Reference to workflow service
    agent_id VARCHAR,    -- Reference to agent service
    mcp_id VARCHAR,      -- Reference to MCP service
    
    -- Metrics
    usage_count INTEGER DEFAULT 0,
    average_rating FLOAT DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    
    -- Performance metrics
    total_execution_time_ms BIGINT DEFAULT 0,
    average_execution_time_ms INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    
    -- Status
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- User Activity Table
CREATE TABLE IF NOT EXISTS user_activities (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- User information
    user_id VARCHAR NOT NULL,
    
    -- Activity metrics by service
    mcp_usage_count INTEGER DEFAULT 0,
    workflow_usage_count INTEGER DEFAULT 0,
    agent_usage_count INTEGER DEFAULT 0,
    
    mcp_creation_count INTEGER DEFAULT 0,
    workflow_creation_count INTEGER DEFAULT 0,
    agent_creation_count INTEGER DEFAULT 0,
    
    -- API usage
    api_call_count INTEGER DEFAULT 0,
    api_key_usage_count INTEGER DEFAULT 0,
    
    last_activity_date TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Usage Events Table (maintain workflow IDs and agent IDs)
CREATE TABLE IF NOT EXISTS usage_events (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Usage information
    user_id VARCHAR NOT NULL,
    entity_type VARCHAR NOT NULL, -- agent, workflow, mcp, application
    entity_id VARCHAR NOT NULL,
    action VARCHAR NOT NULL, -- invoke, run, execute, create, update, delete
    
    -- Reference IDs for cross-service tracking
    workflow_id VARCHAR, -- Reference to workflow service
    agent_id VARCHAR,    -- Reference to agent service
    mcp_id VARCHAR,      -- Reference to MCP service
    api_key_id VARCHAR REFERENCES api_keys(id), -- Reference to API key used
    
    -- Credit and cost tracking
    credits_used FLOAT DEFAULT 0.0,
    cost FLOAT DEFAULT 0.0,
    
    -- Performance tracking
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    
    -- Additional data
    event_metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Activation Events Table
CREATE TABLE IF NOT EXISTS activation_events (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Activation information
    user_id VARCHAR NOT NULL,
    event_type VARCHAR NOT NULL,
    entity_id VARCHAR,
    
    -- Reference IDs for cross-service tracking
    workflow_id VARCHAR, -- Reference to workflow service
    agent_id VARCHAR,    -- Reference to agent service
    mcp_id VARCHAR,      -- Reference to MCP service
    
    event_metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Webhooks Table
CREATE TABLE IF NOT EXISTS webhooks (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Webhook information
    user_id VARCHAR NOT NULL,
    application_id VARCHAR REFERENCES applications(id),
    url VARCHAR NOT NULL,
    event_types TEXT[] NOT NULL,
    secret VARCHAR,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Status and metrics
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    delivery_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_delivery_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Webhook Logs Table
CREATE TABLE IF NOT EXISTS webhook_logs (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Log information
    webhook_id VARCHAR REFERENCES webhooks(id) NOT NULL,
    event_type VARCHAR NOT NULL,
    payload JSONB NOT NULL,
    response_status INTEGER,
    response_body TEXT,
    error_message TEXT,
    success BOOLEAN NOT NULL,
    retry_count INTEGER DEFAULT 0,
    
    -- Timestamps
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_application_id ON api_keys(application_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_public_key ON api_keys(public_key);

CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_entity_id ON analytics_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_workflow_id ON analytics_events(workflow_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_agent_id ON analytics_events(agent_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_mcp_id ON analytics_events(mcp_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_api_key_id ON analytics_events(api_key_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);

CREATE INDEX IF NOT EXISTS idx_service_metrics_entity_id ON service_metrics(entity_id);
CREATE INDEX IF NOT EXISTS idx_service_metrics_workflow_id ON service_metrics(workflow_id);
CREATE INDEX IF NOT EXISTS idx_service_metrics_agent_id ON service_metrics(agent_id);
CREATE INDEX IF NOT EXISTS idx_service_metrics_mcp_id ON service_metrics(mcp_id);

CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);

CREATE INDEX IF NOT EXISTS idx_usage_events_user_id ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_entity_id ON usage_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_workflow_id ON usage_events(workflow_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_agent_id ON usage_events(agent_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_mcp_id ON usage_events(mcp_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_api_key_id ON usage_events(api_key_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_created_at ON usage_events(created_at);

CREATE INDEX IF NOT EXISTS idx_activation_events_user_id ON activation_events(user_id);
CREATE INDEX IF NOT EXISTS idx_activation_events_workflow_id ON activation_events(workflow_id);
CREATE INDEX IF NOT EXISTS idx_activation_events_agent_id ON activation_events(agent_id);
CREATE INDEX IF NOT EXISTS idx_activation_events_mcp_id ON activation_events(mcp_id);

CREATE INDEX IF NOT EXISTS idx_webhooks_user_id ON webhooks(user_id);
CREATE INDEX IF NOT EXISTS idx_webhooks_application_id ON webhooks(application_id);
CREATE INDEX IF NOT EXISTS idx_webhook_logs_webhook_id ON webhook_logs(webhook_id);

-- Update triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analytics_events_updated_at BEFORE UPDATE ON analytics_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_metrics_updated_at BEFORE UPDATE ON service_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_activities_updated_at BEFORE UPDATE ON user_activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhooks_updated_at BEFORE UPDATE ON webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();