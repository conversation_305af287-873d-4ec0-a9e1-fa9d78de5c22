from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class WaitlistRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.WAITLIST_COLLECTION]

    def create_waitlist(self, waitlist_data) -> dict:
        """
        Create a new waitlist document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        waitlist_data["created_at"] = current_time
        waitlist_data["updated_at"] = current_time
        result = self.collection.insert_one(waitlist_data)

        return self.get_waitlist_by_id(str(result.inserted_id))

    def get_waitlist_by_id(self, waitlist_id: str) -> dict:
        """
        Retrieve a waitlist document by its ID.
        """
        try:
            waitlist = self.collection.find_one({"_id": ObjectId(waitlist_id)})
            return waitlist
        except Exception as e:
            print("Could not find waitlist with _id ", str(e))
            return None

    def get_waitlist_by_email(self, email: str) -> dict:
        """
        Retrieve a waitlist document by its ID.
        """
        try:
            waitlist = self.collection.find_one({"email": email})
            return waitlist
        except Exception as e:
            print("Could not find waitlist with _id ", str(e))
            return None

    def get_waitlist_by_id_and_user(self, waitlist_id: str, user_id: str) -> dict:
        """
        Retrieve a waitlist document by its ID.
        """
        waitlist = self.collection.find_one(
            {"_id": ObjectId(waitlist_id), "user_id": ObjectId(user_id)}
        )
        return waitlist

    def get_waitlists_by_user(self, user_id: str) -> list:
        """
        Retrieve all waitlist documents for a specific user.
        """
        try:
            waitlists = self.collection.find_one({"user_id": ObjectId(user_id)})
            return waitlists
        except Exception as e:
            print("Could not find waitlist with user_id ", str(e))
            return None

    def update_waitlist(self, waitlist_id: str, update_data: dict) -> dict:
        """
        Update a waitlist document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(waitlist_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_waitlist_by_id(waitlist_id)
        return None

    def delete_waitlist(self, waitlist_id: str) -> bool:
        """
        Delete a waitlist document by its ID.
        """
        result = self.collection.delete_one(
            {
                "_id": ObjectId(waitlist_id),
            }
        )
        return result.deleted_count > 0

    def get_waitlists_by_waitlist(self, waitlist_id: str) -> list:
        """
        Retrieve all waitlist documents associated with a specific waitlist.
        """
        waitlists = list(self.collection.find({"waitlist_id": ObjectId(waitlist_id)}))
        return waitlists

    def update_waitlist_status(self, waitlist_id: str, new_status: str) -> dict:
        """
        Update the status of a waitlist document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(waitlist_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_waitlist_by_id(waitlist_id)
        return None

    def create_waitlist_list(self, waitlist_list: list) -> list:
        """
        Create a list of new waitlists.
        """
        result = []
        for waitlist in waitlist_list:
            created_waitlist = self.create_waitlist(waitlist)
            result.append(self.to_dict(created_waitlist))
        return result

    def get_waitlist_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of waitlists in the collection.

        :param filter_criteria: Optional dictionary to filter the waitlists before counting
        :return: The count of waitlists matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_waitlists_paginated(
        self,
        page: int = 1,
        per_page: int = 10,
        status: str = None,
    ) -> dict:
        """
        Get waitlists with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {}
        if status is not None:
            filter_criteria["status"] = status

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        waitlists = list(
            self.collection.find(filter_criteria).skip(skip).limit(per_page)
        )

        # Convert ObjectId to string for each waitlist
        waitlists = [self.to_dict(waitlist) for waitlist in waitlists]

        return {
            "waitlists": waitlists,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, waitlist_data):
        return {
            "_id": str(waitlist_data.get("_id", None)),
            "name": waitlist_data.get("name", None),
            "created_at": waitlist_data.get("created_at", None),
            "updated_at": waitlist_data.get("updated_at", None),
            "email": waitlist_data.get("email", None),
            "status": waitlist_data.get("status", None),
            "organization": waitlist_data.get("organization", None),
            "job_title": waitlist_data.get("job_title", None),
        }

    def get_waitlists_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        waitlists = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "waitlists": waitlists,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, waitlist_data):

        if isinstance(waitlist_data, dict):

            waitlist_data = self.convert_single_document_to_dict(waitlist_data)

        if isinstance(waitlist_data, list):

            for index, waitlist in enumerate(waitlist_data):
                waitlist = self.convert_single_document_to_dict(waitlist)
                waitlist_data[index] = waitlist

        return waitlist_data
