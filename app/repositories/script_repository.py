import datetime

from bson import ObjectId

from app.database.connection import MongoDBConnection

import app.database.collection_name as collection

from app.exceptions.script_exceptions import (
    ScriptCreationError,
)


class ScriptRepository:
    """
    Repository class for handling authentication-related database operations.
    Provides methods to create a user, find users, check user existence, and update user password.
    """

    def __init__(self):
        """
        Initializes the ScriptRepository with a database connection.
        """

        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.SCRIPT_COLLECTION]

    def create_script(self, script_data) -> str:
        """
        Creates a new script document in the database for the specified user.

        This function inserts a new document into the 'scripts' collection with the
        following fields:
        - user_id: The ObjectId of the user associated with the script.
        - script: The actual script content as a string.
        - created_at: The timestamp (in UTC) when the script was created.
        - updated_at: The timestamp (in UTC) when the script was last updated,
                    which is initially set to the same value as created_at.
        - type: The type or category of the script (e.g., 'python', 'bash', etc.).

        Parameters:
        - user_id (str): The unique identifier of the user creating the script.
        - script (str): The content of the script.
        - type (str): The type or category of the script.

        Returns:
        - str: The unique identifier (_id) of the created script as a string.

        Raises:
        - ScriptCreationError: If there is a failure during script creation,
                            typically due to a database exception.

        Example usage:
        >>> repo = ScriptRepository()
        >>> user_id = "5f9b1b9b9c5d4a0b8c8b4567"
        >>> script_content = "print('Hello, World!')"
        >>> script_type = "python"
        >>> script_id = repo.create_script(user_id, script_content, script_type)
        >>> print(script_id)
        "5f9b1b9b9c5d4a0b8c8b4568"
        """

        try:
            current_time = datetime.datetime.utcnow().isoformat()
            script_data["created_at"] = current_time
            script_data["updated_at"] = current_time

            result = self.collection.insert_one(script_data)
            return str(result.inserted_id)

        except Exception as e:
            raise ScriptCreationError(f"Failed to create user: {e}")

    def get_script(self, script_id: str) -> dict:
        """
        Retrieves a script by its ID.

        Parameters:
        - script_id (str): The unique identifier of the script.

        Returns:
        - dict: The script document if found, or None if not found.
        """
        try:
            result = self.collection.find_one({"_id": ObjectId(script_id)})
            return result
        except Exception as e:
            print(f"Error retrieving script by _id: {e}")
            return None

    def get_script_of_user(
        self,
        user_id: str,
        script_id: str,
    ) -> dict:
        """
        Retrieves a script by its ID.

        Parameters:
        - script_id (str): The unique identifier of the script.

        Returns:
        - dict: The script document if found, or None if not found.
        """
        try:
            result = self.collection.find_one(
                {"_id": ObjectId(script_id), "user_id": ObjectId(user_id)}
            )
            return result
        except Exception as e:
            print(f"Error retrieving script: {e}")
            return None

    def get_script_of_type_and_title(
        self,
        video_type: str,
        title: str,
    ) -> dict:
        """
        Retrieves a script by its ID.

        Parameters:
        - script_id (str): The unique identifier of the script.

        Returns:
        - dict: The script document if found, or None if not found.
        """
        try:
            result = self.collection.find_one(
                {"video_type": video_type, "title": title}
            )
            return result
        except Exception as e:
            print(f"Error retrieving script: {e}")
            return None

    def update_script(self, script_id: str, updated_script) -> bool:
        """
        Updates the script for the given script ID.

        Parameters:
        - script_id (str): The unique identifier of the script.
        - updated_script (str): The updated script content.

        Returns:
        - bool: True if the script was updated successfully, False otherwise.
        """
        try:
            current_time = datetime.datetime.utcnow().isoformat()
            updated_script["updated_at"] = current_time
            result = self.collection.update_one(
                {"_id": ObjectId(script_id)},
                {"$set": updated_script},
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating script: {e}")
            return False

    def delete_script(self, script_id: str) -> bool:
        """
        Deletes a script by its ID.

        Parameters:
        - script_id (str): The unique identifier of the script.

        Returns:
        - bool: True if the script was deleted successfully, False otherwise.
        """
        try:
            result = self.collection.delete_one({"_id": ObjectId(script_id)})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting script: {e}")
            return False

    def get_all_scripts_by_user(
        self, user_id: str, page: int, limit: int, filter: str = None
    ) -> tuple:
        """
        Retrieves paginated and filtered scripts for a given user.

        Parameters:
        - user_id (str): The unique identifier of the user.
        - page (int): The page number.
        - limit (int): The number of items per page.
        - filter (str, optional): Filter scripts by name.

        Returns:
        - tuple: A tuple containing a list of script documents for the user and the total count.
        """
        try:
            query = {"user_id": ObjectId(user_id)}
            if filter:
                query["name"] = {"$regex": filter, "$options": "i"}

            total_count = self.collection.count_documents(query)

            skip = (page - 1) * limit
            results = self.collection.find(query).skip(skip).limit(limit)

            return list(results), total_count
        except Exception as e:
            print(f"Error retrieving scripts for user: {e}")
            return [], 0

    def convert_single_document_to_dict(self, script_data):
        return {
            "_id": str(script_data.get("_id", "")),
            "user_id": str(script_data.get("user_id", "")),
            "script": script_data.get("script", ""),
            "type": script_data.get("type", None),
            "video_type": script_data.get("video_type", None),
            "keywords": script_data.get("keywords", {}),
            "title": script_data.get("title", ""),
            "link": script_data.get("link", None),
            "created_at": script_data.get("created_at", None),
            "updated_at": script_data.get("updated_at", None),
            "scenes": script_data.get("scenes", None),
            "video_link": script_data.get("video_link", None),
            "music_media_id": str(script_data.get("music_media_id", "")),
            "add_brand": script_data.get("add_brand", None),
        }

    def to_dict(self, script_data):

        if isinstance(script_data, dict):

            script_data = self.convert_single_document_to_dict(script_data)

        if isinstance(script_data, list):

            for index, script in enumerate(script_data):
                script = self.convert_single_document_to_dict(script)
                script_data[index] = script

        return script_data
