from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime

from app.constant.common_enum import Status


class SubscriptionRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.SUBSCRIPTION_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_subscription(self, subscription_data) -> dict:
        """
        Create a new subscription document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        subscription_data["created_at"] = current_time
        subscription_data["updated_at"] = current_time

        subscription_data["status"] = Status.ACTIVE.value

        result = self.collection.insert_one(subscription_data)
        return self.get_subscription_by_id(str(result.inserted_id))

    def get_subscription_by_id(self, subscription_id: str) -> dict:
        """
        Retrieve a subscription document by its ID.
        """
        try:
            subscription = self.collection.find_one({"_id": ObjectId(subscription_id)})
            return subscription

        except Exception as e:
            print("Could not find subscription with ID ", str(e))
            return None

    def get_subscription_by_stripe_id(self, subscription_id: str) -> dict:
        """
        Retrieve a subscription document by its ID.
        """
        try:
            subscription = self.collection.find_one(
                {"stripe_subscription_id": subscription_id}
            )
            return subscription

        except Exception as e:
            print("Could not find subscription with ID ", str(e))
            return None

    def get_subscription_by_user_id(self, user_id: str) -> dict:
        """
        Retrieve a subscription document by its ID.
        """
        try:
            subscription = self.collection.find_one({"user_id": ObjectId(user_id)})
            return subscription
        except Exception as e:
            print("Could not find subscription with ID ", str(e))
            return None

    def get_subscriptions(self, active=None, interval=None) -> list:
        """
        Retrieve all subscription documents for a specific user.
        """

        filter = {"status": Status.ACTIVE.value}

        if active is not None:
            filter["active"] = active
        if interval is not None:
            filter["interval"] = interval

        subscriptions = list(self.collection.find(filter))
        return subscriptions

    def update_subscription(self, subscription_id: str, update_data: dict) -> dict:
        """
        Update a subscription document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(subscription_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_subscription_by_id(subscription_id)
        return None

    def delete_subscription(self, subscription_id: str) -> bool:
        """
        Delete a subscription document by its ID.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(subscription_id)},
            {"$set": {"status": Status.INACTIVATE.value}},
        )
        return result.modified_count > 0

    def update_subscription_status(self, subscription_id: str, new_status: str) -> dict:
        """
        Update the status of a subscription document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(subscription_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_subscription_by_id(subscription_id)
        return None

    def get_populated_user_subscription(self, user_id: str) -> dict:
        """
        Retrieve user subscription with populated plan information.
        """
        pipeline = [
            # Match the user subscription for the given user_id
            {"$match": {"user_id": ObjectId(user_id)}},
            # Lookup user information
            {
                "$lookup": {
                    "from": collection.SUBSCRIPTION_PLAN_COLLECTION,
                    "localField": "plan_id",
                    "foreignField": "_id",
                    "as": "plan",
                }
            },
            {"$unwind": {"path": "$plan", "preserveNullAndEmptyArrays": True}},
        ]

        result = list(self.collection.aggregate(pipeline))

        if result:
            # Convert ObjectId to string for JSON serialization
            populated_subscription = self.to_dict(result[0])
            return populated_subscription
        return None

    def convert_single_document_to_dict(self, subscription_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in subscription_data.items()}

    def to_dict(self, subscription_data):
        if isinstance(subscription_data, dict):
            return self.convert_single_document_to_dict(subscription_data)
        elif isinstance(subscription_data, list):
            return [
                self.convert_single_document_to_dict(item) for item in subscription_data
            ]
        else:
            return subscription_data
