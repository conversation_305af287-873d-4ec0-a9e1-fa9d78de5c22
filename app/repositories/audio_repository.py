from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime


class AudioRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.AUDIO_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_audio(self, audio_data) -> dict:
        """
        Create a new audio document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        audio_data["created_at"] = current_time
        audio_data["updated_at"] = current_time

        result = self.collection.insert_one(audio_data)
        return self.get_audio_by_id(str(result.inserted_id))

    def get_audio_by_id(self, audio_id: str) -> dict:
        """
        Retrieve a audio document by its ID.
        """
        audio = self.collection.find_one({"_id": ObjectId(audio_id)})
        return audio

    def get_audio_by_script_and_voice_id(self, script_id: str, voice_id: str) -> dict:
        """
        Retrieve a audio document by its ID.
        """
        audio = self.collection.find_one(
            {"script_id": ObjectId(script_id), "voice_id": ObjectId(voice_id)}
        )
        return audio

    def get_audio_by_script(self, script_id: str) -> dict:
        """
        Retrieve a audio document by its ID.
        """
        audio = self.collection.find_one({"script_id": ObjectId(script_id)})
        return audio

    def get_audios_by_user(self, user_id: str) -> list:
        """
        Retrieve all audio documents for a specific user.
        """
        audios = list(self.collection.find({"user_id": ObjectId(user_id)}))
        return audios

    def update_audio(self, audio_id: str, update_data: dict) -> dict:
        """
        Update a audio document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(audio_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_audio_by_id(audio_id)
        return None

    def delete_audio(self, audio_id: str) -> bool:
        """
        Delete a audio document by its ID.
        """
        result = self.collection.delete_one({"_id": ObjectId(audio_id)})
        return result.deleted_count > 0

    def get_audios_by_script(self, script_id: str) -> list:
        """
        Retrieve all audio documents associated with a specific script.
        """
        audios = list(self.collection.find({"script_id": ObjectId(script_id)}))
        return audios

    def update_audio_status(self, audio_id: str, new_status: str) -> dict:
        """
        Update the status of a audio document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(audio_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_audio_by_id(audio_id)
        return None

    def convert_single_document_to_dict(self, audio_data):
        return {
            "_id": str(audio_data.get("_id", "")),
            "user_id": str(audio_data.get("user_id", None)),
            "audio_ids": audio_data.get("audio_ids", None),
            "script_id": str(audio_data.get("script_id", None)),
            "voice_id": str(audio_data.get("voice_id", None)),
            "status": audio_data.get("status", None),
            "audio_links": audio_data.get("audio_links", None),
            "subtitles": audio_data.get("subtitles", None),
            "created_at": audio_data.get("created_at", None),
            "updated_at": audio_data.get("updated_at", None),
        }

    def to_dict(self, audio_data):

        if isinstance(audio_data, dict):

            audio_data = self.convert_single_document_to_dict(audio_data)

        if isinstance(audio_data, list):

            for index, audio in enumerate(audio_data):
                audio = self.convert_single_document_to_dict(audio)
                audio_data[index] = audio

        return audio_data
