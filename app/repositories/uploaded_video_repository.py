from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class UploadedVideoRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.UPLOADED_VIDEO_COLLECTION]

    def create_uploaded_video(self, uploaded_video_data) -> dict:
        """
        Create a new uploaded_video document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        uploaded_video_data["created_at"] = current_time
        uploaded_video_data["updated_at"] = current_time
        result = self.collection.insert_one(uploaded_video_data)

        return self.get_uploaded_video_by_id(str(result.inserted_id))

    def get_uploaded_video_by_id(self, uploaded_video_id: str) -> dict:
        """
        Retrieve a uploaded_video document by its ID.
        """
        uploaded_video = self.collection.find_one({"_id": ObjectId(uploaded_video_id)})
        return uploaded_video

    def get_uploaded_video_by_id_and_user(
        self, uploaded_video_id: str, user_id: str
    ) -> dict:
        """
        Retrieve a uploaded_video document by its ID.
        """
        uploaded_video = self.collection.find_one(
            {"_id": ObjectId(uploaded_video_id), "user_id": ObjectId(user_id)}
        )
        return uploaded_video

    def get_uploaded_videos_by_user(self, user_id: str) -> list:
        """
        Retrieve all uploaded_video documents for a specific user.
        """
        uploaded_videos = list(self.collection.find({"user_id": ObjectId(user_id)}))

        return uploaded_videos

    def update_uploaded_video(self, uploaded_video_id: str, update_data: dict) -> dict:
        """
        Update a uploaded_video document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(uploaded_video_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_uploaded_video_by_id(uploaded_video_id)
        return None

    def delete_uploaded_video(self, user_id: str, uploaded_video_id: str) -> bool:
        """
        Delete a uploaded_video document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(uploaded_video_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_uploaded_videos_by_uploaded_video(self, uploaded_video_id: str) -> list:
        """
        Retrieve all uploaded_video documents associated with a specific uploaded_video.
        """
        uploaded_videos = list(
            self.collection.find({"uploaded_video_id": ObjectId(uploaded_video_id)})
        )
        for uploaded_video in uploaded_videos:
            uploaded_video["_id"] = str(uploaded_video["_id"])
            uploaded_video["user_id"] = str(uploaded_video["user_id"])
            uploaded_video["uploaded_video_id"] = str(
                uploaded_video["uploaded_video_id"]
            )
        return uploaded_videos

    def update_uploaded_video_status(
        self, uploaded_video_id: str, new_status: str
    ) -> dict:
        """
        Update the status of a uploaded_video document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(uploaded_video_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_uploaded_video_by_id(uploaded_video_id)
        return None

    def create_uploaded_video_list(self, uploaded_video_list: list) -> list:
        """
        Create a list of new uploaded_videos.
        """
        result = []
        for uploaded_video in uploaded_video_list:
            created_uploaded_video = self.create_uploaded_video(uploaded_video)
            result.append(self.to_dict(created_uploaded_video))
        return result

    def get_uploaded_video_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of uploaded_videos in the collection.

        :param filter_criteria: Optional dictionary to filter the uploaded_videos before counting
        :return: The count of uploaded_videos matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_uploaded_videos_paginated(
        self,
        user_id,
        video_id,
        page: int = 1,
        per_page: int = 10,
    ) -> dict:
        """
        Get uploaded_videos with pagination and optional filters on status and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param status: Optional filter for status ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {"user_id": ObjectId(user_id), "video_id": ObjectId(video_id)}

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        uploaded_videos = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each uploaded_video
        uploaded_videos = [
            self.to_dict(uploaded_video) for uploaded_video in uploaded_videos
        ]

        return {
            "videos": uploaded_videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def get_uploaded_video_list(
        self,
        user_id: str,
        page: int = 1,
        per_page: int = 10,
        status: str = None,
        is_cloned: bool = None,
    ) -> dict:
        """
        Retrieve a list of uploaded videos with optional filtering and pagination.

        :param user_id: The ID of the user whose uploaded videos to retrieve
        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param status: Optional filter for video status
        :param is_cloned: Optional filter for cloned status
        :return: A dictionary containing the list of uploaded videos and pagination metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {"user_id": ObjectId(user_id)}
        if status is not None:
            filter_criteria["status"] = status
        if is_cloned is not None:
            filter_criteria["is_cloned"] = is_cloned

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        uploaded_videos = list(
            self.collection.find(filter_criteria)
            .sort("uploaded_time", DESCENDING)
            .skip(skip)
            .limit(per_page)
        )

        # Populate user and video data for each uploaded video
        populated_uploaded_videos = []
        for uploaded_video in uploaded_videos:
            populated_uploaded_video = self.populate_uploaded_video(uploaded_video)
            populated_uploaded_videos.append(populated_uploaded_video)

        return {
            "uploaded_videos": self.to_dict(populated_uploaded_videos),
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def populate_uploaded_video(self, uploaded_video: dict) -> dict:
        """
        Populate a single uploaded video with user and video data.
        """
        user_collection = self.dbname[collection.USER_COLLECTION]
        video_collection = self.dbname[collection.VIDEO_COLLECTION]

        user = user_collection.find_one({"_id": uploaded_video["user_id"]})
        video = video_collection.find_one({"_id": uploaded_video["video_id"]})

        return {
            **uploaded_video,
            "user": self.to_dict(user) if user else None,
            "video": self.to_dict(video) if video else None,
        }

    def get_populated_uploaded_video(self, uploaded_video_id: str) -> dict:
        """
        Retrieve a uploaded_video document by its ID and populate it with user and video data.
        """
        # Get the uploaded video document
        uploaded_video = self.collection.find_one({"_id": ObjectId(uploaded_video_id)})

        if not uploaded_video:
            return None

        # Get the user data
        user_collection = self.dbname[collection.USER_COLLECTION]
        user = user_collection.find_one({"_id": uploaded_video["user_id"]})

        # Get the video data
        video_collection = self.dbname[collection.VIDEO_COLLECTION]
        video = video_collection.find_one({"_id": uploaded_video["video_id"]})

        # Populate the uploaded video with user and video data
        populated_uploaded_video = {
            **self.to_dict(uploaded_video),
            "user": self.to_dict(user) if user else None,
            "video": self.to_dict(video) if video else None,
        }

        return populated_uploaded_video

    def convert_single_document_to_dict(self, uploaded_video_data):
        return {
            "_id": str(uploaded_video_data.get("_id", "")),
            "user_id": str(uploaded_video_data.get("user_id", "")),
            "video_id": str(uploaded_video_data.get("video_id", "")),
            "upload_video_id": uploaded_video_data.get("upload_video_id", None),
            "upload_link": uploaded_video_data.get("upload_link", ""),
            "created_at": uploaded_video_data.get("created_at", None),
            "updated_at": uploaded_video_data.get("updated_at", None),
            "upload_domain": uploaded_video_data.get("upload_domain", None),
        }

    def get_uploaded_videos_by_user_paginated(
        self, user_id: str, video_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents(
            {"user_id": ObjectId(user_id), "video_id": ObjectId(video_id)}
        )

        sort_criteria = [("created_at", -1)]
        # Get paginated results
        uploaded_videos = list(
            self.collection.find(
                {"user_id": ObjectId(user_id), "video_id": ObjectId(video_id)}
            )
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        return {
            "uploaded_videos": uploaded_videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, uploaded_video_data):

        if isinstance(uploaded_video_data, dict):

            uploaded_video_data = self.convert_single_document_to_dict(
                uploaded_video_data
            )

        if isinstance(uploaded_video_data, list):

            for index, uploaded_video in enumerate(uploaded_video_data):
                uploaded_video = self.convert_single_document_to_dict(uploaded_video)
                uploaded_video_data[index] = uploaded_video

        return uploaded_video_data
