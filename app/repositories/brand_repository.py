from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class BrandRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.BRAND_COLLECTION]

    def create_brand(self, brand_data) -> dict:
        """
        Create a new brand document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        brand_data["created_at"] = current_time
        brand_data["updated_at"] = current_time
        result = self.collection.insert_one(brand_data)

        return self.get_brand_by_id(str(result.inserted_id))

    def get_brand_by_id(self, brand_id: str) -> dict:
        """
        Retrieve a brand document by its ID.
        """
        try:
            brand = self.collection.find_one({"_id": ObjectId(brand_id)})
            return brand
        except Exception as e:
            print("Could not find brand with _id ", str(e))
            return None

    def get_brand_by_id_and_user(self, brand_id: str, user_id: str) -> dict:
        """
        Retrieve a brand document by its ID.
        """
        brand = self.collection.find_one(
            {"_id": ObjectId(brand_id), "user_id": ObjectId(user_id)}
        )
        return brand

    def get_brands_by_user(self, user_id: str) -> list:
        """
        Retrieve all brand documents for a specific user.
        """
        try:
            brands = self.collection.find_one({"user_id": ObjectId(user_id)})
            return brands
        except Exception as e:
            print("Could not find brand with user_id ", str(e))
            return None

    def update_brand(self, brand_id: str, update_data: dict) -> dict:
        """
        Update a brand document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(brand_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_brand_by_id(brand_id)
        return None

    def delete_brand(self, user_id: str, brand_id: str) -> bool:
        """
        Delete a brand document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(brand_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_brands_by_brand(self, brand_id: str) -> list:
        """
        Retrieve all brand documents associated with a specific brand.
        """
        brands = list(self.collection.find({"brand_id": ObjectId(brand_id)}))
        return brands

    def update_brand_status(self, brand_id: str, new_status: str) -> dict:
        """
        Update the status of a brand document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(brand_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_brand_by_id(brand_id)
        return None

    def create_brand_list(self, brand_list: list) -> list:
        """
        Create a list of new brands.
        """
        result = []
        for brand in brand_list:
            created_brand = self.create_brand(brand)
            result.append(self.to_dict(created_brand))
        return result

    def get_brand_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of brands in the collection.

        :param filter_criteria: Optional dictionary to filter the brands before counting
        :return: The count of brands matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_brands_paginated(
        self,
        page: int = 1,
        per_page: int = 10,
        gender: str = None,
        is_cloned: bool = False,
    ) -> dict:
        """
        Get brands with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {}
        if gender is not None:
            filter_criteria["gender"] = gender

        if is_cloned is not None:
            filter_criteria["is_cloned"] = is_cloned
        else:
            filter_criteria["is_cloned"] = False

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        brands = list(self.collection.find(filter_criteria).skip(skip).limit(per_page))

        # Convert ObjectId to string for each brand
        brands = [self.to_dict(brand) for brand in brands]

        return {
            "brands": brands,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, brand_data):
        return {
            "_id": str(brand_data.get("_id", None)),
            "user_id": str(brand_data.get("user_id", None)),
            "name": brand_data.get("name", None),
            "logo": brand_data.get("logo", None),
            "created_at": brand_data.get("created_at", None),
            "updated_at": brand_data.get("updated_at", None),
            "intro_landscape": brand_data.get("intro_landscape", None),
            "outro_landscape": brand_data.get("outro_landscape", None),
            "intro_portrait": brand_data.get("intro_portrait", None),
            "outro_portrait": brand_data.get("outro_portrait", None),
            "social_links": brand_data.get("social_links", {}),
        }

    def get_brands_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        brands = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "brands": brands,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, brand_data):

        if isinstance(brand_data, dict):

            brand_data = self.convert_single_document_to_dict(brand_data)

        if isinstance(brand_data, list):

            for index, brand in enumerate(brand_data):
                brand = self.convert_single_document_to_dict(brand)
                brand_data[index] = brand

        return brand_data
