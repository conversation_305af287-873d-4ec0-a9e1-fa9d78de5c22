from bson import ObjectId
from typing import Dict, Any, List
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime
from app.constant.common_enum import Status


class UsageRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.USAGE_COLLECTION]
        self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_usage(self, usage_data) -> dict:
        """
        Create a new usage document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        usage_data["created_at"] = current_time
        usage_data["updated_at"] = current_time
        result = self.collection.insert_one(usage_data)
        return self.get_usage_by_id(str(result.inserted_id))

    def get_usage_by_id(self, usage_id: str) -> dict:
        """
        Retrieve a usage document by its ID.
        """
        try:
            usage = self.collection.find_one({"_id": ObjectId(usage_id)})
            return usage
        except Exception as e:
            print("Could not find usage with ID ", str(e))
            return None

    def get_usage_by_user_id(self, user_id: str) -> dict:
        """
        Retrieve a usage document by its user ID.
        """
        try:
            usage = self.collection.find_one({"user_id": ObjectId(user_id)})
            return usage
        except Exception as e:
            print("Could not find usage with user ID ", str(e))
            return None

    def get_daily_usage_time_series(
        self, user_id: str, start_date: datetime.datetime, end_date: datetime.datetime
    ) -> list:
        """
        Retrieve a time series of daily usage for a given user and date range.
        """
        pipeline = [
            {
                "$match": {
                    "user_id": ObjectId(user_id),
                    "created_at": {"$gte": start_date, "$lte": end_date},
                }
            },
            {
                "$group": {
                    "_id": {
                        "year": {"$year": "$created_at"},
                        "month": {"$month": "$created_at"},
                        "day": {"$dayOfMonth": "$created_at"},
                    },
                    "date": {"$first": "$created_at"},
                    "credits_used": {"$sum": "$credits_used"},
                    "seconds_used": {"$sum": "$seconds_used"},
                }
            },
            {
                "$sort": {
                    "_id.year": ASCENDING,
                    "_id.month": ASCENDING,
                    "_id.day": ASCENDING,
                }
            },
        ]
        daily_usage_time_series = list(self.collection.aggregate(pipeline))
        return daily_usage_time_series

    def get_weekly_usage_time_series(
        self, user_id: str, start_date: datetime.datetime, end_date: datetime.datetime
    ) -> list:
        """
        Retrieve a time series of weekly usage for a given user and date range.
        """
        pipeline = [
            {
                "$match": {
                    "user_id": ObjectId(user_id),
                    "created_at": {"$gte": start_date, "$lte": end_date},
                }
            },
            {
                "$group": {
                    "_id": {
                        "year": {"$year": "$created_at"},
                        "week": {"$week": "$created_at"},
                    },
                    "date": {"$first": "$created_at"},
                    "credits_used": {"$sum": "$credits_used"},
                    "seconds_used": {"$sum": "$seconds_used"},
                }
            },
            {"$sort": {"_id.year": ASCENDING, "_id.week": ASCENDING}},
        ]
        weekly_usage_time_series = list(self.collection.aggregate(pipeline))
        return weekly_usage_time_series

    def get_monthly_usage_time_series(
        self, user_id: str, start_date: datetime.datetime, end_date: datetime.datetime
    ) -> list:
        """
        Retrieve a time series of monthly usage for a given user and date range.
        """
        pipeline = [
            {
                "$match": {
                    "user_id": ObjectId(user_id),
                    "created_at": {"$gte": start_date, "$lte": end_date},
                }
            },
            {
                "$group": {
                    "_id": {
                        "year": {"$year": "$created_at"},
                        "month": {"$month": "$created_at"},
                    },
                    "date": {"$first": "$created_at"},
                    "credits_used": {"$sum": "$credits_used"},
                    "seconds_used": {"$sum": "$seconds_used"},
                }
            },
            {"$sort": {"_id.year": ASCENDING, "_id.month": ASCENDING}},
        ]
        monthly_usage_time_series = list(self.collection.aggregate(pipeline))
        return monthly_usage_time_series

    def get_week_range(self, year: int, week: int) -> tuple:
        """
        Get the start and end dates for a specific week of the year

        :param year: Year of the week
        :param week: Week number (1-53)
        :return: Tuple of start and end dates
        """
        # Validate week number
        if week < 1 or week > 53:
            raise ValueError(f"Invalid week number: {week}. Must be between 1 and 53.")

        # First day of the year
        first_day = datetime.date(year, 1, 1)

        # Adjust to get the first day of the first week
        first_week_start = first_day - datetime.timedelta(days=first_day.weekday())

        # Calculate the start date of the specified week
        start_date = first_week_start + datetime.timedelta(weeks=week - 1)

        # End date is 6 days after the start date (full week)
        end_date = start_date + datetime.timedelta(days=6)

        return start_date, end_date

    def transform_stats(
        self,
        token_stats: List[Dict],
        start_year: int,
        end_year: int,
        month: int = None,
        week: int = None,
        page: int = None,
    ) -> Dict:
        """
        Transform and fill in missing stats for a given time range

        :param token_stats: List of existing token stats
        :param start_year: Starting year for stats
        :param end_year: Ending year for stats
        :param month: Optional specific month
        :param week: Optional specific week
        :param page: Optional page number for pagination
        :return: Dictionary with transformed stats and total count
        """
        # Create a set of existing stats for quick lookup
        existing_stats_set = set()
        for stat in token_stats:
            # Depending on the filter, create different keys
            if week:
                key = (stat.get("year"), stat.get("month"), stat.get("day"))
            elif month:
                key = (stat.get("year"), stat.get("month"), stat.get("day"))
            else:
                key = (stat.get("year"), stat.get("month"), None)
            existing_stats_set.add(key)

        # Determine the date range
        if week:
            # If a specific week is provided, generate daily stats for that week
            start_date, end_date = self.get_week_range(start_year, week)

            current_date = start_date
            while current_date <= end_date:
                stat_key = (current_date.year, current_date.month, current_date.day)

                if stat_key not in existing_stats_set:
                    missing_stat = {
                        "year": current_date.year,
                        "month": current_date.month,
                        "day": current_date.day,
                        "credits_used": 0,
                        "seconds_used": 0,
                        "usage_count": 0,
                    }
                    token_stats.append(missing_stat)
                    existing_stats_set.add(stat_key)

                current_date += datetime.timedelta(days=1)

        elif month:
            # If a specific month is provided, generate daily stats
            start_date = datetime.date(start_year, month, 1)
            end_date = (start_date + datetime.timedelta(days=32)).replace(
                day=1
            ) - datetime.timedelta(days=1)

            current_date = start_date
            while current_date <= end_date:
                stat_key = (current_date.year, current_date.month, current_date.day)

                if stat_key not in existing_stats_set:
                    missing_stat = {
                        "year": current_date.year,
                        "month": current_date.month,
                        "day": current_date.day,
                        "credits_used": 0,
                        "seconds_used": 0,
                        "usage_count": 0,
                    }
                    token_stats.append(missing_stat)
                    existing_stats_set.add(stat_key)

                current_date += datetime.timedelta(days=1)

        else:
            # If no specific month or week, generate monthly stats
            current_date = datetime.date(start_year, 1, 1)
            end_date = datetime.date(end_year, 12, 31)

            while current_date <= end_date:
                stat_key = (current_date.year, current_date.month, None)

                if stat_key not in existing_stats_set:
                    missing_stat = {
                        "year": current_date.year,
                        "month": current_date.month,
                        "day": None,
                        "credits_used": 0,
                        "seconds_used": 0,
                        "usage_count": 0,
                    }
                    token_stats.append(missing_stat)
                    existing_stats_set.add(stat_key)

                # Move to next month
                if current_date.month == 12:
                    current_date = datetime.date(current_date.year + 1, 1, 1)
                else:
                    current_date = datetime.date(
                        current_date.year, current_date.month + 1, 1
                    )

        # Sort the stats
        token_stats.sort(
            key=lambda x: (
                x.get("year", 0),
                x.get("month", 0) or 0,
                x.get("day", 0) or 0,
            )
        )

        # Pagination
        total_count = len(token_stats)

        paginated_stats = token_stats

        return {"token_stats": paginated_stats, "total_count": total_count}

    def get_stats(self, user_id: str, filter: Dict[str, Any]):
        """
        Retrieve and transform user statistics

        :param user_id: Unique identifier of the user
        :param filter: Dictionary containing filtering parameters
        :return: Dictionary with paginated stats and metadata
        """
        try:
            # Validate and process user_id
            if not user_id:
                raise ValueError("User ID is required")

            # Handle start_year with robust validation
            start_year_raw = filter.get("start_year")
            current_year = datetime.datetime.now().year

            if start_year_raw is None or start_year_raw == "":
                start_year = current_year
            else:
                try:
                    start_year = int(start_year_raw)
                    if start_year < 2020 or start_year > current_year:
                        raise ValueError(f"Invalid start year: {start_year}")
                except ValueError:
                    start_year = current_year

            # Handle end_year with fallback to start_year
            end_year_raw = filter.get("end_year")
            if end_year_raw is None or end_year_raw == "":
                end_year = start_year
            else:
                try:
                    end_year = int(end_year_raw)
                    if end_year < start_year or end_year > current_year:
                        raise ValueError(f"Invalid end year: {end_year}")
                except ValueError:
                    end_year = start_year

            # Handle week with robust processing
            week_raw = filter.get("week")
            try:
                week = int(week_raw) if week_raw is not None and week_raw != "" else 0
                if week < 0 or week > 53:
                    raise ValueError(f"Invalid week: {week}")
            except ValueError:
                week = 0

            # Handle month with robust processing
            month_raw = filter.get("month")
            try:
                month = (
                    int(month_raw) if month_raw is not None and month_raw != "" else 0
                )
                if month < 0 or month > 12:
                    raise ValueError(f"Invalid month: {month}")
            except ValueError:
                month = 0

            # Ensure only one time filter is used
            if sum(bool(x) for x in [week, month]) > 1:
                raise ValueError("Only one of week or month can be specified")

            # Handle pagination
            page_raw = filter.get("page")
            try:
                page = (
                    int(page_raw) if page_raw is not None and page_raw != "" else None
                )
                page = max(1, page) if page else None
            except ValueError:
                page = None

            # Prepare match stage for user_id
            match_stage = {"$match": {"user_id": ObjectId(user_id)}}

            # Prepare date filtering stages
            if week:
                # If specific week is provided
                start_date, end_date = self.get_week_range(start_year, week)
                start_date = datetime.datetime.combine(start_date, datetime.time.min)
                end_date = datetime.datetime.combine(end_date, datetime.time.max)
            elif month:
                # If specific month is provided
                start_date = datetime.datetime(
                    start_year, month, 1, tzinfo=datetime.timezone.utc
                )
                end_date = (start_date + datetime.timedelta(days=32)).replace(
                    day=1
                ) - datetime.timedelta(seconds=1)
            else:
                # If no specific month or week, filter by full year range
                start_date = datetime.datetime(
                    start_year, 1, 1, tzinfo=datetime.timezone.utc
                )
                end_date = datetime.datetime(
                    end_year, 12, 31, 23, 59, 59, tzinfo=datetime.timezone.utc
                )

            # Date range filter stage
            date_filter_stages = [
                {"$match": {"created_at": {"$gte": start_date, "$lte": end_date}}}
            ]

            pipeline = [
                match_stage,
                {"$addFields": {"created_at": {"$toDate": "$created_at"}}},
                *date_filter_stages,
                {
                    "$group": {
                        "_id": {
                            "year": {"$year": "$created_at"},
                            **(
                                {
                                    "month": {"$month": "$created_at"},
                                    "day": {"$dayOfMonth": "$created_at"},
                                }
                                if month
                                else {"month": {"$month": "$created_at"}}
                            ),
                        },
                        "total_credits_used": {"$sum": "$credits_used"},
                        "total_seconds_used": {"$sum": "$seconds_used"},
                        "usage_count": {"$sum": 1},
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "year": "$_id.year",
                        "month": "$_id.month",
                        "day": "$_id.day",
                        "credits_used": "$total_credits_used",
                        "seconds_used": "$total_seconds_used",
                        "usage_count": "$usage_count",
                    }
                },
                {
                    "$sort": {
                        "_id.year": 1,
                        "_id.month": 1,
                        "_id.day": 1 if month else 1,
                    }
                },
            ]

            # Execute aggregation with comprehensive error handling
            try:
                token_stats = list(self.collection.aggregate(pipeline))
            except Exception as agg_error:
                print(f"Aggregation Error: {agg_error}")
                print(f"Pipeline Details: {pipeline}")
                raise

            # Transform the stats
            result = self.transform_stats(
                token_stats, start_year, end_year, month, week, page
            )

            return {
                **result,
            }

        except Exception as e:
            print(f"Comprehensive Error in get_stats: {str(e)}")
            import traceback

            traceback.print_exc()
            raise

    def get_counts(self, user_id):

        match_criteria = {}
        if user_id:
            match_criteria["user_id"] = ObjectId(user_id)

        # Aggregation for total token counts
        token_counts = self.collection.aggregate(
            [
                {"$match": match_criteria},
                {
                    "$group": {
                        "_id": "$api_type",
                        "credits_used": {"$sum": "$credits_used"},
                        "seconds_used": {"$sum": "$seconds_used"},
                    }
                },
            ]
        )

        # Transform results
        token_counts_by_type = [
            {
                "api_type": count["_id"],
                "credits_used": count["credits_used"],
                "seconds_used": count["seconds_used"],
            }
            for count in token_counts
        ]

        # Calculate total tokens
        total_credits_used = sum(
            count["credits_used"] for count in token_counts_by_type
        )
        total_seconds_used = sum(
            count["seconds_used"] for count in token_counts_by_type
        )

        return {
            "token_counts_by_type": token_counts_by_type,
            "total_credits_used": total_credits_used,
            "total_seconds_used": total_seconds_used,
        }

    def get_usages(self, filter={}) -> list:
        """
        Retrieve all usage documents for a specific user.
        """

        usages = list(self.collection.find(filter))
        return usages

    def update_usage(self, usage_id: str, update_data: dict) -> dict:
        """
        Update a usage document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(usage_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_usage_by_id(usage_id)
        return None

    def delete_usage(self, usage_id: str) -> bool:
        """
        Delete a usage document by its ID.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(usage_id)},
            {"$set": {"status": Status.INACTIVATE.value}},
        )
        return result.modified_count > 0

    def update_usage_status(self, usage_id: str, new_status: str) -> dict:
        """
        Update the status of a usage document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(usage_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_usage_by_id(usage_id)
        return None

    def convert_single_document_to_dict(self, usage_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in usage_data.items()}

    def to_dict(self, usage_data):
        if isinstance(usage_data, dict):
            return self.convert_single_document_to_dict(usage_data)
        elif isinstance(usage_data, list):
            return [self.convert_single_document_to_dict(item) for item in usage_data]
        else:
            return usage_data
