from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime

from app.constant.common_enum import Status


class SubscriptionPlanRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.SUBSCRIPTION_PLAN_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_subscription_plan(self, subscription_plan_data) -> dict:
        """
        Create a new subscription_plan document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        subscription_plan_data["created_at"] = current_time
        subscription_plan_data["updated_at"] = current_time
        subscription_plan_data["status"] = Status.ACTIVE.value
        result = self.collection.insert_one(subscription_plan_data)
        return self.get_subscription_plan_by_id(str(result.inserted_id))

    def get_subscription_plan_by_id(self, subscription_plan_id: str) -> dict:
        """
        Retrieve a subscription_plan document by its ID.
        """
        try:
            subscription_plan = self.collection.find_one(
                {"_id": ObjectId(subscription_plan_id)}
            )
            return subscription_plan

        except Exception as e:
            print("Could not find subscription_plan with ID ", str(e))
            return None

    def get_free_subscription_plan(self) -> dict:
        """
        Retrieve a subscription_plan document by its ID.
        """
        try:
            subscription_plan = self.collection.find_one(
                {"price": 0, "status": "active"}
            )
            return subscription_plan
        except Exception as e:
            print("Could not find subscription_plan with ID ", str(e))
            return None

    def get_subscription_plan_by_stripe_product_id(
        self, stripe_product_id: str
    ) -> dict:
        """
        Retrieve a subscription_plan document by its ID.
        """
        try:
            subscription_plan = self.collection.find_one(
                {"stripe_product_id": stripe_product_id}
            )
            return subscription_plan

        except Exception as e:
            print("Could not find subscription_plan with ID ", str(e))
            return None

    def get_subscription_plans_by_user(self, user_id: str) -> list:
        """
        Retrieve all subscription_plan documents for a specific user.
        """
        subscription_plans = list(self.collection.find({"user_id": ObjectId(user_id)}))
        return subscription_plans

    def get_subscription_plans(
        self,
        interval=None,
        active=True,
    ) -> list:
        """
        Retrieve all subscription_plan documents for a specific user.
        """

        if interval is not None:
            filter = {
                "$or": [
                    {"interval": interval},
                    {"price": 0},
                ],
            }
        else:
            filter = {"status": Status.ACTIVE.value}

        if active is not None:
            filter["active"] = active

        subscription_plans = list(self.collection.find(filter))
        return subscription_plans

    def update_subscription_plan(
        self, subscription_plan_id: str, update_data: dict
    ) -> dict:
        """
        Update a subscription_plan document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(subscription_plan_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_subscription_plan_by_id(subscription_plan_id)
        return None

    def delete_subscription_plan(self, subscription_plan_id: str) -> bool:
        """
        Delete a subscription_plan document by its ID.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(subscription_plan_id)},
            {"$set": {"status": Status.INACTIVATE.value}},
        )
        return result.modified_count > 0

    def update_subscription_plan_status(
        self, subscription_plan_id: str, new_status: str
    ) -> dict:
        """
        Update the status of a subscription_plan document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(subscription_plan_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_subscription_plan_by_id(subscription_plan_id)
        return None

    def convert_single_document_to_dict(self, subscription_plan_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in subscription_plan_data.items()}

    def to_dict(self, subscription_plan_data):
        if isinstance(subscription_plan_data, dict):
            return self.convert_single_document_to_dict(subscription_plan_data)
        elif isinstance(subscription_plan_data, list):
            return [
                self.convert_single_document_to_dict(item)
                for item in subscription_plan_data
            ]
        else:
            return subscription_plan_data
