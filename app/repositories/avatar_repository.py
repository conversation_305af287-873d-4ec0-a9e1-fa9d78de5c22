from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime
from pymongo import ASCENDING


class AvatarRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.AVATAR_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_avatar(self, avatar_data) -> dict:
        """
        Create a new avatar document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        avatar_data["created_at"] = current_time
        avatar_data["updated_at"] = current_time
        result = self.collection.insert_one(avatar_data)

        return self.get_avatar_by_id(str(result.inserted_id))

    def get_avatar_by_id(self, avatar_id: str) -> dict:
        """
        Retrieve a avatar document by its ID.
        """
        try:
            avatar = self.collection.find_one({"_id": ObjectId(avatar_id)})
            return avatar

        except Exception as e:
            print("Could not find avatar with ID ", str(e))
            return None

    def get_avatar_by_avatar_id(self, avatar_id: str) -> dict:
        """
        Retrieve a avatar document by its ID.
        """
        try:
            avatar = self.collection.find_one({"avatar_id": avatar_id})
            return avatar

        except Exception as e:
            print("Could not find avatar with ID ", str(e))
            return None

    def get_default_avatar(self) -> dict:
        """
        Retrieve a avatar document by its ID.
        """
        try:
            avatar = self.collection.find_one({"is_cloned": False})
            return avatar
        except Exception as e:
            print("Could not find default avatar", str(e))
            return None

    def get_avatar_of_user_by_id(self, avatar_id: str, user_id: str) -> dict:
        """
        Retrieve a avatar document by its ID.
        """
        avatar = self.collection.find_one(
            {"_id": ObjectId(avatar_id), user_id: ObjectId(user_id)}
        )
        return avatar

    def get_avatars_by_user(self, user_id: str) -> list:
        """
        Retrieve all avatar documents for a specific user.
        """
        avatars = list(self.collection.find({"user_id": ObjectId(user_id)}))

        return avatars

    def update_avatar(self, avatar_id: str, update_data: dict) -> dict:
        """
        Update a avatar document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(avatar_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.collection.find_one({"avatar_id": avatar_id})
        return None

    def update_avatar_by_avatar_id(self, avatar_id: str, update_data: dict) -> dict:
        """
        Update a avatar document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"avatar_id": avatar_id}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.collection.find_one({"avatar_id": avatar_id})
        return None

    def delete_avatar(self, user_id: str, avatar_id: str) -> bool:
        """
        Delete a avatar document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(avatar_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_avatars_by_avatar(self, avatar_id: str) -> list:
        """
        Retrieve all avatar documents associated with a specific avatar.
        """
        avatars = list(self.collection.find({"avatar_id": ObjectId(avatar_id)}))
        for avatar in avatars:
            avatar["_id"] = str(avatar["_id"])
            avatar["user_id"] = str(avatar["user_id"])
            avatar["avatar_id"] = str(avatar["avatar_id"])
        return avatars

    def update_avatar_status(self, avatar_id: str, new_status: str) -> dict:
        """
        Update the status of a avatar document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(avatar_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_avatar_by_id(avatar_id)
        return None

    def get_avatar_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of avatars in the collection.

        :param filter_criteria: Optional dictionary to filter the avatars before counting
        :return: The count of avatars matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def convert_single_document_to_dict(self, avatar_data):
        return {
            "_id": str(avatar_data.get("_id", "")),
            "user_id": str(avatar_data.get("user_id", "")),
            "avatar_id": avatar_data.get("avatar_id", ""),
            "link": avatar_data.get("link", ""),
            "name": avatar_data.get("name", ""),
            "status": avatar_data.get("status", ""),
            "is_cloned": avatar_data.get("is_cloned", ""),
            "gender": avatar_data.get("gender", ""),
            "model_name": avatar_data.get("model_name", ""),
            "replica_type": avatar_data.get("replica_type", ""),
            "created_at": avatar_data.get("created_at", None),
            "updated_at": avatar_data.get("updated_at", None),
            "thumbnail_image": avatar_data.get("thumbnail_image", None),
        }

    def get_avatars_paginated(
        self,
        user_id,
        page: int = 1,
        per_page: int = 10,
        is_cloned: bool = False,
    ) -> dict:
        """
        Get avatars with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Assuming `user_id` is a variable holding the value you want to check against
        filter_criteria = {
            "status": "completed",
            "$or": [
                {"user_id": None},  # Matches documents where user_id is null
                {
                    "user_id": ObjectId(user_id)
                },  # Matches documents where user_id is equal to the specific user_id
            ],
        }

        if is_cloned is not None:
            filter_criteria["is_cloned"] = is_cloned
        else:
            filter_criteria["is_cloned"] = False

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        avatars = list(self.collection.find(filter_criteria).skip(skip).limit(per_page))

        # Convert ObjectId to string for each avatar
        avatars = [self.to_dict(avatar) for avatar in avatars]

        return {
            "avatars": avatars,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, avatar_data):

        if isinstance(avatar_data, dict):

            avatar_data = self.convert_single_document_to_dict(avatar_data)

        if isinstance(avatar_data, list):

            for index, avatar in enumerate(avatar_data):
                avatar = self.convert_single_document_to_dict(avatar)
                avatar_data[index] = avatar

        return avatar_data
