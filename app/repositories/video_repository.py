from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class VideoRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.VIDEO_COLLECTION]

    def create_video(self, video_data) -> dict:
        """
        Create a new video document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        video_data["created_at"] = current_time
        video_data["updated_at"] = current_time
        result = self.collection.insert_one(video_data)
        return self.get_video_by_id(str(result.inserted_id))

    def get_video_by_id(self, video_id: str) -> dict:
        """
        Retrieve a video document by its ID.
        """
        try:
            video = self.collection.find_one({"_id": ObjectId(video_id)})

            return video
        except Exception as e:
            print("Could not find video with _id ", str(e))
            return None

    def get_video_by_user_id(self, user_id: str, video_id: str) -> dict:
        """
        Retrieve a video document by its ID.
        """
        try:
            video = self.collection.find_one(
                {"_id": ObjectId(video_id), "user_id": ObjectId(user_id)}
            )

            return video
        except Exception as e:
            print("Could not find video with user_id ", str(e))
            return None

    def get_video_details(self, video_id: str) -> dict:
        """
        Retrieve a video document by its ID.
        """
        try:

            # Build the pipeline
            pipeline = [
                {
                    "$match": {
                        "_id": ObjectId(video_id),  # Use _id instead of video_id
                        "is_community_video": True,
                    }
                },
                {
                    "$lookup": {
                        "from": collection.USER_COLLECTION,  # Assuming your user collection is named "users"
                        "localField": "user_id",
                        "foreignField": "_id",
                        "as": "user",
                    }
                },
                {"$unwind": {"path": "$user", "preserveNullAndEmptyArrays": True}},
                {
                    "$project": {
                        "_id": {"$toString": "$_id"},
                        "title": 1,
                        "link": 1,
                        "thumbnail_link": 1,
                        "created_at": 1,
                        "updated_at": 1,
                        "status": 1,
                        "is_community_video": 1,
                        "view_type": 1,
                        "metadata": 1,
                        "user": {
                            "_id": {"$toString": "$user._id"},
                            "name": "$user.name",
                            "email": "$user.email",
                            "profile_picture": "$user.profile_picture",
                        },
                    }
                },
            ]

            # Execute the pipeline
            videos = list(self.collection.aggregate(pipeline))

            return videos[0] if len(videos) != 0 else None
        except Exception as e:
            print("Could not find video with user_id ", str(e))
            return None

    def get_videos_by_user(self, user_id: str) -> list:
        """
        Retrieve all video documents for a specific user.
        """
        try:
            videos = list(self.collection.find({"user_id": ObjectId(user_id)}))
            return videos
        except Exception as e:
            print("Could not find video with user_id ", str(e))
            return []

    def update_video(self, video_id: str, update_data: dict) -> dict:
        """
        Update a video document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(video_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_video_by_id(video_id)
        return None

    def soft_delete_video(self, video_id: str) -> bool:
        """
        Delete a video document by its ID.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(video_id)}, {"$set": {"is_deleted": True}}
        )
        return result.modified_count > 0

    def delete_video(self, video_id: str) -> bool:
        """
        Delete a video document by its ID.
        """
        result = self.collection.delete_one({"_id": ObjectId(video_id)})
        return result.deleted_count > 0

    def get_videos_by_script(self, script_id: str) -> list:
        """
        Retrieve all video documents associated with a specific script.
        """
        videos = list(self.collection.find({"script_id": ObjectId(script_id)}))

        return videos

    def update_video_status(self, video_id: str, new_status: str) -> dict:
        """
        Update the status of a video document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(video_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_video_by_id(video_id)
        return None

    def update_video_status_by_task_id(self, task_id: str, new_status: str):
        """
        Update the status of a video document.
        """

        print(task_id, new_status)

        result = self.collection.update_one(
            {task_id: task_id}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_video({"task_id": task_id})
        return None

    def find_videos_by_user_id(
        self,
        user_id: str,
        page: int = 1,
        per_page: int = 10,
        status: bool = None,
        view_type: str = None,
        title: str = None,
    ) -> dict:
        """
        Get videos with pagination and optional filters on status, view_type, and title.

        :param user_id: The ID of the user
        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param status: Optional filter for status (True or False)
        :param view_type: Optional filter for view type
        :param title: Optional filter for title (case-insensitive partial match)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {
            "user_id": ObjectId(user_id),
            "is_deleted": {"$ne": True},
        }

        if status is not None:
            filter_criteria["status"] = status

        if view_type is not None:
            filter_criteria["view_type"] = view_type

        if title is not None:
            filter_criteria["title"] = {"$regex": title, "$options": "i"}

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        videos = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each video
        videos = [self.to_public_dict(video) for video in videos]

        return {
            "videos": videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def find_community_videos(
        self,
        page: int = 1,
        per_page: int = 10,
        view_type: str = None,
    ) -> dict:
        """
        Get community videos with pagination and optional filters, using an aggregation pipeline to populate user data.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param view_type: Optional filter for view type
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the match stage
        match_stage = {
            "$match": {
                "status": "COMPLETED",
                "is_deleted": {"$ne": True},
                "is_community_video": True,
            }
        }
        if view_type is not None:
            match_stage["$match"]["view_type"] = view_type

        # Build the pipeline
        pipeline = [
            match_stage,
            {"$sort": {"created_at": -1}},
            {"$skip": skip},
            {"$limit": per_page},
            {
                "$lookup": {
                    "from": collection.USER_COLLECTION,  # Assuming your user collection is named "users"
                    "localField": "user_id",
                    "foreignField": "_id",
                    "as": "user",
                }
            },
            {"$unwind": {"path": "$user", "preserveNullAndEmptyArrays": True}},
            {
                "$project": {
                    "_id": {"$toString": "$_id"},
                    "title": 1,
                    "link": 1,
                    "thumbnail_link": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "status": 1,
                    "is_community_video": 1,
                    "view_type": 1,
                    "user": {
                        "_id": {"$toString": "$user._id"},
                        "name": "$user.name",
                        "email": "$user.email",
                        "profile_picture": "$user.profile_picture",
                    },
                }
            },
        ]

        # Execute the pipeline
        videos = list(self.collection.aggregate(pipeline))

        # Get the total count of matching documents
        total_count = self.collection.count_documents(match_stage["$match"])

        return {
            "videos": videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_public_dict(self, video_data):
        return {
            "_id": str(video_data.get("_id", "")),
            "user_id": str(video_data.get("user_id", "")),
            "voice_id": str(video_data.get("voice_id", None)),
            "avatar_id": str(video_data.get("avatar_id", None)),
            "script_id": str(video_data.get("script_id", "")),
            "status": video_data.get("status", ""),
            "link": video_data.get("link", ""),
            "title": video_data.get("title", None),
            "avatar_video_ids": video_data.get("avatar_video_ids", None),
            "avatar_video_urls": video_data.get("avatar_video_urls", None),
            "created_at": video_data.get("created_at", None),
            "updated_at": video_data.get("updated_at", None),
            "template_id": video_data.get("template_id", None),
            "status_message": video_data.get("status_message", None),
            "view_type": video_data.get("view_type", None),
            "metadata": video_data.get("metadata", None),
            "link_1080p": video_data.get("link_1080p", None),
            "thumbnail_link": video_data.get("thumbnail_link", None),
            "is_community_video": video_data.get("is_community_video", None),
            "duration": video_data.get("duration", None),
        }

    def to_public_dict(self, video_data):

        if isinstance(video_data, dict):

            video_data = self.convert_single_document_to_public_dict(video_data)

        if isinstance(video_data, list):

            for index, voice in enumerate(video_data):
                voice = self.convert_single_document_to_public_dict(voice)
                video_data[index] = voice

        return video_data

    def convert_single_document_to_dict(self, video_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in video_data.items()}

    def to_dict(self, video_data):
        if isinstance(video_data, dict):
            return self.convert_single_document_to_dict(video_data)
        elif isinstance(video_data, list):
            return [self.convert_single_document_to_dict(item) for item in video_data]
        else:
            return video_data
