from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class MediaRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.MEDIA_COLLECTION]

    def create_media(self, media_data) -> dict:
        """
        Create a new media document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        media_data["created_at"] = current_time
        media_data["updated_at"] = current_time
        result = self.collection.insert_one(media_data)

        return self.get_media_by_id(str(result.inserted_id))

    def get_media_by_id(self, media_id: str) -> dict:
        """
        Retrieve a media document by its ID.
        """
        media = self.collection.find_one({"_id": ObjectId(media_id)})
        return media

    def get_medias_by_user(self, user_id: str, media_id: str) -> list:
        """
        Retrieve all media documents for a specific user.
        """
        medias = list(
            self.collection.find(
                {"user_id": ObjectId(user_id), "_id": ObjectId(media_id)}
            )
        )

        return medias

    def update_media(self, user_id: str, media_id: str, update_data: dict) -> dict:
        """
        Update a media document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(media_id), "user_id": ObjectId(user_id)},
            {"$set": update_data},
        )

        if result.modified_count > 0:
            return self.get_media_by_id(media_id)
        return None

    def delete_media(self, user_id: str, media_id: str) -> bool:
        """
        Delete a media document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(media_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_medias_by_media(self, media_id: str) -> list:
        """
        Retrieve all media documents associated with a specific media.
        """
        medias = list(self.collection.find({"media_id": ObjectId(media_id)}))
        for media in medias:
            media["_id"] = str(media["_id"])
            media["user_id"] = str(media["user_id"])
            media["media_id"] = str(media["media_id"])
        return medias

    def update_media_status(self, media_id: str, new_status: str) -> dict:
        """
        Update the status of a media document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(media_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_media_by_id(media_id)
        return None

    def create_media_list(self, media_list: list) -> list:
        """
        Create a list of new medias.
        """
        result = []
        for media in media_list:
            created_media = self.create_media(media)
            result.append(self.to_dict(created_media))
        return result

    def get_media_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of medias in the collection.

        :param filter_criteria: Optional dictionary to filter the medias before counting
        :return: The count of medias matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_medias_paginated(
        self,
        user_id: str,
        page: int = 1,
        per_page: int = 10,
        media_type: str = None,
        category=None,
        folder=None,
    ) -> dict:
        """
        Get medias with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {}

        if user_id:
            filter_criteria["user_id"] = ObjectId(user_id)
        if media_type is not None:
            filter_criteria["type"] = media_type
        if category:
            filter_criteria["category"] = category
        if folder:
            filter_criteria["folder"] = folder

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        medias = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each media
        medias = [self.to_dict(media) for media in medias]

        return {
            "medias": medias,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, media_data):
        return {
            "_id": str(media_data.get("_id", "")),
            "user_id": str(media_data.get("user_id", "")),
            "title": media_data.get("title", ""),
            "created_at": media_data.get("created_at", None),
            "updated_at": media_data.get("updated_at", None),
            "description": media_data.get("description", None),
            "type": media_data.get("type", None),
            "link": media_data.get("link", None),
            "mime_type": media_data.get("mime_type", None),
            "resolution": media_data.get("resolution", None),
            "duration": media_data.get("duration", None),
            "thumbnail": media_data.get("thumbnail", None),
            "category": media_data.get("category", None),
            "folder": media_data.get("folder", None),
        }

    def get_medias_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        medias = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "medias": medias,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, media_data):

        if isinstance(media_data, dict):

            media_data = self.convert_single_document_to_dict(media_data)

        if isinstance(media_data, list):

            for index, media in enumerate(media_data):
                media = self.convert_single_document_to_dict(media)
                media_data[index] = media

        return media_data
