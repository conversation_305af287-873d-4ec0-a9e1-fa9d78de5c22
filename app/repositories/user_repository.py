import datetime

from bson import ObjectId
from werkzeug.security import generate_password_hash

from app.constant.user_enums import User<PERSON><PERSON>
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from app.exceptions.auth_exceptions import (
    UserCreationError,
    UserListFetchError,
    UserNameExistError,
    UserNotFoundError,
)

from app.constant.constant import DEFAULT_PROFILE


class AuthRepository:
    """
    Repository class for handling authentication-related database operations.
    Provides methods to create a user, find users, check user existence, and update user password.
    """

    def __init__(self):
        """
        Initializes the AuthRepository with a database connection.
        """

        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.USER_COLLECTION]

    def create_admin(self, email: str, password: str, name: str | None = None) -> str:
        """
        Creates a new admin in the database.

        Parameters:
        - email (str): The email ID of the new admin.
        - password (str): The plaintext password for the new admin.
        - name (str): The name of the admin.

        Returns:
        - str: The unique identifier (_id) of the created user as a string.

        Raises:
        - UserCreationError: If there is a failure during user creation.
        """

        try:
            current_time = datetime.datetime.utcnow().isoformat()
            password_hash = generate_password_hash(
                password
            )  # Hash the plaintext password
            result = self.collection.insert_one(
                {
                    "email": email,
                    "password": password_hash,
                    "name": name,
                    "created_at": current_time,
                    "updated_at": current_time,
                    "role": UserRole.ADMIN.value,
                }
            )

            return str(result.inserted_id)

        except Exception as e:
            raise UserCreationError(f"Failed to create user: {e}")

    def update_or_create_user(self, user_data):

        user_data["last_login"] = datetime.datetime.utcnow().isoformat()
        user_data["updated_at"] = datetime.datetime.utcnow().isoformat()

        self.collection.update_one(
            {"email": user_data["email"]}, {"$set": user_data}, upsert=True
        )

    def update_one(self, query, update):
        # update["updated_at"] = datetime.datetime.utcnow().isoformat()
        return self.collection.update_one(query, update)

    def get_by_email(self, email):
        try:
            return self.collection.find_one({"email": email})
        except Exception as e:
            print("get_by_email", str(e))
            return None

    def find_user_by_stripe_customer_id(self, customer_id):
        try:
            return self.collection.find_one({"customer_id": customer_id})
        except Exception as e:
            print("find_user_by_stripe_customer_id", str(e))
            return None

    def create_user(self, user_data) -> str:
        """
        Creates a new user in the database.

        Parameters:
        - email (str): The email ID of the new user.
        - password (str): The plaintext password for the new user.
        - name (str): The name of the user.

        Returns:
        - str: The unique identifier (_id) of the created user as a string.

        Raises:
        - UserCreationError: If there is a failure during user creation.
        """

        try:
            current_time = datetime.datetime.utcnow().isoformat()
            user_data["created_at"] = current_time
            user_data["updated_at"] = current_time

            if not user_data.get("profile_picture"):
                user_data["profile_picture"] = DEFAULT_PROFILE

            result = self.collection.insert_one(user_data)
            return str(result.inserted_id)

        except Exception as e:
            raise UserCreationError(f"Failed to create user: {e}")

    def update_user(self, user_id: str, **fields) -> None:
        """
        Updates the specified fields for an existing user identified by their email ID.

        Parameters:
        - user_id (str): The user ID of the user to update.
        - fields (dict): A dictionary of fields to update.

        Raises:
        - UserNotFoundError: If no user is found with the provided email ID.
        - UserUpdateError: If there is a failure during the user update.
        """

        # Prepare the update document
        update_doc = {"$set": {}}
        for field, value in fields.items():
            if (
                field == "password"
            ):  # Ensure the password is hashed if it's being updated
                value = generate_password_hash(value)
            update_doc["$set"][field] = value

        current_time = datetime.datetime.utcnow().isoformat()
        update_doc["$set"]["updated_at"] = current_time  # Update the 'updated_at' field

        # Execute the update
        result = self.collection.update_one({"_id": ObjectId(user_id)}, update_doc)

        if result.matched_count == 0:
            raise UserNotFoundError(description=f"User with ID {user_id} not found.")

    def update_user_by_email(self, email: str, update_data: dict) -> None:
        """
        Updates the specified fields for an existing user identified by their email ID.

        Parameters:
        - user_id (str): The user ID of the user to update.
        - fields (dict): A dictionary of fields to update.

        Raises:
        - UserNotFoundError: If no user is found with the provided email ID.
        - UserUpdateError: If there is a failure during the user update.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        # Execute the update
        result = self.collection.update_one({"email": email}, {"$set": update_data})

        if result.matched_count == 0:
            raise UserNotFoundError(description=f"User with ID {email} not found.")

    def find_user_without_password(
        self, identifier: str, search_by: str = "id"
    ) -> dict:
        """
        Retrieves a user from the database by their unique identifier or email without including the password in the result.

        Parameters:
        - identifier (str): The unique identifier or email of the user to retrieve.
        - search_by (str): Determines whether to search by 'id' or 'email'. Defaults to 'id'.

        Returns:
        - dict: The found user document without the password field.

        Raises:
        - UserNotFoundError: If no user is found with the provided identifier.
        - ValueError: If the search_by parameter is not 'id' or 'email'.
        """

        if search_by not in ["id", "email"]:
            raise ValueError("search_by parameter must be 'id' or 'email'.")

        search_field = "_id" if search_by == "id" else "email"
        query = {
            search_field: ObjectId(identifier) if search_by == "id" else identifier
        }

        user = self.collection.find_one(query, {"password": 0})
        if not user:
            raise UserNotFoundError(
                description=f"User with {search_by} {identifier} not found."
            )

        return user

    def find_user_by_id(self, user_id: str):
        """
        Retrieves a user from the database by their unique identifier.

        Parameters:
        - user_id (str): The unique identifier of the user to retrieve.

        Returns:
        - dict: The found user document.

        Raises:
        - UserNotFoundError: If no user is found with the provided ID.
        """

        user = self.collection.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise UserNotFoundError(description=f"User with ID {user_id} not found.")

        return user

    def get_user_details(self, user_id: str):
        """
        Retrieves a user from the database by their unique identifier.

        Parameters:
        - user_id (str): The unique identifier of the user to retrieve.

        Returns:
        - dict: The found user document.

        Raises:
        - UserNotFoundError: If no user is found with the provided ID.
        """

        pipeline = [
            # Match the user settings for the given user_id
            {"$match": {"_id": ObjectId(user_id)}},
            # Lookup user information
            {
                "$lookup": {
                    "from": collection.USER_SETTING_COLLECTION,
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_setting",
                }
            },
            # Unwind the user_setting array, preserving null and empty arrays
            {"$unwind": {"path": "$user_setting", "preserveNullAndEmptyArrays": True}},
            {
                "$lookup": {
                    "from": collection.SUBSCRIPTION_COLLECTION,
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "subscription",
                }
            },
            # Unwind the subscription array, preserving null and empty arrays
            {"$unwind": {"path": "$subscription", "preserveNullAndEmptyArrays": True}},
        ]

        result = list(self.collection.aggregate(pipeline))

        if result:
            # Convert ObjectId to string for JSON serialization
            populated_user = self.to_dict(result[0])
            return populated_user
        return None

    def get_populated_user_details(self, user_id: str) -> dict:
        """
        Retrieve user subscription with populated plan information.
        """
        pipeline = [
            # Match the user settings for the given user_id
            {"$match": {"_id": ObjectId(user_id)}},
            # Lookup user information
            {
                "$lookup": {
                    "from": collection.USER_SETTING_COLLECTION,
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_setting",
                }
            },
            # Unwind the user_setting array, preserving null and empty arrays
            {"$unwind": {"path": "$user_setting", "preserveNullAndEmptyArrays": True}},
            # Lookup user information
            {
                "$lookup": {
                    "from": collection.SUBSCRIPTION_COLLECTION,
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "subscription",
                }
            },
            # Unwind the user_setting array, preserving null and empty arrays
            {"$unwind": {"path": "$subscription", "preserveNullAndEmptyArrays": True}},
        ]

        result = list(self.collection.aggregate(pipeline))

        if result:
            # Convert ObjectId to string for JSON serialization
            populated_subscription = self.to_dict(result[0])
            return populated_subscription
        return None

    def find_by_name(self, name: str):
        """
        Retrieves a user from the database by their unique identifier.

        Parameters:
        - name (str): The name of user.

        Returns:
        - dict: The found user document.

        Raises:
        - UserNameExistError: If no user is found with the provided ID.
        """

        user = self.collection.find_one({"name": name})
        if user:
            raise UserNameExistError(
                description=f"This user name {name} already present."
            )

        return user

    def find_user_by_email(self, email: str):
        """
        Retrieves a user from the database by their email ID.

        Parameters:
        - email (str): The email ID of the user to retrieve.

        Returns:
        - dict: The found user document.

        Raises:
        - UserNotFoundError: If no user is found with the provided email ID.
        """
        try:
            user = self.collection.find_one({"email": email})
            return user
        except Exception as e:
            print("Could not find user with email ", str(e))
            return None

    def user_already_exists(self, user_id: str) -> bool:
        """
        Checks if a user already exists in the database with the given user ID.

        Parameters:
        - user_id (str): The user ID to check in the database.

        Returns:
        - bool: True if a user with the given user ID exists, False otherwise.
        """

        user = self.collection.find_one({"_id": ObjectId(user_id)})

        return bool(user)

    def email_already_exists(self, email: str):
        """
        Checks if a user already exists in the database with the given email ID.

        Parameters:
        - email (str): The email ID to check in the database.

        Returns:
        - bool: True if a user with the given email ID exists, False otherwise.
        """

        user = self.collection.find_one({"email": email})

        return user

    def update_user_password(self, user_id: str, new_password: str) -> None:
        """
        Updates the password for an existing user.

        Parameters:
        - user_id (str): The unique identifier of the user whose password is to be updated.
        - new_password (str): The new plaintext password for the user.

        Raises:
        - UserNotFoundError: If no user is found with the provided ID.
        - UserUpdateError: If there is a failure during the password update.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        result = self.collection.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": {"password": new_password, "updated_at": current_time}},
        )

        if result.matched_count == 0:
            raise UserNotFoundError(f"User with ID {user_id} not found.")

    def fetch_users(
        self, q: str, filters: dict, offset: int = 0, limit: int = 10
    ) -> tuple[list, int]:
        """
        Fetches a list of users from the database with specified filters and pagination.

        Parameters:
        - q (str): Query string to search for users with name and email
        - filters (dict): A dictionary containing filter criteria such as username, status, and days.
        - offset (int): The offset from where to start fetching the users.
        - limit (int): The maximum number of users to return.

        Returns:
        - list: A list of user documents matching the filter criteria.
        - int: The total count of users matching the filter criteria (ignoring pagination).

        Raises:
        - Exception: If there is an error during database operation.
        """

        try:
            query = {}
            if q is not None:
                query["$or"] = [
                    {"name": {"$regex": q, "$options": "i"}},
                    {"email": {"$regex": q, "$options": "i"}},
                ]
            # Filter by user status
            if "status" in filters and filters["status"]:
                query["status"] = filters["status"].upper()

            # Search by name using regex
            if "uname" in filters and filters["uname"]:
                query["name"] = {"$regex": filters["uname"], "$options": "i"}

            if "role" in filters and filters["role"]:
                query["role"] = (
                    UserRole.ADMIN.value
                    if filters["role"] == UserRole.ADMIN.value
                    else UserRole.USER.value
                )

            # Filter by number of days
            if "days" in filters and filters["days"]:
                days_ago = (
                    datetime.datetime.utcnow()
                    - datetime.timedelta(days=filters["days"])
                ).isoformat()

                query["$or"] = [
                    {"created_at": {"$gte": days_ago}},
                    {"updated_at": {"$gte": days_ago}},
                ]

            users_cursor = (
                self.collection.find(query)
                .skip(offset)
                .limit(limit)
                .sort("created_at", -1)
            )

            users = list(users_cursor)
            total_count = self.collection.count_documents(query)

            return users, total_count

        except Exception as e:
            raise UserListFetchError(str(e))

    def fetch_user_count(self, query: dict) -> int:
        """
        Fetches the total count of users in the database.

        Returns:
        - int: The total count of users in the database.
        """
        conditions = {
            **query,
            "role": UserRole.USER.value,
            # "is_deleted": {"$exists": False},
        }

        return self.collection.count_documents(conditions)

    def convert_single_document_to_dict(self, user_data):
        # return {
        #     "_id": str(user_data.get("_id", "")),
        #     "google_id": user_data.get("google_id", ""),
        #     "name": user_data.get("name", ""),
        #     "email": user_data.get("email", ""),
        #     "profile_picture": user_data.get("profile_picture", ""),
        #     "verified_email": user_data.get("verified_email", False),
        #     "status": user_data.get("status", ""),
        #     "created_at": user_data.get("created_at", None),
        #     "updated_at": user_data.get("updated_at", None),
        #     "role": user_data.get("role", ""),
        #     "login_method": user_data.get("login_method", None),
        #     "youtube_credentials": user_data.get("youtube_credentials", None),
        # }

        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in user_data.items()}

    def to_dict(self, user_setting_data):
        if isinstance(user_setting_data, dict):
            return self.convert_single_document_to_dict(user_setting_data)
        elif isinstance(user_setting_data, list):
            return [
                self.convert_single_document_to_dict(item) for item in user_setting_data
            ]
        else:
            return user_setting_data
