from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class TrendingRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.TRENDING_COLLECTION]

    def create_trending(self, trending_data) -> dict:
        """
        Create a new trending document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        trending_data["created_at"] = current_time
        trending_data["updated_at"] = current_time
        result = self.collection.insert_one(trending_data)

        return self.get_trending_by_id(str(result.inserted_id))

    def get_trending_by_id(self, trending_id: str) -> dict:
        """
        Retrieve a trending document by its ID.
        """
        trending = self.collection.find_one({"_id": ObjectId(trending_id)})
        return trending

    def get_trendings_by_user(self, user_id: str) -> list:
        """
        Retrieve all trending documents for a specific user.
        """
        trendings = list(self.collection.find({"user_id": ObjectId(user_id)}))

        return trendings

    def update_trending(self, trending_id: str, update_data: dict) -> dict:
        """
        Update a trending document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(trending_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_trending_by_id(trending_id)
        return None

    def delete_trending(self, user_id: str, trending_id: str) -> bool:
        """
        Delete a trending document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(trending_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_trendings_by_trending(self, trending_id: str) -> list:
        """
        Retrieve all trending documents associated with a specific trending.
        """
        trendings = list(self.collection.find({"trending_id": ObjectId(trending_id)}))
        for trending in trendings:
            trending["_id"] = str(trending["_id"])
            trending["user_id"] = str(trending["user_id"])
            trending["trending_id"] = str(trending["trending_id"])
        return trendings

    def update_trending_status(self, trending_id: str, new_status: str) -> dict:
        """
        Update the status of a trending document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(trending_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_trending_by_id(trending_id)
        return None

    def create_trending_list(self, trending_list: list) -> list:
        """
        Create a list of new trendings.
        """
        result = []
        for trending in trending_list:
            created_trending = self.create_trending(trending)
            result.append(self.to_dict(created_trending))
        return result

    def get_trending_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of trendings in the collection.

        :param filter_criteria: Optional dictionary to filter the trendings before counting
        :return: The count of trendings matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_trendings_paginated(
        self, page: int = 1, per_page: int = 10, user_domain: list = []
    ) -> dict:
        """
        Get trendings with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {"field": {"$in": user_domain}}

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        trendings = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each trending
        trendings = [self.to_dict(trending) for trending in trendings]

        return {
            "trendings": trendings,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, trending_data):
        return {
            "_id": str(trending_data.get("_id", "")),
            "title": trending_data.get("title", ""),
            "link": trending_data.get("link", ""),
            "source": trending_data.get("source", ""),
            "snippet": trending_data.get("snippet", ""),
            "date": trending_data.get("date", ""),
            "image_url": trending_data.get("image_url", None),
            "video_url": trending_data.get("video_url", None),
            "field": trending_data.get("field", ""),
            "summary": trending_data.get("summary", None),
            "images": trending_data.get("images", []),
            "videos": trending_data.get("videos", []),
            "created_at": trending_data.get("created_at", None),
            "updated_at": trending_data.get("updated_at", None),
        }

    def get_trendings_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        trendings = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "trendings": trendings,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, trending_data):

        if isinstance(trending_data, dict):

            trending_data = self.convert_single_document_to_dict(trending_data)

        if isinstance(trending_data, list):

            for index, trending in enumerate(trending_data):
                trending = self.convert_single_document_to_dict(trending)
                trending_data[index] = trending

        return trending_data
