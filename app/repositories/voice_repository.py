from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class VoiceRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.VOICE_COLLECTION]

    def create_voice(self, voice_data) -> dict:
        """
        Create a new voice document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        voice_data["created_at"] = current_time
        voice_data["updated_at"] = current_time
        result = self.collection.insert_one(voice_data)

        return self.get_voice_by_id(str(result.inserted_id))

    def get_voice_by_id(self, voice_id: str) -> dict:
        """
        Retrieve a voice document by its ID.
        """
        try:
            voice = self.collection.find_one({"_id": ObjectId(voice_id)})
            return voice
        except Exception as e:
            print("Could not find voice with ID ", str(e))
            return None

    def get_default_voice(self) -> dict:
        """
        Retrieve a voice document by its ID.
        """
        try:
            voice = self.collection.find_one({"is_cloned": False})
            return voice
        except Exception as e:
            print("Could not find default voice", str(e))
            return None

    def get_voices_by_user(self, user_id: str) -> list:
        """
        Retrieve all voice documents for a specific user.
        """
        try:
            voices = list(self.collection.find({"user_id": ObjectId(user_id)}))
            return voices
        except Exception as e:
            print("Could not find voice with user_id ", str(e))
            return []

    def update_voice(self, voice_id: str, update_data: dict) -> dict:
        """
        Update a voice document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(voice_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_voice_by_id(voice_id)
        return None

    def delete_voice(self, user_id: str, voice_id: str) -> bool:
        """
        Delete a voice document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(voice_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_voices_by_voice(self, voice_id: str) -> list:
        """
        Retrieve all voice documents associated with a specific voice.
        """
        voices = list(self.collection.find({"voice_id": ObjectId(voice_id)}))
        for voice in voices:
            voice["_id"] = str(voice["_id"])
            voice["user_id"] = str(voice["user_id"])
            voice["voice_id"] = str(voice["voice_id"])
        return voices

    def update_voice_status(self, voice_id: str, new_status: str) -> dict:
        """
        Update the status of a voice document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(voice_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_voice_by_id(voice_id)
        return None

    def create_voice_list(self, voice_list: list) -> list:
        """
        Create a list of new voices.
        """
        result = []
        for voice in voice_list:
            created_voice = self.create_voice(voice)
            result.append(self.to_dict(created_voice))
        return result

    def get_voice_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of voices in the collection.

        :param filter_criteria: Optional dictionary to filter the voices before counting
        :return: The count of voices matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_voices_paginated(
        self,
        user_id,
        page: int = 1,
        per_page: int = 10,
        gender: str = None,
        is_cloned: bool = False,
    ) -> dict:
        """
        Get voices with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {
            "$or": [
                {"user_id": None},  # Matches documents where user_id is null
                {
                    "user_id": ObjectId(user_id)
                },  # Matches documents where user_id is equal to the specific user_id
            ],
        }
        if gender is not None:
            filter_criteria["gender"] = gender

        if is_cloned is not None:
            filter_criteria["is_cloned"] = is_cloned
        else:
            filter_criteria["is_cloned"] = False

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        voices = list(self.collection.find(filter_criteria).skip(skip).limit(per_page))

        # Convert ObjectId to string for each voice
        voices = [self.to_dict(voice) for voice in voices]

        return {
            "voices": voices,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, voice_data):
        return {
            "_id": str(voice_data.get("_id", "")),
            "user_id": str(voice_data.get("user_id", "")),
            "voice_id": voice_data.get("voice_id", ""),
            "link": voice_data.get("link", ""),
            "name": voice_data.get("name", ""),
            "accent": voice_data.get("accent", ""),
            "age": voice_data.get("age", ""),
            "gender": voice_data.get("gender", ""),
            "language": voice_data.get("language", ""),
            "language_code": voice_data.get("language_code", ""),
            "is_cloned": voice_data.get("is_cloned", ""),
            "voice_person": voice_data.get("voice_person", ""),
            "created_at": voice_data.get("created_at", None),
            "updated_at": voice_data.get("updated_at", None),
        }

    def get_voices_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        voices = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "voices": voices,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, voice_data):

        if isinstance(voice_data, dict):

            voice_data = self.convert_single_document_to_dict(voice_data)

        if isinstance(voice_data, list):

            for index, voice in enumerate(voice_data):
                voice = self.convert_single_document_to_dict(voice)
                voice_data[index] = voice

        return voice_data
