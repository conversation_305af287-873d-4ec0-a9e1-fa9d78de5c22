from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class ScheduleVideoRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.SCHEDULE_VIDEO_COLLECTION]

    def create_schedule_video(self, schedule_video_data) -> dict:
        """
        Create a new schedule_video document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        schedule_video_data["created_at"] = current_time
        schedule_video_data["updated_at"] = current_time
        result = self.collection.insert_one(schedule_video_data)

        return self.get_schedule_video_by_id(str(result.inserted_id))

    def get_schedule_video_by_id(self, schedule_video_id: str) -> dict:
        """
        Retrieve a schedule_video document by its ID.
        """
        schedule_video = self.collection.find_one({"_id": ObjectId(schedule_video_id)})
        return schedule_video

    def get_schedule_video_by_id_and_user(
        self, schedule_video_id: str, user_id: str
    ) -> dict:
        """
        Retrieve a schedule_video document by its ID.
        """
        schedule_video = self.collection.find_one(
            {"_id": ObjectId(schedule_video_id), "user_id": ObjectId(user_id)}
        )
        return schedule_video

    def get_schedule_videos_by_user(self, user_id: str) -> list:
        """
        Retrieve all schedule_video documents for a specific user.
        """
        schedule_videos = list(self.collection.find({"user_id": ObjectId(user_id)}))

        return schedule_videos

    def update_schedule_video(self, schedule_video_id: str, update_data: dict) -> dict:
        """
        Update a schedule_video document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(schedule_video_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_schedule_video_by_id(schedule_video_id)
        return None

    def delete_schedule_video(self, user_id: str, schedule_video_id: str) -> bool:
        """
        Delete a schedule_video document by its ID.
        """
        result = self.collection.delete_one(
            {"_id": ObjectId(schedule_video_id), "user_id": ObjectId(user_id)}
        )
        return result.deleted_count > 0

    def get_schedule_videos_by_schedule_video(self, schedule_video_id: str) -> list:
        """
        Retrieve all schedule_video documents associated with a specific schedule_video.
        """
        schedule_videos = list(
            self.collection.find({"schedule_video_id": ObjectId(schedule_video_id)})
        )
        for schedule_video in schedule_videos:
            schedule_video["_id"] = str(schedule_video["_id"])
            schedule_video["user_id"] = str(schedule_video["user_id"])
            schedule_video["schedule_video_id"] = str(
                schedule_video["schedule_video_id"]
            )
        return schedule_videos

    def update_schedule_video_status(
        self, schedule_video_id: str, new_status: str
    ) -> dict:
        """
        Update the status of a schedule_video document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(schedule_video_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_schedule_video_by_id(schedule_video_id)
        return None

    def create_schedule_video_list(self, schedule_video_list: list) -> list:
        """
        Create a list of new schedule_videos.
        """
        result = []
        for schedule_video in schedule_video_list:
            created_schedule_video = self.create_schedule_video(schedule_video)
            result.append(self.to_dict(created_schedule_video))
        return result

    def get_schedule_video_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of schedule_videos in the collection.

        :param filter_criteria: Optional dictionary to filter the schedule_videos before counting
        :return: The count of schedule_videos matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_schedule_videos_paginated(
        self,
        user_id,
        page: int = 1,
        per_page: int = 10,
        status: str = None,
    ) -> dict:
        """
        Get schedule_videos with pagination and optional filters on status and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param status: Optional filter for status ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {"user_id": ObjectId(user_id)}
        if status is not None:
            filter_criteria["status"] = status

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        schedule_videos = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each schedule_video
        schedule_videos = [
            self.to_dict(schedule_video) for schedule_video in schedule_videos
        ]

        return {
            "videos": schedule_videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def get_schedule_video_list(
        self,
        user_id: str,
        page: int = 1,
        per_page: int = 10,
        status: str = None,
        is_cloned: bool = None,
    ) -> dict:
        """
        Retrieve a list of schedule videos with optional filtering and pagination.

        :param user_id: The ID of the user whose schedule videos to retrieve
        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param status: Optional filter for video status
        :param is_cloned: Optional filter for cloned status
        :return: A dictionary containing the list of schedule videos and pagination metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {"user_id": ObjectId(user_id)}
        if status is not None:
            filter_criteria["status"] = status
        if is_cloned is not None:
            filter_criteria["is_cloned"] = is_cloned

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        schedule_videos = list(
            self.collection.find(filter_criteria)
            .sort("scheduled_time", DESCENDING)
            .skip(skip)
            .limit(per_page)
        )

        # Populate user and video data for each schedule video
        populated_schedule_videos = []
        for schedule_video in schedule_videos:
            populated_schedule_video = self.populate_schedule_video(schedule_video)
            populated_schedule_videos.append(populated_schedule_video)

        return {
            "schedule_videos": self.to_dict(populated_schedule_videos),
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def populate_schedule_video(self, schedule_video: dict) -> dict:
        """
        Populate a single schedule video with user and video data.
        """
        user_collection = self.dbname[collection.USER_COLLECTION]
        video_collection = self.dbname[collection.VIDEO_COLLECTION]

        user = user_collection.find_one({"_id": schedule_video["user_id"]})
        video = video_collection.find_one({"_id": schedule_video["video_id"]})

        return {
            **schedule_video,
            "user": self.to_dict(user) if user else None,
            "video": self.to_dict(video) if video else None,
        }

    def get_populated_schedule_video(self, schedule_video_id: str) -> dict:
        """
        Retrieve a schedule_video document by its ID and populate it with user and video data.
        """
        # Get the schedule video document
        schedule_video = self.collection.find_one({"_id": ObjectId(schedule_video_id)})

        if not schedule_video:
            return None

        # Get the user data
        user_collection = self.dbname[collection.USER_COLLECTION]
        user = user_collection.find_one({"_id": schedule_video["user_id"]})

        # Get the video data
        video_collection = self.dbname[collection.VIDEO_COLLECTION]
        video = video_collection.find_one({"_id": schedule_video["video_id"]})

        # Populate the schedule video with user and video data
        populated_schedule_video = {
            **self.to_dict(schedule_video),
            "user": self.to_dict(user) if user else None,
            "video": self.to_dict(video) if video else None,
        }

        return populated_schedule_video

    def convert_single_document_to_dict(self, schedule_video_data):
        return {
            "_id": str(schedule_video_data.get("_id", "")),
            "user_id": str(schedule_video_data.get("user_id", "")),
            "video_id": str(schedule_video_data.get("video_id", "")),
            "metadata": schedule_video_data.get("metadata", ""),
            "video_link": schedule_video_data.get("video_link", ""),
            "scheduled_time": schedule_video_data.get("scheduled_time", ""),
            "status": schedule_video_data.get("status", None),
            "thumbnail_link": schedule_video_data.get("thumbnail_link", None),
            "created_at": schedule_video_data.get("created_at", None),
            "updated_at": schedule_video_data.get("updated_at", None),
            "upload_domain": schedule_video_data.get("upload_domain", None),
        }

    def get_schedule_videos_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        sort_criteria = [("scheduled_time", 1)]
        # Get paginated results
        schedule_videos = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        return {
            "schedule_videos": schedule_videos,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, schedule_video_data):

        if isinstance(schedule_video_data, dict):

            schedule_video_data = self.convert_single_document_to_dict(
                schedule_video_data
            )

        if isinstance(schedule_video_data, list):

            for index, schedule_video in enumerate(schedule_video_data):
                schedule_video = self.convert_single_document_to_dict(schedule_video)
                schedule_video_data[index] = schedule_video

        return schedule_video_data
