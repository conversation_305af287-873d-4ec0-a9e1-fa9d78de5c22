from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class TemplateRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.TEMPLATE_COLLECTION]

    def create_template(self, template_data) -> dict:
        """
        Create a new template document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        template_data["created_at"] = current_time
        template_data["updated_at"] = current_time
        result = self.collection.insert_one(template_data)

        return self.get_template_by_id(str(result.inserted_id))

    def get_template_by_id(self, template_id: str) -> dict:
        """
        Retrieve a template document by its ID.
        """
        try:
            template = self.collection.find_one({"_id": ObjectId(template_id)})
            return template
        except Exception as e:
            print("Could not find template with ID ", str(e))
            return None

    def get_default_template(self) -> dict:
        """
        Retrieve a template document by its ID.
        """
        try:
            template = self.collection.find_one({})
            return template
        except Exception as e:
            print("Could not find default template", str(e))
            return None

    def get_templates_by_user(self, user_id: str) -> list:
        """
        Retrieve all template documents for a specific user.
        """
        templates = list(self.collection.find({"user_id": ObjectId(user_id)}))

        return templates

    def update_template(self, template_id: str, update_data: dict) -> dict:
        """
        Update a template document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(template_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_template_by_id(template_id)
        return None

    def delete_template(self, template_id: str) -> bool:
        """
        Delete a template document by its ID.
        """
        result = self.collection.delete_one({"_id": ObjectId(template_id)})
        return result.deleted_count > 0

    def get_templates_by_template(self, template_id: str) -> list:
        """
        Retrieve all template documents associated with a specific template.
        """
        templates = list(self.collection.find({"template_id": ObjectId(template_id)}))
        for template in templates:
            template["_id"] = str(template["_id"])
            template["user_id"] = str(template["user_id"])
            template["template_id"] = str(template["template_id"])
        return templates

    def update_template_status(self, template_id: str, new_status: str) -> dict:
        """
        Update the status of a template document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(template_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_template_by_id(template_id)
        return None

    def create_template_list(self, template_list: list) -> list:
        """
        Create a list of new templates.
        """
        result = []
        for template in template_list:
            created_template = self.create_template(template)
            result.append(self.to_dict(created_template))
        return result

    def get_template_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of templates in the collection.

        :param filter_criteria: Optional dictionary to filter the templates before counting
        :return: The count of templates matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_templates_paginated(
        self,
        page: int = 1,
        per_page: int = 10,
        type: str = None,
    ) -> dict:
        """
        Get templates with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param gender: Optional filter for gender ('male', 'female', etc.)
        :param is_cloned: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {}
        if type is not None:
            filter_criteria["type"] = type

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        # Get the paginated results
        templates = list(
            self.collection.find(filter_criteria).skip(skip).limit(per_page)
        )

        # Convert ObjectId to string for each template
        templates = [self.to_dict(template) for template in templates]

        return {
            "templates": templates,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, template_data):
        return {
            "_id": str(template_data.get("_id", "")),
            "template_id": template_data.get("template_id", ""),
            "title": template_data.get("title", ""),
            "type": template_data.get("type", ""),
            "created_at": template_data.get("created_at", None),
            "updated_at": template_data.get("updated_at", None),
            "description": template_data.get("description", ""),
            "image": template_data.get("image", ""),
            "video": template_data.get("video", ""),
            "available": template_data.get("available", False),
            "aspect_ratio": template_data.get(
                "aspect_ratio", None
            ),  # {"width": "1920", "height": "1080"},
        }

    def get_templates_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        templates = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "templates": templates,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, template_data):

        if isinstance(template_data, dict):

            template_data = self.convert_single_document_to_dict(template_data)

        if isinstance(template_data, list):

            for index, template in enumerate(template_data):
                template = self.convert_single_document_to_dict(template)
                template_data[index] = template

        return template_data
