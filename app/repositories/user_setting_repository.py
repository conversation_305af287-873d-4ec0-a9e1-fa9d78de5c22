from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime


class UserSettingRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.USER_SETTING_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_user_setting(self, user_setting_data) -> dict:
        """
        Create a new user_setting document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        user_setting_data["created_at"] = current_time
        user_setting_data["updated_at"] = current_time

        result = self.collection.insert_one(user_setting_data)
        return self.get_user_setting_by_id(str(result.inserted_id))

    def get_user_setting_by_id(self, user_setting_id: str) -> dict:
        """
        Retrieve a user_setting document by its ID.
        """
        try:
            user_setting = self.collection.find_one({"_id": ObjectId(user_setting_id)})
            return user_setting
        except Exception as e:
            print("Could not find user_setting with _id ", str(e))
            return None

    def get_user_settings_by_user(self, user_id: str) -> list:
        """
        Retrieve all user_setting documents for a specific user.
        """
        try:
            user_settings = list(self.collection.find({"user_id": ObjectId(user_id)}))
            return user_settings
        except Exception as e:
            print("Could not find user_setting with user_id ", str(e))
            return None

    def update_user_setting(self, user_setting_id: str, update_data: dict) -> dict:
        """
        Update a user_setting document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(user_setting_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_user_setting_by_id(user_setting_id)
        return None

    def update_user_setting_by_user(self, user_id: str, update_data: dict):
        """
        Update a user_setting document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"user_id": ObjectId(user_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_user_settings_by_user(user_id)
        return None

    def delete_user_setting(self, user_setting_id: str) -> bool:
        """
        Delete a user_setting document by its ID.
        """
        result = self.collection.delete_one({"_id": ObjectId(user_setting_id)})
        return result.deleted_count > 0

    def update_user_setting_status(self, user_setting_id: str, new_status: str) -> dict:
        """
        Update the status of a user_setting document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(user_setting_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_user_setting_by_id(user_setting_id)
        return None

    def convert_single_document_to_dict(self, user_setting_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in user_setting_data.items()}

    def to_dict(self, user_setting_data):
        if isinstance(user_setting_data, dict):
            return self.convert_single_document_to_dict(user_setting_data)
        elif isinstance(user_setting_data, list):
            return [
                self.convert_single_document_to_dict(item) for item in user_setting_data
            ]
        else:
            return user_setting_data

    def get_populated_user_settings(self, user_id: str) -> dict:
        """
        Retrieve user settings with populated user, voice, avatar, and template information.
        """
        pipeline = [
            # Match the user settings for the given user_id
            {"$match": {"user_id": ObjectId(user_id)}},
            # Lookup user information
            {
                "$lookup": {
                    "from": collection.USER_COLLECTION,
                    "localField": "user_id",
                    "foreignField": "_id",
                    "as": "user",
                }
            },
            {"$unwind": "$user"},
            # Lookup voice information
            {
                "$lookup": {
                    "from": collection.VOICE_COLLECTION,
                    "localField": "default_voice",
                    "foreignField": "_id",
                    "as": "voice",
                }
            },
            {"$unwind": {"path": "$voice", "preserveNullAndEmptyArrays": True}},
            # Lookup avatar information
            {
                "$lookup": {
                    "from": collection.AVATAR_COLLECTION,
                    "localField": "default_avatar",
                    "foreignField": "_id",
                    "as": "avatar",
                }
            },
            {"$unwind": {"path": "$avatar", "preserveNullAndEmptyArrays": True}},
            # Lookup template information
            {
                "$lookup": {
                    "from": collection.TEMPLATE_COLLECTION,
                    "localField": "default_template",
                    "foreignField": "_id",
                    "as": "template",
                }
            },
            {"$unwind": {"path": "$template", "preserveNullAndEmptyArrays": True}},
            # Project the final structure
            {
                "$project": {
                    "_id": 1,
                    "user_id": 1,
                    "default_avatar": 1,
                    "default_voice": 1,
                    "default_template": 1,
                    "user_interest_domain": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "user": {
                        "_id": 1,
                        "name": 1,
                        "email": 1,
                        "profile_picture": 1,
                        # Add other user fields as needed
                    },
                    "voice": {
                        "_id": 1,
                        "name": 1,
                        "voice_id": 1,
                        "link": 1,
                        # Add other voice fields as needed
                    },
                    "avatar": {
                        "_id": 1,
                        "name": 1,
                        "avatar_id": 1,
                        "link": 1,
                        "thumbnail_image": 1,
                        # Add other avatar fields as needed
                    },
                    "template": {
                        "_id": 1,
                        "title": 1,
                        "template_id": 1,
                        "image": 1,
                        # Add other template fields as needed
                    },
                }
            },
        ]

        result = list(self.collection.aggregate(pipeline))

        if result:
            # Convert ObjectId to string for JSON serialization
            populated_settings = self.to_dict(result[0])
            return populated_settings
        return None
