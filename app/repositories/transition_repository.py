from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
from pymongo import ASCENDING
import datetime

from app.constant.common_enum import Status


class TransactionRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.TRANSACTION_COLLECTION]
        # self._setup_collection()

    def _setup_collection(self):
        # Create indexes for createdAt and updatedAt
        self.collection.create_index([("created_at", ASCENDING)])
        self.collection.create_index([("updated_at", ASCENDING)])

    def create_transaction(self, transaction_data) -> dict:
        """
        Create a new transaction document in the database.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        transaction_data["created_at"] = current_time
        transaction_data["updated_at"] = current_time

        transaction_data["status"] = Status.ACTIVE.value

        result = self.collection.insert_one(transaction_data)
        return self.get_transaction_by_id(str(result.inserted_id))

    def get_transaction_by_id(self, transaction_id: str) -> dict:
        """
        Retrieve a transaction document by its ID.
        """
        try:
            transaction = self.collection.find_one({"_id": ObjectId(transaction_id)})
            return transaction

        except Exception as e:
            print("Could not find transaction with ID ", str(e))
            return None

    def get_transaction_by_stripe_id(self, transaction_id: str) -> dict:
        """
        Retrieve a transaction document by its ID.
        """
        try:
            transaction = self.collection.find_one(
                {"stripe_transaction_id": transaction_id}
            )
            return transaction

        except Exception as e:
            print("Could not find transaction with ID ", str(e))
            return None

    def get_transaction_by_user_id(self, user_id: str) -> dict:
        """
        Retrieve a transaction document by its ID.
        """
        try:
            transaction = self.collection.find_one({"user_id": ObjectId(user_id)})
            return transaction

        except Exception as e:
            print("Could not find transaction with ID ", str(e))
            return None

    def get_transactions(self, active=None, interval=None) -> list:
        """
        Retrieve all transaction documents for a specific user.
        """

        filter = {"status": Status.ACTIVE.value}

        if active is not None:
            filter["active"] = active
        if interval is not None:
            filter["interval"] = interval

        transactions = list(self.collection.find(filter))
        return transactions

    def update_transaction(self, transaction_id: str, update_data: dict) -> dict:
        """
        Update a transaction document. Only allows updating 'link' and 'status' fields.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(transaction_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_transaction_by_id(transaction_id)
        return None

    def delete_transaction(self, transaction_id: str) -> bool:
        """
        Delete a transaction document by its ID.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(transaction_id)},
            {"$set": {"status": Status.INACTIVATE.value}},
        )
        return result.modified_count > 0

    def update_transaction_status(self, transaction_id: str, new_status: str) -> dict:
        """
        Update the status of a transaction document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(transaction_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_transaction_by_id(transaction_id)
        return None

    def convert_single_document_to_dict(self, transaction_data):
        def convert_value(value):
            if isinstance(value, ObjectId):
                return str(value)
            elif isinstance(value, datetime.datetime):
                return value.isoformat()
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            return value

        return {k: convert_value(v) for k, v in transaction_data.items()}

    def to_dict(self, transaction_data):
        if isinstance(transaction_data, dict):
            return self.convert_single_document_to_dict(transaction_data)
        elif isinstance(transaction_data, list):
            return [
                self.convert_single_document_to_dict(item) for item in transaction_data
            ]
        else:
            return transaction_data
