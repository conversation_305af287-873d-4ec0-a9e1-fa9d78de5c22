from bson import ObjectId
from app.database.connection import MongoDBConnection
import app.database.collection_name as collection
import datetime


class NotificationRepository:
    def __init__(self):
        self.db_connection = MongoDBConnection().connection
        self.dbname = self.db_connection[collection.DB_NAME]
        self.collection = self.dbname[collection.NOTIFICATION_COLLECTION]

    def create_notification(self, notification_data) -> dict:
        """
        Create a new notification document in the database.
        """

        current_time = datetime.datetime.utcnow().isoformat()
        notification_data["created_at"] = current_time
        notification_data["updated_at"] = current_time
        result = self.collection.insert_one(notification_data)

        return self.get_notification_by_id(str(result.inserted_id))

    def get_notification_by_id(self, notification_id: str) -> dict:
        """
        Retrieve a notification document by its ID.
        """
        try:
            notification = self.collection.find_one({"_id": ObjectId(notification_id)})
            return notification
        except Exception as e:
            print("Could not find notification with ID ", str(e))
            return None

    def get_default_notification(self) -> dict:
        """
        Retrieve a notification document by its ID.
        """
        try:
            notification = self.collection.find_one({"is_cloned": False})
            return notification
        except Exception as e:
            print("Could not find default notification", str(e))
            return None

    def get_notifications_by_user(self, user_id: str) -> list:
        """
        Retrieve all notification documents for a specific user.
        """
        try:
            notifications = list(self.collection.find({"user_id": ObjectId(user_id)}))
            return notifications
        except Exception as e:
            print("Could not find notification with user_id ", str(e))
            return []

    def update_notification(self, notification_id: str, update_data: dict) -> dict:
        """
        Update a notification document. Only allows updating 'link' and 'status' fields.
        """
        current_time = datetime.datetime.utcnow().isoformat()
        update_data["updated_at"] = current_time

        result = self.collection.update_one(
            {"_id": ObjectId(notification_id)}, {"$set": update_data}
        )

        if result.modified_count > 0:
            return self.get_notification_by_id(notification_id)
        return None

    def delete_notification(self, notification_id: str) -> bool:
        """
        Delete a notification document by its ID.
        """
        result = self.collection.delete_one(
            {
                "_id": ObjectId(notification_id),
            }
        )
        return result.deleted_count > 0

    def get_notifications_by_user_and_id(self, notification_id, user_id: str) -> list:
        """
        Retrieve all notification documents associated with a specific notification.
        """
        try:
            notifications = list(
                self.collection.find(
                    {
                        "_id": ObjectId(notification_id),
                        "user_id": ObjectId(user_id),
                    }
                )
            )

            return notifications
        except Exception as e:
            print("Could not find default notification", str(e))
            return None

    def update_notification_status(self, notification_id: str, new_status: str) -> dict:
        """
        Update the status of a notification document.
        """
        result = self.collection.update_one(
            {"_id": ObjectId(notification_id)}, {"$set": {"status": new_status}}
        )

        if result.modified_count > 0:
            return self.get_notification_by_id(notification_id)
        return None

    def create_notification_list(self, notification_list: list) -> list:
        """
        Create a list of new notifications.
        """
        result = []
        for notification in notification_list:
            created_notification = self.create_notification(notification)
            result.append(self.to_dict(created_notification))
        return result

    def get_notification_count(self, filter_criteria: dict = None) -> int:
        """
        Get the count of notifications in the collection.

        :param filter_criteria: Optional dictionary to filter the notifications before counting
        :return: The count of notifications matching the filter criteria (or total count if no filter is provided)
        """
        if filter_criteria is None:
            filter_criteria = {}
        return self.collection.count_documents(filter_criteria)

    def get_notifications_paginated(
        self,
        user_id,
        page: int = 1,
        per_page: int = 10,
        type: str = None,
        is_read: bool = False,
    ) -> dict:
        """
        Get notifications with pagination and optional filters on gender and is_cloned.

        :param page: The page number (1-indexed)
        :param per_page: The number of items per page
        :param type: Optional filter for type ('male', 'female', etc.)
        :param is_read: Optional filter for cloned status (True or False)
        :return: A dictionary containing the paginated results and metadata
        """
        skip = (page - 1) * per_page

        # Build the filter
        filter_criteria = {
            "$or": [
                {"user_id": None},  # Matches documents where user_id is null
                {
                    "user_id": ObjectId(user_id)
                },  # Matches documents where user_id is equal to the specific user_id
            ],
        }
        if type is not None:
            filter_criteria["type"] = type

        if is_read is not None:
            filter_criteria["is_read"] = is_read

        # Get the total count of matching documents
        total_count = self.collection.count_documents(filter_criteria)

        sort_criteria = [("created_at", -1)]

        # Get the paginated results
        notifications = list(
            self.collection.find(filter_criteria)
            .sort(sort_criteria)
            .skip(skip)
            .limit(per_page)
        )

        # Convert ObjectId to string for each notification
        notifications = [self.to_dict(notification) for notification in notifications]

        return {
            "notifications": notifications,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def convert_single_document_to_dict(self, notification_data):
        return {
            "_id": str(notification_data.get("_id", "")),
            "user_id": str(notification_data.get("user_id", "")),
            "title": notification_data.get("title", None),
            "description": notification_data.get("description", None),
            "type": notification_data.get("type", None),
            "is_read": notification_data.get("is_read", None),
            "data": notification_data.get("data", {}),
            "created_at": notification_data.get("created_at", None),
            "updated_at": notification_data.get("updated_at", None),
        }

    def get_notifications_by_user_paginated(
        self, user_id: str, page: int = 1, per_page: int = 10
    ):
        skip = (page - 1) * per_page

        # Get total count
        total_count = self.collection.count_documents({"user_id": ObjectId(user_id)})

        # Get paginated results
        notifications = list(
            self.collection.find({"user_id": ObjectId(user_id)})
            .skip(skip)
            .limit(per_page)
        )

        return {
            "notifications": notifications,
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": -(-total_count // per_page),  # Ceiling division
        }

    def to_dict(self, notification_data):

        if isinstance(notification_data, dict):

            notification_data = self.convert_single_document_to_dict(notification_data)

        if isinstance(notification_data, list):

            for index, notification in enumerate(notification_data):
                notification = self.convert_single_document_to_dict(notification)
                notification_data[index] = notification

        return notification_data
