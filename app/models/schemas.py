import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Enum, Integer, Float, JSON, Text, ForeignKey
from sqlalchemy.dialects.postgresql import ARRAY
from app.db.base_class import Base
from app.utils.constants.constants import (
    EventType,
    ServiceType,
    AnalyticsStatus,
    ApplicationStatus,
    APIKeyStatus,
)


class AnalyticsEventSchema(Base):
    __tablename__ = "analytics_events"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    event_type = Column(Enum(EventType), nullable=False)
    service_type = Column(Enum(ServiceType), nullable=False)
    entity_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    event_metadata = Column(JSON, nullable=True)
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )


class ServiceMetricsSchema(Base):
    __tablename__ = "service_metrics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    service_type = Column(Enum(ServiceType), nullable=False)
    entity_id = Column(String, nullable=False)
    usage_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)
    rating_count = Column(Integer, default=0)
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )


class UserActivitySchema(Base):
    __tablename__ = "user_activities"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False)
    mcp_usage_count = Column(Integer, default=0)
    workflow_usage_count = Column(Integer, default=0)
    agent_usage_count = Column(Integer, default=0)
    mcp_creation_count = Column(Integer, default=0)
    workflow_creation_count = Column(Integer, default=0)
    agent_creation_count = Column(Integer, default=0)
    last_activity_date = Column(DateTime, nullable=True)
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )


class ApplicationSchema(Base):
    __tablename__ = "applications"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum(ApplicationStatus), nullable=False, default=ApplicationStatus.ACTIVE)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )


class APIKeySchema(Base):
    __tablename__ = "api_keys"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    application_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    public_key = Column(String, nullable=False, unique=True)
    private_key_hash = Column(String, nullable=False)
    scopes = Column(ARRAY(String), nullable=True)
    status = Column(Enum(APIKeyStatus), nullable=False, default=APIKeyStatus.ACTIVE)
    expires_at = Column(DateTime, nullable=True)
    last_used_at = Column(DateTime, nullable=True)
    usage_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )


# Additional schemas for Webhook, WebhookLog, ActivationEvent, UsageEvent can be similarly defined here
