"""
Dashboard Analytics Models
Database models for comprehensive analytics dashboard functionality
"""

from sqlalchemy import (
    Column,
    String,
    Integer,
    Float,
    DateTime,
    Text,
    Boolean,
    ForeignKey,
    Index,
    Enum,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
import uuid
import enum


class RequestType(str, enum.Enum):
    """Types of requests tracked in the system"""

    API_REQUEST = "api_request"
    WORKFLOW_EXEC = "workflow_exec"
    AUTH_EVENT = "auth_event"
    AGENT_INVOKE = "agent_invoke"
    MCP_REQUEST = "mcp_request"


class RequestStatus(str, enum.Enum):
    """Status of requests"""

    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    TIMEOUT = "timeout"


class CreditCategory(str, enum.Enum):
    """Categories for credit usage breakdown"""

    AGENTS = "agents"
    WORKFLOWS = "workflows"
    CUSTOM_MCPS = "custom_mcps"
    APP_CREDITS = "app_credits"
    OTHER = "other"


class DashboardMetrics(Base):
    """
    Daily aggregated metrics for dashboard overview
    """

    __tablename__ = "dashboard_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, nullable=False, index=True)
    user_id = Column(String, nullable=True, index=True)  # Optional user-specific metrics

    # Core metrics
    active_agents = Column(Integer, default=0)
    credit_usage = Column(Float, default=0.0)
    agent_requests = Column(Integer, default=0)
    workflow_requests = Column(Integer, default=0)
    custom_mcps = Column(Integer, default=0)

    # Growth metrics (compared to previous period)
    credit_usage_change = Column(Float, default=0.0)
    agent_requests_change_pct = Column(Float, default=0.0)
    workflow_requests_change_pct = Column(Float, default=0.0)
    custom_mcps_change = Column(Integer, default=0)

    # Additional metrics
    total_cost = Column(Float, default=0.0)
    app_credits_used = Column(Integer, default=0)
    app_credits_cost = Column(Float, default=0.0)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index("idx_dashboard_metrics_date_user", "date", "user_id"),
        Index("idx_dashboard_metrics_date", "date"),
    )


class CreditUsageBreakdown(Base):
    """
    Credit usage breakdown by category for bar chart visualization
    """

    __tablename__ = "credit_usage_breakdown"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, nullable=False, index=True)
    user_id = Column(String, nullable=True, index=True)

    category = Column(Enum(CreditCategory), nullable=False)
    credits_used = Column(Float, default=0.0)
    cost = Column(Float, default=0.0)
    request_count = Column(Integer, default=0)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index("idx_credit_breakdown_date_category", "date", "category"),
        Index("idx_credit_breakdown_user_date", "user_id", "date"),
    )


class ApiRequestEvent(Base):
    """
    Individual API request and event tracking for real-time activity
    """

    __tablename__ = "api_request_events"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Request details
    request_type = Column(Enum(RequestType), nullable=False, index=True)
    endpoint = Column(String, nullable=False)
    method = Column(String, nullable=True)  # GET, POST, etc.
    status = Column(Enum(RequestStatus), nullable=False, index=True)

    # Timing
    timestamp = Column(DateTime, nullable=False, index=True, server_default=func.now())
    duration_ms = Column(Integer, nullable=True)  # Duration in milliseconds

    # User and context
    user_id = Column(String, nullable=True, index=True)
    user_email = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)
    user_agent = Column(Text, nullable=True)

    # Request/Response data
    request_data = Column(JSONB, nullable=True)
    response_data = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)

    # Resource identifiers
    agent_id = Column(String, nullable=True, index=True)
    workflow_id = Column(String, nullable=True, index=True)
    application_id = Column(String, nullable=True, index=True)

    # Credits and cost
    credits_used = Column(Float, default=0.0)
    cost = Column(Float, default=0.0)

    created_at = Column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("idx_api_events_timestamp_status", "timestamp", "status"),
        Index("idx_api_events_user_timestamp", "user_id", "timestamp"),
        Index("idx_api_events_type_timestamp", "request_type", "timestamp"),
        Index("idx_api_events_endpoint", "endpoint"),
    )


class AgentPerformanceMetrics(Base):
    """
    Agent performance metrics for platform analytics
    """

    __tablename__ = "agent_performance_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, nullable=False, index=True)
    hour = Column(Integer, nullable=True)  # Hour of day for hourly metrics

    agent_id = Column(String, nullable=False, index=True)
    agent_name = Column(String, nullable=True)
    user_id = Column(String, nullable=True, index=True)

    # Performance metrics
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    avg_response_time_ms = Column(Float, default=0.0)

    # Usage metrics
    total_credits_used = Column(Float, default=0.0)
    total_cost = Column(Float, default=0.0)

    # Status
    is_active = Column(Boolean, default=True)
    deployment_status = Column(String, nullable=True)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index("idx_agent_perf_date_agent", "date", "agent_id"),
        Index("idx_agent_perf_user_date", "user_id", "date"),
        Index("idx_agent_perf_active", "is_active"),
    )


class WorkflowUtilizationMetrics(Base):
    """
    Workflow utilization metrics for platform analytics
    """

    __tablename__ = "workflow_utilization_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, nullable=False, index=True)
    hour = Column(Integer, nullable=True)  # Hour of day for hourly metrics

    workflow_id = Column(String, nullable=False, index=True)
    workflow_name = Column(String, nullable=True)
    user_id = Column(String, nullable=True, index=True)

    # Execution metrics
    total_executions = Column(Integer, default=0)
    successful_executions = Column(Integer, default=0)
    failed_executions = Column(Integer, default=0)
    avg_execution_time_ms = Column(Float, default=0.0)

    # Completion rate
    completion_rate_pct = Column(Float, default=0.0)

    # Usage metrics
    total_credits_used = Column(Float, default=0.0)
    total_cost = Column(Float, default=0.0)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        Index("idx_workflow_util_date_workflow", "date", "workflow_id"),
        Index("idx_workflow_util_user_date", "user_id", "date"),
    )


class AppCreditUsage(Base):
    """
    Application credit usage over time for time series visualization
    """

    __tablename__ = "app_credit_usage"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime, nullable=False, index=True)

    application_id = Column(String, nullable=True, index=True)
    application_name = Column(String, nullable=True)
    user_id = Column(String, nullable=True, index=True)

    # Credit metrics
    credits_used = Column(Float, default=0.0)
    cost = Column(Float, default=0.0)
    cumulative_credits = Column(Float, default=0.0)
    cumulative_cost = Column(Float, default=0.0)

    created_at = Column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("idx_app_credit_timestamp_app", "timestamp", "application_id"),
        Index("idx_app_credit_user_timestamp", "user_id", "timestamp"),
    )


class SystemActivity(Base):
    """
    System activity and issues tracking
    """

    __tablename__ = "system_activity"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime, nullable=False, index=True, server_default=func.now())

    activity_type = Column(String, nullable=False, index=True)  # inquiry, alert, etc.
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)

    # Severity and status
    severity = Column(String, nullable=True)  # info, warning, error
    status = Column(String, nullable=True)  # new, acknowledged, resolved

    # Related entities
    user_id = Column(String, nullable=True, index=True)
    customer_id = Column(String, nullable=True)

    # Additional data
    activity_metadata = Column(JSONB, nullable=True)

    created_at = Column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("idx_system_activity_timestamp_type", "timestamp", "activity_type"),
        Index("idx_system_activity_severity", "severity"),
    )
