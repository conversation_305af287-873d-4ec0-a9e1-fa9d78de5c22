import enum
import uuid
from datetime import datetime, timezone
from sqlalchemy import <PERSON>um<PERSON>, Foreign<PERSON>ey, String, DateTime, Enum, Integer, Float, JSON, Text, Boolean
from sqlalchemy.dialects.postgresql import ARRAY
from app.db.base_class import Base
from app.utils.constants.constants import (
    EventType,
    ServiceType,
    AnalyticsStatus,
    ApplicationStatus,
    WebhookStatus,
    TableNames,
)


class AnalyticsEvent(Base):
    __tablename__ = TableNames.ANALYTICS_EVENTS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Event information
    event_type = Column(Enum(EventType), nullable=False)
    service_type = Column(Enum(ServiceType), nullable=False)

    # Entity information
    entity_id = Column(String, nullable=False)  # ID of the entity (MCP, workflow, agent, etc.)
    entity_type = Column(String, nullable=False)  # workflow, agent, mcp, application
    user_id = Column(String, nullable=False)  # User who performed the action

    # Reference IDs for cross-service tracking
    workflow_id = Column(String, nullable=True)  # Reference to workflow service
    agent_id = Column(String, nullable=True)  # Reference to agent service
    mcp_id = Column(String, nullable=True)  # Reference to MCP service
    api_key_id = Column(String, nullable=True)  # Reference to API key

    # Additional data
    event_metadata = Column(JSON, nullable=True)  # Additional event-specific data

    # Status
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<AnalyticsEvent(id={self.id}, event_type='{self.event_type}', service_type='{self.service_type}', entity_id='{self.entity_id}')>"


class ServiceMetrics(Base):
    __tablename__ = TableNames.SERVICE_METRICS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Service information
    service_type = Column(Enum(ServiceType), nullable=False)
    entity_id = Column(String, nullable=False)  # ID of the entity (MCP, workflow, agent, etc.)
    entity_type = Column(String, nullable=False)  # workflow, agent, mcp, application

    # Reference IDs for cross-service tracking
    workflow_id = Column(String, nullable=True)  # Reference to workflow service
    agent_id = Column(String, nullable=True)  # Reference to agent service
    mcp_id = Column(String, nullable=True)  # Reference to MCP service

    # Metrics
    usage_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)
    rating_count = Column(Integer, default=0)

    # Performance metrics
    total_execution_time_ms = Column(Integer, default=0)
    average_execution_time_ms = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)

    # Status
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<ServiceMetrics(id={self.id}, service_type='{self.service_type}', entity_id='{self.entity_id}', usage_count={self.usage_count})>"


class UserActivity(Base):
    __tablename__ = TableNames.USER_ACTIVITIES

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # User information
    user_id = Column(String, nullable=False)

    # Activity metrics by service
    mcp_usage_count = Column(Integer, default=0)
    workflow_usage_count = Column(Integer, default=0)
    agent_usage_count = Column(Integer, default=0)

    mcp_creation_count = Column(Integer, default=0)
    workflow_creation_count = Column(Integer, default=0)
    agent_creation_count = Column(Integer, default=0)

    # API usage
    api_call_count = Column(Integer, default=0)
    api_key_usage_count = Column(Integer, default=0)

    last_activity_date = Column(DateTime, nullable=True)

    # Status
    status = Column(Enum(AnalyticsStatus), nullable=False, default=AnalyticsStatus.ACTIVE)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<UserActivity(id={self.id}, user_id='{self.user_id}')>"


class Application(Base):
    __tablename__ = TableNames.APPLICATIONS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Application information
    user_id = Column(String, nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)

    # Reference IDs for cross-service tracking (matching proto repeated fields)
    workflow_ids = Column(
        ARRAY(String), nullable=True, default=list
    )  # repeated string workflow_ids
    agent_ids = Column(ARRAY(String), nullable=True, default=list)  # repeated string agent_ids

    api_keys = Column(
        ARRAY(String), nullable=True, default=list
    )  # API key IDs for application access

    # Status and soft deletion
    status = Column(
        Enum(ApplicationStatus), nullable=False, default=ApplicationStatus.ACTIVE
    )  # Application status
    is_deleted = Column(Boolean, nullable=True, default=False)  # Soft deletion flag

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<Application(id={self.id}, name='{self.name}', user_id='{self.user_id}', status='{self.status}')>"


class ApplicationImage(Base):
    __tablename__ = TableNames.APPLICATION_IMAGES

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Image information
    application_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    image_name = Column(String, nullable=False)
    image_type = Column(String, nullable=False)  # MIME type: image/jpeg, image/png, etc.
    image_size = Column(Integer, nullable=False)  # Size in bytes
    image_path = Column(String, nullable=False)  # File system path or cloud storage URL
    description = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<ApplicationImage(id={self.id}, application_id='{self.application_id}', image_name='{self.image_name}')>"


class Webhook(Base):
    __tablename__ = TableNames.WEBHOOKS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Webhook information
    user_id = Column(String, nullable=False)
    application_id = Column(String, nullable=True)  # Optional association with application
    url = Column(String, nullable=False)
    event_types = Column(ARRAY(String), nullable=False)  # List of webhook event types
    secret = Column(String, nullable=True)  # For payload verification
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)

    # Status and metrics
    status = Column(Enum(WebhookStatus), nullable=False, default=WebhookStatus.ACTIVE)
    delivery_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    last_delivery_at = Column(DateTime, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<Webhook(id={self.id}, url='{self.url}', user_id='{self.user_id}', status='{self.status}')>"


class WebhookLog(Base):
    __tablename__ = TableNames.WEBHOOK_LOGS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Log information
    webhook_id = Column(String, nullable=False)
    event_type = Column(String, nullable=False)  # WebhookEventType as string
    payload = Column(String, nullable=False)  # JSON string (matching proto)
    response_status = Column(Integer, nullable=True)
    response_body = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    success = Column(Boolean, nullable=False)
    retry_count = Column(Integer, default=0)

    # Timestamps
    delivered_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    def __repr__(self):
        return f"<WebhookLog(id={self.id}, webhook_id='{self.webhook_id}', success={self.success})>"


class ActivationEvent(Base):
    __tablename__ = TableNames.ACTIVATION_EVENTS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Activation information
    user_id = Column(String, nullable=False)
    event_type = Column(String, nullable=False)  # ActivationEventType as string
    entity_id = Column(String, nullable=True)  # Optional: ID of related entity

    # Reference IDs for cross-service tracking
    workflow_id = Column(String, nullable=True)  # Reference to workflow service
    agent_id = Column(String, nullable=True)  # Reference to agent service
    mcp_id = Column(String, nullable=True)  # Reference to MCP service

    event_metadata = Column(JSON, nullable=True)  # Additional event-specific data

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    def __repr__(self):
        return f"<ActivationEvent(id={self.id}, user_id='{self.user_id}', event_type='{self.event_type}')>"


class UsageEvent(Base):
    __tablename__ = TableNames.USAGE_EVENTS

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Usage information
    user_id = Column(String, nullable=False)
    entity_type = Column(String, nullable=False)  # agent, workflow, mcp, application
    entity_id = Column(String, nullable=False)  # ID of the entity being used
    action = Column(String, nullable=False)  # invoke, run, execute, create, update, delete

    # Reference IDs for cross-service tracking
    workflow_id = Column(String, nullable=True)  # Reference to workflow service
    agent_id = Column(String, nullable=True)  # Reference to agent service
    mcp_id = Column(String, nullable=True)  # Reference to MCP service
    api_key_id = Column(String, nullable=True)  # Reference to API key used

    # Credit and cost tracking
    credits_used = Column(Float, default=0.0)
    cost = Column(Float, default=0.0)

    # Performance tracking
    execution_time_ms = Column(Integer, nullable=True)
    success = Column(Boolean, default=True)
    error_message = Column(String, nullable=True)

    # Additional data
    event_metadata = Column(JSON, nullable=True)  # Additional usage-specific data

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    def __repr__(self):
        return f"<UsageEvent(id={self.id}, user_id='{self.user_id}', entity_type='{self.entity_type}', credits_used={self.credits_used})>"



#enums and schemas for activity
class ActivityType(enum.Enum):
    WORKFLOW = "WORKFLOW"
    AGENT = "AGENT"
    # Add other types as needed

class ActivityStatus(enum.Enum):
    STARTED = "STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class LogType(enum.Enum):
    TOOL_EXECUTION = "TOOL_EXECUTION"
    NODE_EXECUTION = "NODE_EXECUTION"

class LogStatus(enum.Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"

# Base Activity Table Schema
class Activity(Base):
    __tablename__ = "activities"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    resource_id = Column(String, nullable=False, index=True) # e.g., workflow_id
    type = Column(Enum(ActivityType, native_enum=False), nullable=False, index=True)
    user_id = Column(String, nullable=False, index=True)
    status = Column(Enum(ActivityStatus, native_enum=False), nullable=False, index=True)
    activity_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

# Activity Log Specific Table Schema (references Activity)
class ActivityLog(Base):
    __tablename__ = "activity_logs"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4())) # Own primary key
    activity_id = Column(String, ForeignKey("activities.id", ondelete="CASCADE"), nullable=False, index=True) # Link to the base activity record
    
    log_type = Column(Enum(LogType, native_enum=False), nullable=False)
    log_status = Column(Enum(LogStatus, native_enum=False), nullable=False)
    log_details = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

# Activity Event Specific Table Schema (references Activity)
class ActivityEvent(Base):
    __tablename__ = "activity_events"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4())) # Own primary key
    activity_id = Column(String, ForeignKey("activities.id", ondelete="CASCADE"), nullable=False, index=True) # Link to the base activity record
    
    event_name = Column(String(255), nullable=False, index=True) # e.g., workflow.completed, user.signup
    event_details = Column(JSON, nullable=True) # Event-specific payload
    
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)