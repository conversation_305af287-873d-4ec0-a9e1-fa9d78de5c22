"""
<PERSON><PERSON>t to initialize default topup plans.
This script creates default topup plans if they don't exist in the database.
"""

import stripe
import structlog
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.topup import TopupPlan

stripe.api_key = settings.STRIPE_API_KEY
logger = structlog.get_logger(__name__)


def initialize_topup_plans():
    """Initialize default topup plans if they don't exist."""
    db = SessionLocal()
    try:
        # Check if topup plans already exist
        existing_plans = db.query(TopupPlan).count()
        if existing_plans > 0:
            logger.info("Topup plans already exist in database", count=existing_plans)
            return

        logger.info("Creating default topup plans...")

        # Define default topup plans
        # Based on subscription plans:
        # - Free Plan: 20 credits for $0.00
        # - Pro Plan: 100 credits for $10.00
        # 
        # Topup plans should be smaller and more affordable:
        default_topup_plans = [
            {
                "plan_id_code": "topup_small",
                "name": "Small Credit Topup",
                "credit_amount": 10,  # 10 credits for $2 (better value than pro plan)
                "price": 2.00
            },
            {
                "plan_id_code": "topup_medium",
                "name": "Medium Credit Topup", 
                "credit_amount": 25,  # 25 credits for $4 (better value than pro plan)
                "price": 4.00
            },
            {
                "plan_id_code": "topup_large",
                "name": "Large Credit Topup",
                "credit_amount": 50,  # 50 credits for $7 (better value than pro plan)
                "price": 7.00
            }
        ]

        for plan_data in default_topup_plans:
            try:
                # Create product in Stripe
                product = stripe.Product.create(name=plan_data["name"])

                # Create price in Stripe (one-time payment, not recurring)
                price = stripe.Price.create(
                    product=product.id,
                    unit_amount=int(plan_data["price"] * 100),  # Convert to cents
                    currency="usd",
                    # No recurring parameter for one-time payments
                )

                # Create topup plan in database
                topup_plan = TopupPlan(
                    plan_id_code=plan_data["plan_id_code"],
                    name=plan_data["name"],
                    credit_amount=plan_data["credit_amount"],
                    price=plan_data["price"],
                    stripe_price_id=price.id,
                )
                db.add(topup_plan)

                logger.info("Created topup plan", 
                           plan_name=plan_data["name"],
                           credits=plan_data["credit_amount"],
                           price=plan_data["price"],
                           stripe_product_id=product.id,
                           stripe_price_id=price.id)

            except stripe.error.StripeError as e:
                logger.error("Failed to create topup plan in Stripe", 
                           plan_name=plan_data["name"], 
                           error=str(e))
                continue
            except Exception as e:
                logger.error("Failed to create topup plan", 
                           plan_name=plan_data["name"], 
                           error=str(e))
                continue

        db.commit()
        logger.info("Successfully initialized default topup plans")

    except Exception as e:
        logger.error("Error initializing topup plans", error=str(e))
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    initialize_topup_plans()
