import os
import sys
import importlib.util
import traceback
import logging

# Check if required packages are installed
required_packages = ["grpc", "concurrent"]
missing_packages = []

for package in required_packages:
    if importlib.util.find_spec(package) is None:
        missing_packages.append(package)

if missing_packages:
    print(f"Error: Missing required packages: {', '.join(missing_packages)}")
    print("Please install them using: poetry add grpcio")
    sys.exit(1)

try:
    import grpc
    from concurrent import futures
    from app.services.analytics_service import AnalyticsService
    from app.services.application_service import ApplicationService
    from app.services.activity_service import ActivityService
    from app.grpc import analytics_pb2_grpc
    from app.grpc.dashboard_analytics_service import EnhancedAnalyticsServicer
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure dependencies are installed and gRPC code is generated.")
    print("Run: poetry install && python -m grpc_tools.protoc \\")
    print("  --proto_path=proto-definitions --python_out=app/grpc \\")
    print("  --grpc_python_out=app/grpc analytics.proto")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def serve():
    """
    Start the gRPC server with analytics and application services.

    This function creates and starts a gRPC server that includes:
    - Original AnalyticsService
    - Enhanced Dashboard Analytics service
    - ApplicationService
    """
    try:
        # Create gRPC server with thread pool and keepalive options
        server = grpc.server(
            futures.ThreadPoolExecutor(max_workers=10),
            options=[
                ("grpc.keepalive_time_ms", 30000),
                ("grpc.keepalive_timeout_ms", 5000),
                ("grpc.keepalive_permit_without_calls", True),
                ("grpc.http2.max_pings_without_data", 0),
                ("grpc.http2.min_time_between_pings_ms", 10000),
                ("grpc.http2.min_ping_interval_without_data_ms", 300000),
            ],
        )

        # Add original analytics service to server
        logger.info("Adding AnalyticsService to gRPC server...")
        analytics_service = AnalyticsService()
        analytics_pb2_grpc.add_AnalyticsServiceServicer_to_server(analytics_service, server)

        # Add enhanced dashboard analytics service to server
        logger.info("Adding Enhanced Dashboard Analytics service...")
        enhanced_analytics_service = EnhancedAnalyticsServicer()
        analytics_pb2_grpc.add_AnalyticsServiceServicer_to_server(
            enhanced_analytics_service, server
        )

        # Add application service to server
        logger.info("Adding ApplicationService to gRPC server...")
        application_service = ApplicationService()
        analytics_pb2_grpc.add_ApplicationServiceServicer_to_server(application_service, server)

        # Add activity service to server
        logger.info("Adding ActivityService to gRPC server...")
        activity_service = ActivityService()
        analytics_pb2_grpc.add_ActivityServiceServicer_to_server(activity_service, server)

        # Get port from environment or use default
        port = os.getenv("PORT", "50059")
        server.add_insecure_port(f"[::]:{port}")

        # Start server
        server.start()
        logger.info(f"🚀 gRPC server started successfully on port {port}")
        logger.info("📊 Available services:")
        logger.info("   - AnalyticsService (original)")
        logger.info("   - Enhanced Dashboard Analytics")
        logger.info("   - ApplicationService")
        logger.info("   - ActivityService")
        logger.info("🛑 Press Ctrl+C to stop the server")

        # Keep server running
        server.wait_for_termination()

    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal, shutting down gracefully...")
        server.stop(grace=5)
        logger.info("✅ Server stopped successfully")

    except Exception as e:
        logger.error(f"❌ Error starting gRPC server: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    serve()
