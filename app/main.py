import os
import asyncio
import grpc
import structlog
import threading
from app.core.config import settings
from app.services.payment_service import get_payment_service
from app.services.topup_service import initialize_topup_plans
from app.grpc import payment_pb2_grpc
from app.kafka.token_usage_listener import start_token_usage_listener
from app.db.session import SessionLocal

logger = structlog.get_logger()

def run_kafka_listener():
    """Function to run in a separate thread for the Kafka listener."""
    logger.info("Starting Kafka listener in a background thread...")
    db = SessionLocal()
    try:
        start_token_usage_listener(db)
    finally:
        db.close()

async def serve():
    # Initialize topup plans on startup
    logger.info("Initializing topup plans...")
    initialize_topup_plans()

    # Create gRPC server
    server = grpc.aio.server()

    # Add payment service to server
    payment_service = get_payment_service()
    payment_pb2_grpc.add_PaymentServiceServicer_to_server(payment_service, server)

    # Get port from environment or use default
    port = os.getenv('PORT', '50061')
    server.add_insecure_port(f'[::]:{port}')
    
    # Start the Kafka listener in a background thread
    kafka_thread = threading.Thread(target=run_kafka_listener, daemon=True)
    kafka_thread.start()

    # Start server
    await server.start()
    logger.info(f"Payment service started on port {port}")

    # Keep thread alive
    await server.wait_for_termination()

if __name__ == '__main__':
    asyncio.run(serve())