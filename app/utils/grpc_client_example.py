"""
gRPC Client Example for Enhanced Dashboard Analytics
"""

import grpc
import json
from datetime import datetime
from app.grpc import analytics_pb2, analytics_pb2_grpc


class AnalyticsGRPCClient:
    """gRPC Client for Analytics Service"""
    
    def __init__(self, server_address: str = "localhost:50051"):
        self.server_address = server_address
        self.channel = None
        self.stub = None
    
    def connect(self):
        """Connect to the gRPC server"""
        self.channel = grpc.insecure_channel(self.server_address)
        self.stub = analytics_pb2_grpc.AnalyticsServiceStub(self.channel)
        print(f"Connected to gRPC server at {self.server_address}")
    
    def disconnect(self):
        """Disconnect from the gRPC server"""
        if self.channel:
            self.channel.close()
            print("Disconnected from gRPC server")
    
    def get_dashboard_metrics(self, user_id: str = None, days: int = 7):
        """Get dashboard overview metrics"""
        request = analytics_pb2.GetDashboardMetricsRequest(
            user_id=user_id or "",
            days=days
        )
        
        try:
            response = self.stub.GetDashboardMetrics(request)
            if response.success:
                print("Dashboard Metrics:")
                print(f"  Active Agents: {response.metrics.active_agents}")
                print(f"  Credit Usage: ${response.metrics.credit_usage:.2f}")
                print(f"  Agent Requests: {response.metrics.agent_requests}")
                print(f"  Workflow Requests: {response.metrics.workflow_requests}")
                print(f"  Custom MCPs: {response.metrics.custom_mcps}")
                print(f"  Total Cost: ${response.metrics.total_cost:.2f}")
                return response.metrics
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def get_credit_usage_breakdown(self, user_id: str = None, days: int = 7):
        """Get credit usage breakdown by category"""
        request = analytics_pb2.GetCreditUsageBreakdownRequest(
            user_id=user_id or "",
            days=days
        )
        
        try:
            response = self.stub.GetCreditUsageBreakdown(request)
            if response.success:
                print("Credit Usage Breakdown:")
                for item in response.breakdown:
                    print(f"  {item.category}: {item.credits_used:.1f} credits (${item.cost:.2f})")
                return response.breakdown
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def get_app_credit_usage(self, user_id: str = None, application_id: str = None, days: int = 7):
        """Get application credit usage over time"""
        request = analytics_pb2.GetAppCreditUsageRequest(
            user_id=user_id or "",
            application_id=application_id or "",
            days=days
        )
        
        try:
            response = self.stub.GetAppCreditUsage(request)
            if response.success:
                print("App Credit Usage:")
                print(f"  Total Credits: {response.data.total_credits:.1f}")
                print(f"  Total Cost: ${response.data.total_cost:.2f}")
                print(f"  Time Series Points: {len(response.data.timeseries)}")
                return response.data
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def get_latest_api_requests(self, user_id: str = None, limit: int = 10):
        """Get latest API requests and events"""
        request = analytics_pb2.GetLatestApiRequestsRequest(
            user_id=user_id or "",
            limit=limit
        )
        
        try:
            response = self.stub.GetLatestApiRequests(request)
            if response.success:
                print(f"Latest API Requests ({len(response.events)} events):")
                for event in response.events[:5]:  # Show first 5
                    print(f"  {event.timestamp}: {event.type} - {event.endpoint} ({event.status})")
                return response.events
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def get_agent_performance(self, user_id: str = None, days: int = 7):
        """Get agent performance metrics"""
        request = analytics_pb2.GetAgentPerformanceRequest(
            user_id=user_id or "",
            days=days
        )
        
        try:
            response = self.stub.GetAgentPerformance(request)
            if response.success:
                print(f"Agent Performance ({len(response.performance)} agents):")
                for agent in response.performance[:3]:  # Show first 3
                    print(f"  {agent.agent_id}: {agent.success_rate:.1f}% success rate")
                return response.performance
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def record_api_request(self, endpoint: str, status: str = "success", user_id: str = None):
        """Record an API request event"""
        # Map string status to proto enum
        status_map = {
            "success": analytics_pb2.REQUEST_STATUS_SUCCESS,
            "error": analytics_pb2.REQUEST_STATUS_ERROR,
            "pending": analytics_pb2.REQUEST_STATUS_PENDING,
            "timeout": analytics_pb2.REQUEST_STATUS_TIMEOUT
        }
        
        request = analytics_pb2.RecordApiRequestRequest(
            request_type=analytics_pb2.REQUEST_TYPE_API_REQUEST,
            endpoint=endpoint,
            status=status_map.get(status, analytics_pb2.REQUEST_STATUS_SUCCESS),
            duration_ms=250,
            user_id=user_id or "test_user",
            user_email=f"{user_id or 'test_user'}@example.com",
            method="POST",
            credits_used=10.0,
            cost=0.10
        )
        
        try:
            response = self.stub.RecordApiRequest(request)
            if response.success:
                print(f"API request recorded: {response.event_id}")
                return response.event_id
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None
    
    def record_system_activity(self, title: str, activity_type: str = "info", user_id: str = None):
        """Record a system activity"""
        request = analytics_pb2.RecordSystemActivityRequest(
            activity_type=activity_type,
            title=title,
            description=f"Test activity: {title}",
            severity="info",
            status="new",
            user_id=user_id or "test_user",
            metadata=json.dumps({"source": "grpc_client", "test": True})
        )
        
        try:
            response = self.stub.RecordSystemActivity(request)
            if response.success:
                print(f"System activity recorded: {response.activity_id}")
                return response.activity_id
            else:
                print(f"Error: {response.message}")
                return None
        except grpc.RpcError as e:
            print(f"gRPC Error: {e.code()} - {e.details()}")
            return None


def demo_analytics_client():
    """Demonstrate the analytics gRPC client"""
    client = AnalyticsGRPCClient()
    
    try:
        client.connect()
        
        print("=== Dashboard Analytics gRPC Client Demo ===\n")
        
        # Record some test data
        print("1. Recording test API request...")
        client.record_api_request("/api/test", "success", "demo_user")
        
        print("\n2. Recording test system activity...")
        client.record_system_activity("Demo Activity", "inquiry", "demo_user")
        
        print("\n3. Getting dashboard metrics...")
        client.get_dashboard_metrics("demo_user", 7)
        
        print("\n4. Getting credit usage breakdown...")
        client.get_credit_usage_breakdown("demo_user", 7)
        
        print("\n5. Getting latest API requests...")
        client.get_latest_api_requests("demo_user", 5)
        
        print("\n6. Getting agent performance...")
        client.get_agent_performance("demo_user", 7)
        
        print("\n=== Demo completed ===")
        
    except Exception as e:
        print(f"Demo failed: {e}")
    finally:
        client.disconnect()


if __name__ == "__main__":
    demo_analytics_client()
