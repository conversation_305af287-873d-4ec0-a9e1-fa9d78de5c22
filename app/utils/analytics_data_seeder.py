"""
Analytics Data Seeder
Utility to seed the database with sample analytics data for testing
"""

import random
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.dashboard_analytics import (
    RequestType, RequestStatus, CreditCategory
)
from app.services.dashboard_analytics_service import DashboardAnalyticsService
from app.services.analytics_aggregation_service import AnalyticsAggregationService
import logging

logger = logging.getLogger(__name__)


class AnalyticsDataSeeder:
    """Utility class for seeding analytics data"""
    
    def __init__(self, db: Session):
        self.db = db
        self.analytics_service = DashboardAnalyticsService(db)
        self.aggregation_service = AnalyticsAggregationService(db)
    
    def seed_api_request_events(
        self,
        days: int = 7,
        events_per_day: int = 100,
        user_ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Seed API request events for testing
        
        Args:
            days: Number of days to generate data for
            events_per_day: Number of events per day
            user_ids: List of user IDs to use (generates random if None)
            
        Returns:
            List of created event IDs
        """
        if user_ids is None:
            user_ids = [f"user_{i}" for i in range(1, 6)]  # 5 test users
        
        agent_ids = [f"agent_{i}" for i in range(1, 11)]  # 10 test agents
        workflow_ids = [f"workflow_{i}" for i in range(1, 6)]  # 5 test workflows
        application_ids = [f"app_{i}" for i in range(1, 4)]  # 3 test applications
        
        endpoints = [
            "/api/agents/invoke",
            "/api/workflows/run",
            "/api/knowledge/search",
            "/api/auth/login",
            "/api/mcp/request"
        ]
        
        event_ids = []
        
        for day_offset in range(days):
            date = datetime.utcnow() - timedelta(days=day_offset)
            
            for _ in range(events_per_day):
                # Random event parameters
                request_type = random.choice(list(RequestType))
                endpoint = random.choice(endpoints)
                status = random.choices(
                    list(RequestStatus),
                    weights=[70, 20, 8, 2],  # Success, Error, Pending, Timeout
                    k=1
                )[0]
                
                user_id = random.choice(user_ids)
                user_email = f"{user_id}@example.com"
                
                # Duration based on request type
                if request_type == RequestType.WORKFLOW_EXEC:
                    duration_ms = random.randint(1000, 10000)  # 1-10 seconds
                elif request_type == RequestType.AGENT_INVOKE:
                    duration_ms = random.randint(200, 2000)  # 200ms-2s
                else:
                    duration_ms = random.randint(50, 500)  # 50-500ms
                
                # Credits and cost based on request type
                if request_type == RequestType.AGENT_INVOKE:
                    credits_used = random.uniform(10, 100)
                    cost = credits_used * 0.01  # $0.01 per credit
                elif request_type == RequestType.WORKFLOW_EXEC:
                    credits_used = random.uniform(50, 500)
                    cost = credits_used * 0.01
                elif request_type == RequestType.MCP_REQUEST:
                    credits_used = random.uniform(5, 50)
                    cost = credits_used * 0.01
                else:
                    credits_used = random.uniform(1, 10)
                    cost = credits_used * 0.01
                
                # Assign related IDs based on request type
                agent_id = random.choice(agent_ids) if request_type == RequestType.AGENT_INVOKE else None
                workflow_id = random.choice(workflow_ids) if request_type == RequestType.WORKFLOW_EXEC else None
                application_id = random.choice(application_ids) if request_type == RequestType.MCP_REQUEST else None
                
                # Add some randomness to timestamp within the day
                timestamp = date.replace(
                    hour=random.randint(0, 23),
                    minute=random.randint(0, 59),
                    second=random.randint(0, 59)
                )
                
                # Create the event
                event = self.analytics_service.record_api_request(
                    request_type=request_type,
                    endpoint=endpoint,
                    status=status,
                    duration_ms=duration_ms if status != RequestStatus.TIMEOUT else None,
                    user_id=user_id,
                    user_email=user_email,
                    method="POST",
                    ip_address=f"192.168.1.{random.randint(1, 254)}",
                    user_agent="Analytics Test Seeder",
                    agent_id=agent_id,
                    workflow_id=workflow_id,
                    application_id=application_id,
                    credits_used=credits_used,
                    cost=cost
                )
                
                # Update timestamp to the generated one
                event.timestamp = timestamp
                self.db.commit()
                
                event_ids.append(str(event.id))
        
        logger.info(f"Seeded {len(event_ids)} API request events")
        return event_ids
    
    def seed_system_activities(
        self,
        days: int = 7,
        activities_per_day: int = 5,
        user_ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Seed system activities for testing
        
        Args:
            days: Number of days to generate data for
            activities_per_day: Number of activities per day
            user_ids: List of user IDs to use
            
        Returns:
            List of created activity IDs
        """
        if user_ids is None:
            user_ids = [f"user_{i}" for i in range(1, 6)]
        
        activity_types = ["inquiry", "alert", "resolution", "maintenance"]
        severities = ["info", "warning", "error"]
        statuses = ["new", "acknowledged", "resolved"]
        
        activity_templates = [
            {
                "activity_type": "inquiry",
                "title": "New Customer Inquiry",
                "description": "Refund request #{} received from customer {}",
                "severity": "info"
            },
            {
                "activity_type": "alert",
                "title": "Resolution Time Alert",
                "description": "{} inquiries have exceeded the 10-minute SLA target",
                "severity": "warning"
            },
            {
                "activity_type": "alert",
                "title": "High Error Rate",
                "description": "Error rate has exceeded 5% in the last hour",
                "severity": "error"
            },
            {
                "activity_type": "maintenance",
                "title": "System Maintenance",
                "description": "Scheduled maintenance completed successfully",
                "severity": "info"
            }
        ]
        
        activity_ids = []
        
        for day_offset in range(days):
            date = datetime.utcnow() - timedelta(days=day_offset)
            
            for _ in range(activities_per_day):
                template = random.choice(activity_templates)
                user_id = random.choice(user_ids)
                customer_id = f"customer_{random.randint(1, 100)}"
                
                # Format description with random data
                if "{}" in template["description"]:
                    if "inquiries" in template["description"]:
                        description = template["description"].format(random.randint(3, 15))
                    else:
                        description = template["description"].format(
                            random.randint(1000, 9999),
                            customer_id
                        )
                else:
                    description = template["description"]
                
                # Random timestamp within the day
                timestamp = date.replace(
                    hour=random.randint(0, 23),
                    minute=random.randint(0, 59),
                    second=random.randint(0, 59)
                )
                
                activity = self.analytics_service.record_system_activity(
                    activity_type=template["activity_type"],
                    title=template["title"],
                    description=description,
                    severity=template["severity"],
                    status=random.choice(statuses),
                    user_id=user_id,
                    customer_id=customer_id,
                    metadata={
                        "source": "data_seeder",
                        "priority": random.choice(["low", "medium", "high"]),
                        "category": random.choice(["billing", "technical", "general"])
                    }
                )
                
                # Update timestamp
                activity.timestamp = timestamp
                self.db.commit()
                
                activity_ids.append(str(activity.id))
        
        logger.info(f"Seeded {len(activity_ids)} system activities")
        return activity_ids
    
    def seed_app_credit_usage(
        self,
        days: int = 7,
        entries_per_day: int = 24,  # Hourly entries
        user_ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Seed app credit usage data for time series visualization
        
        Args:
            days: Number of days to generate data for
            entries_per_day: Number of entries per day (hourly by default)
            user_ids: List of user IDs to use
            
        Returns:
            List of created usage IDs
        """
        if user_ids is None:
            user_ids = [f"user_{i}" for i in range(1, 6)]
        
        application_ids = [f"app_{i}" for i in range(1, 4)]
        application_names = ["Analytics Dashboard", "AI Assistant", "Workflow Engine"]
        
        usage_ids = []
        
        for day_offset in range(days):
            date = datetime.utcnow() - timedelta(days=day_offset)
            
            for hour in range(0, 24, 24 // entries_per_day):
                timestamp = date.replace(hour=hour, minute=0, second=0, microsecond=0)
                
                for i, app_id in enumerate(application_ids):
                    user_id = random.choice(user_ids)
                    
                    # Generate realistic usage patterns
                    base_credits = 50 + (i * 20)  # Different base usage per app
                    time_factor = 1 + 0.5 * abs(12 - hour) / 12  # Peak usage during work hours
                    day_factor = 1 + 0.3 * random.random()  # Daily variation
                    
                    credits_used = base_credits * time_factor * day_factor
                    cost = credits_used * 0.1  # $0.10 per credit for apps
                    
                    usage = self.analytics_service.record_app_credit_usage(
                        timestamp=timestamp,
                        credits_used=credits_used,
                        cost=cost,
                        application_id=app_id,
                        application_name=application_names[i],
                        user_id=user_id
                    )
                    
                    usage_ids.append(str(usage.id))
        
        logger.info(f"Seeded {len(usage_ids)} app credit usage entries")
        return usage_ids
    
    def run_full_seed(
        self,
        days: int = 7,
        user_ids: Optional[List[str]] = None,
        run_aggregation: bool = True
    ) -> dict:
        """
        Run complete data seeding for all analytics components
        
        Args:
            days: Number of days to generate data for
            user_ids: List of user IDs to use
            run_aggregation: Whether to run daily aggregation after seeding
            
        Returns:
            Summary of seeded data
        """
        logger.info(f"Starting full analytics data seeding for {days} days")
        
        # Seed all data types
        api_events = self.seed_api_request_events(days=days, user_ids=user_ids)
        system_activities = self.seed_system_activities(days=days, user_ids=user_ids)
        app_usage = self.seed_app_credit_usage(days=days, user_ids=user_ids)
        
        summary = {
            "days": days,
            "api_events_count": len(api_events),
            "system_activities_count": len(system_activities),
            "app_usage_count": len(app_usage),
            "total_records": len(api_events) + len(system_activities) + len(app_usage)
        }
        
        # Run aggregation if requested
        if run_aggregation:
            logger.info("Running daily aggregation for seeded data")
            start_date = datetime.utcnow() - timedelta(days=days)
            end_date = datetime.utcnow()
            
            aggregation_results = self.aggregation_service.backfill_aggregations(
                start_date=start_date,
                end_date=end_date
            )
            
            summary["aggregation_results"] = aggregation_results
        
        logger.info(f"Completed analytics data seeding: {summary}")
        return summary
