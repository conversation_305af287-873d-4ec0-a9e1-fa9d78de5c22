import json
from typing import Dict, Any, List, Optional
import structlog
from confluent_kafka import Producer
from app.core.config import settings

logger = structlog.get_logger(__name__)


class KafkaProducer:
    """Kafka producer for sending analytics events."""

    def __init__(self):
        self.logger = logger
        self.bootstrap_servers = settings.BOOTSTRAP_SERVERS
        self.producer = None

        if self.bootstrap_servers:
            try:
                self.producer = Producer({"bootstrap.servers": self.bootstrap_servers})
                self.logger.info("Kafka producer initialized")
            except Exception as e:
                self.logger.error(f"Failed to initialize Kafka producer: {str(e)}")

    def send_analytics_event(
        self, topic: str, data: Dict[str, Any], event_type: str
    ) -> Dict[str, Any]:
        """
        Send an analytics event to Kafka.

        Args:
            topic: Kafka topic to send the event to
            data: Dictionary containing event data
            event_type: Type of analytics event

        Returns:
            Dict with success status and message
        """
        if not self.producer:
            return {"success": False, "message": "Kafka producer not initialized"}

        try:
            message = {"event_type": event_type, "data": data}

            # Convert message to JSON string
            message_bytes = json.dumps(message)

            # Send message to specified Kafka topic
            future = self.producer.produce(topic=topic, value=message_bytes)

            # Block until message is sent (or timeout)
            future.get(timeout=10)

            self.logger.info(f"Analytics event published to topic '{topic}'.")
            return {"success": True, "message": f"Analytics event published to topic '{topic}'."}
        except Exception as error:
            error_msg = f"send_analytics_event error - {str(error)}"
            self.logger.error(error_msg)
            return {"success": False, "message": error_msg}
