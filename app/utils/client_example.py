import grpc
import json
import sys
from app.grpc import analytics_pb2, analytics_pb2_grpc


def track_event(stub, event_type, service_type, entity_id, user_id, metadata=None):
    """Track an analytics event."""
    request = analytics_pb2.TrackEventRequest(
        event_type=event_type,
        service_type=service_type,
        entity_id=entity_id,
        user_id=user_id,
        metadata=json.dumps(metadata) if metadata else "",
    )

    response = stub.TrackEvent(request)
    print(
        f"Track Event Response: {response.success}, {response.message}, Event ID: {response.event_id}"
    )
    return response


def get_service_metrics(stub, service_type, entity_id, time_period_days=0):
    """Get metrics for a service."""
    request = analytics_pb2.GetServiceMetricsRequest(
        service_type=service_type, entity_id=entity_id, time_period_days=time_period_days
    )

    response = stub.GetServiceMetrics(request)
    print(f"Service Metrics Response: {response.success}, {response.message}")
    if response.success:
        metrics = response.metrics
        print(f"  Entity ID: {metrics.entity_id}")
        print(f"  Usage Count: {metrics.usage_count}")
        print(f"  Average Rating: {metrics.average_rating}")
        print(f"  Rating Count: {metrics.rating_count}")

        if metrics.usage_time_series:
            print("  Usage Time Series:")
            for point in metrics.usage_time_series:
                print(f"    {point.date}: {point.count}")

    return response


def get_user_activity(stub, user_id, time_period_days=0):
    """Get user activity data."""
    request = analytics_pb2.GetUserActivityRequest(
        user_id=user_id, time_period_days=time_period_days
    )

    response = stub.GetUserActivity(request)
    print(f"User Activity Response: {response.success}, {response.message}")
    if response.success:
        activity = response.activity
        print(f"  User ID: {activity.user_id}")
        print(f"  MCP Usage Count: {activity.mcp_usage_count}")
        print(f"  Workflow Usage Count: {activity.workflow_usage_count}")
        print(f"  Agent Usage Count: {activity.agent_usage_count}")
        print(f"  MCP Creation Count: {activity.mcp_creation_count}")
        print(f"  Workflow Creation Count: {activity.workflow_creation_count}")
        print(f"  Agent Creation Count: {activity.agent_creation_count}")
        print(f"  Last Activity Date: {activity.last_activity_date}")

    return response


def get_rating_analytics(stub, service_type, entity_id="", time_period_days=0):
    """Get rating analytics."""
    request = analytics_pb2.GetRatingAnalyticsRequest(
        service_type=service_type, entity_id=entity_id, time_period_days=time_period_days
    )

    response = stub.GetRatingAnalytics(request)
    print(f"Rating Analytics Response: {response.success}, {response.message}")
    if response.success:
        analytics = response.analytics
        print(f"  Service Type: {analytics_pb2.ServiceType.Name(analytics.service_type)}")
        print(f"  Entity ID: {analytics.entity_id}")
        print(f"  Average Rating: {analytics.average_rating}")
        print(f"  Rating Count: {analytics.rating_count}")

        if analytics.rating_distribution:
            print("  Rating Distribution:")
            for rating, count in analytics.rating_distribution.items():
                print(f"    {rating}: {count}")

    return response


def run_client_example():
    """Run a client example to demonstrate the analytics service."""
    # Create a gRPC channel
    with grpc.insecure_channel("localhost:50059") as channel:
        # Create a stub (client)
        stub = analytics_pb2_grpc.AnalyticsServiceStub(channel)

        # Example 1: Track a usage event
        track_event(
            stub,
            event_type=analytics_pb2.EventType.EVENT_TYPE_USAGE,
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="example-mcp-123",
            user_id="user-456",
            metadata={"source": "client_example"},
        )

        # Example 2: Track a rating event
        track_event(
            stub,
            event_type=analytics_pb2.EventType.EVENT_TYPE_RATING,
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="example-mcp-123",
            user_id="user-456",
            metadata={"rating": 4.5, "comment": "Great MCP!"},
        )

        # Example 3: Get service metrics
        get_service_metrics(
            stub,
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="example-mcp-123",
            time_period_days=7,
        )

        # Example 4: Get user activity
        get_user_activity(stub, user_id="user-456")

        # Example 5: Get rating analytics
        get_rating_analytics(
            stub,
            service_type=analytics_pb2.ServiceType.SERVICE_TYPE_MCP,
            entity_id="example-mcp-123",
        )


if __name__ == "__main__":
    run_client_example()
