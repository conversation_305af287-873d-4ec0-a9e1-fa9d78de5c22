import enum


# Table name constants
class TableNames:
    """Table name constants for analytics service"""

    ANALYTICS_EVENTS = "analytics_events"
    SERVICE_METRICS = "service_metrics"
    USER_ACTIVITIES = "user_activities"
    APPLICATIONS = "applications"
    APPLICATION_IMAGES = "application_images"
    WEBHOOKS = "webhooks"
    WEBHOOK_LOGS = "webhook_logs"
    ACTIVATION_EVENTS = "activation_events"
    USAGE_EVENTS = "usage_events"
    APP_ANALYTICS = "app_analytics"


class EventType(str, enum.Enum):
    """Event types for analytics tracking"""

    USAGE = "usage"
    RATING = "rating"
    CREATION = "creation"
    MODIFICATION = "modification"
    DELETION = "deletion"
    LOGIN = "login"
    LOGOUT = "logout"
    ERROR = "error"
    OTHER = "other"


class ServiceType(str, enum.Enum):
    """Service types for analytics tracking"""

    MCP = "mcp"
    WORKFLOW = "workflow"
    AGENT = "agent"
    USER = "user"
    APPLICATION = "application"
    WEBHOOK = "webhook"
    OTHER = "other"


class AnalyticsStatus(str, enum.Enum):
    """Status for analytics events"""

    ACTIVE = "active"
    ARCHIVED = "archived"


class ApplicationStatus(str, enum.Enum):
    """Application status"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class WebhookStatus(str, enum.Enum):
    """Webhook status"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"


class WebhookEventType(str, enum.Enum):
    """Webhook event types"""

    AGENT_EXECUTION = "agent_execution"
    WORKFLOW_COMPLETION = "workflow_completion"
    MCP_USAGE = "mcp_usage"
    USER_ACTIVATION = "user_activation"
    API_USAGE = "api_usage"
    ERROR = "error"


class ActivationEventType(str, enum.Enum):
    """Activation event types"""

    USER_SIGNUP = "user_signup"
    FIRST_AGENT_CREATED = "first_agent_created"
    FIRST_WORKFLOW_CREATED = "first_workflow_created"
    FIRST_MCP_REGISTERED = "first_mcp_registered"
    FIRST_API_CALL = "first_api_call"
    MARKETPLACE_USAGE = "marketplace_usage"
