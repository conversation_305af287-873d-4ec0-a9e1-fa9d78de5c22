VIDEO_TEMPLATE_1 = {
    "format": ".mp4",
    "fps": 30,
    "size": {"width": 720, "height": 1280},
    "aspect_ratio": "9:16",
    "background": "#000000",
    "tracks": [
        {
            "clips": [
                {
                    "id": 1,
                    # Size Configuration
                    "width": 720,  # Width in pixels
                    "height": 680,  # Height in pixels
                    "scale": 1,  # Scale multiplier (1 = 100% original size)
                    # Position & Layout
                    "position": {"x": 0, "y": 0},
                    "fit": "none",  # How asset fits in frame:
                    "offset": {  # Fine-tune position from reference point
                        "x": 0,  # Horizontal offset (-1 to 1)
                        # Negative = left, Positive = right
                        "y": 0,  # Vertical offset (-1 to 1)
                        # Negative = down, Positive = up
                    },
                    # Rotation & Transform
                    "rotation": 0,  # Rotation in degrees
                    "opacity": 1,  # Opacity (0 to 1)
                    # Transitions
                    "transition": {
                        "in": {
                            "type": "fade",  # Transition type
                            "duration": 0.5,  # Transition duration in seconds
                        },
                        "out": {"type": "fade", "duration": 0.5},
                    },
                    # Optional Cropping
                    "crop": {
                        "top": 0,  # Crop from top (0 to 1)
                        "bottom": 0,  # Crop from bottom (0 to 1)
                        "left": 0,  # Crop from left (0 to 1)
                        "right": 0,  # Crop from right (0 to 1)
                    },
                },
            ]
        },
        {
            "avatars": [
                {
                    "width": 50,  # Width in pixels
                    "height": 50,  # Height in pixels
                    "scale": 1,  # Scale multiplier (1 = 100% original size)
                    # Position & Layout
                    "position": {"x": 600, "y": 50},
                    "fit": "none",  # How asset fits in frame:
                    "offset": {  # Fine-tune position from reference point
                        "x": -0.441,  # Horizontal offset (-1 to 1)
                        # Negative = left, Positive = right
                        "y": 0,  # Vertical offset (-1 to 1)
                        # Negative = down, Positive = up
                    },
                    # Rotation & Transform
                    "rotation": 0,  # Rotation in degrees
                    "opacity": 1,  # Opacity (0 to 1)
                    # Transitions
                    "transition": {
                        "in": {
                            "type": "fade",  # Transition type
                            "duration": 0.5,  # Transition duration in seconds
                        },
                        "out": {"type": "fade", "duration": 0.5},
                    },
                    "mask": {
                        "enabled": True,
                        "type": "image",
                        "source": None,
                        "invert": False,
                    },
                    # Optional Cropping
                    "crop": {
                        "top": 0,  # Crop from top (0 to 1)
                        "bottom": 0,  # Crop from bottom (0 to 1)
                        "left": 0,  # Crop from left (0 to 1)
                        "right": 0,  # Crop from right (0 to 1)
                    },
                },
            ],
        },
        {
            "logos": [
                {
                    "width": 1920,  # Width in pixels
                    "height": 1080,  # Height in pixels
                    "scale": 1,  # Scale multiplier (1 = 100% original size)
                    # Position & Layout
                    "position": {"x": 10, "y": 10},
                    "fit": "none",  # How asset fits in frame:
                    "offset": {  # Fine-tune position from reference point
                        "x": -0.441,  # Horizontal offset (-1 to 1)
                        # Negative = left, Positive = right
                        "y": 0,  # Vertical offset (-1 to 1)
                        # Negative = down, Positive = up
                    },
                    # Rotation & Transform
                    "rotation": 0,  # Rotation in degrees
                    "opacity": 1,  # Opacity (0 to 1)
                    # Transitions
                    "transition": {
                        "in": {
                            "type": "fade",  # Transition type
                            "duration": 0.5,  # Transition duration in seconds
                        },
                        "out": {"type": "fade", "duration": 0.5},
                    },
                    # Optional Cropping
                    "crop": {
                        "top": 0,  # Crop from top (0 to 1)
                        "bottom": 0,  # Crop from bottom (0 to 1)
                        "left": 0,  # Crop from left (0 to 1)
                        "right": 0,  # Crop from right (0 to 1)
                    },
                },
            ],
        },
        {
            "texts": [
                {
                    "width": 400,  # Width in pixels
                    "height": 200,  # Height in pixels
                    "scale": 1,  # Scale multiplier (1 = 100% original size)
                    # Position & Layout
                    "position": {"x": 0, "y": 0},
                    "fit": "none",  # How asset fits in frame:
                    "font": {
                        "size": 10,
                    },
                    "offset": {  # Fine-tune position from reference point
                        "x": 0,  # Horizontal offset (-1 to 1)
                        # Negative = left, Positive = right
                        "y": 0,  # Vertical offset (-1 to 1)
                        # Negative = down, Positive = up
                    },
                    # Rotation & Transform
                    "rotation": 0,  # Rotation in degrees
                    "opacity": 1,  # Opacity (0 to 1)
                    # Transitions
                    "transition": {
                        "in": {
                            "type": "fade",  # Transition type
                            "duration": 0.5,  # Transition duration in seconds
                        },
                        "out": {"type": "fade", "duration": 0.5},
                    },
                    # Optional Cropping
                    "crop": {
                        "top": 0,  # Crop from top (0 to 1)
                        "bottom": 0,  # Crop from bottom (0 to 1)
                        "left": 0,  # Crop from left (0 to 1)
                        "right": 0,  # Crop from right (0 to 1)
                    },
                },
            ]
        },
    ],
}
