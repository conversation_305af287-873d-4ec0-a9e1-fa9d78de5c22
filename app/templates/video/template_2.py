# Shorts template payload
SHORT_TEMPLATES = {
    "shorts_template": {
        "template_name": "shorts_brand_template",
        "aspect_ratio": {"width": 9, "height": 16},
        "resolution": {"width": 1080, "height": 1920},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 60,
                    "right": 60,
                    "width": 150,
                    "height": 150,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_content",
                "type": "video",
                "position": {
                    "bottom": 760,
                    "centerX": True,
                    "width": 400,
                    "height": 400,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1080,
                    "height": 1920,
                    "aspectRatio": "9:16",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {
                    "x": 0,
                    "y": 0,
                    "centerX": True,
                },
                "mask": {
                    "enabled": False,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
    "template_2": {
        "template_name": "tiktok_brand_template",
        "aspect_ratio": {"width": 9, "height": 16},
        "resolution": {"width": 1080, "height": 1920},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 60,
                    "right": 60,
                    "width": 150,
                    "height": 150,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_content",
                "type": "video",
                "position": {
                    "bottom": 100,
                    "centerX": True,
                    "width": 400,
                    "height": 400,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "stock_footage",
                "type": "video",
                "position": {
                    "top": 0,
                    "centerX": True,
                    "width": 600,
                    "height": 400,
                },
                "fit": "cover",
                "zIndex": 1,
            },
        ],
    },
    "template_youtube": {
        "template_name": "youtube_brand_template",
        "aspect_ratio": {"width": 16, "height": 9},
        "resolution": {"width": 1920, "height": 1080},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 50,
                    "right": 50,
                    "width": 120,
                    "height": 120,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_content",
                "type": "video",
                "position": {
                    "bottom": 50,
                    "centerX": True,
                    "left": 50,  # Move to the left side
                    "width": 400,
                    "height": 225,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1280,
                    "height": 720,
                    "aspectRatio": "16:9",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {
                    "x": 320,
                    "y": 180,
                    "centerX": True,
                },
                "mask": {
                    "enabled": True,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
    "template_linkedin_square": {
        "template_name": "linkedin_brand_template_square",
        "aspect_ratio": {"width": 1, "height": 1},
        "resolution": {"width": 1080, "height": 1080},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 40,
                    "right": 40,
                    "width": 100,
                    "height": 100,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_content",
                "type": "video",
                "position": {
                    "bottom": 50,
                    "centerX": True,
                    "left": 50,  # Move to the left side
                    "width": 300,
                    "height": 168,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1080,
                    "height": 1080,
                    "aspectRatio": "1:1",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {"x": 0, "y": 0, "centerX": True},
                "mask": {
                    "enabled": True,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
}
