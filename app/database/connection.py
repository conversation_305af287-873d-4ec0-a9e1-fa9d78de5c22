from pymongo import MongoClient
import certifi

from config import MONGO_URI


# Get the path to the certificate authority bundle file
ca = certifi.where()

client = MongoClient(
    MONGO_URI,
    tlsCAFile=ca,
)


# Define a singleton class for MongoDB connection
class MongoDBConnection:
    # Class variable to hold the single instance of MongoDBConnection
    _instance = None

    def __new__(cls, *args, **kwargs):
        # Check if an instance already exists
        if not cls._instance:
            # If not, create a new instance and establish a connection
            cls._instance = super(MongoDBConnection, cls).__new__(cls, *args, **kwargs)
            # Get the MongoDB URI from the environment variables and establish a connection
            cls._instance._connection = MongoClient(
                MONGO_URI,
                tlsCAFile=ca,
                # ssl_cert_reqs=ssl.CERT_NONE,
                # maxPoolSize=50,  # Adjust pool size as needed
                # socketTimeoutMS=5000,  # 5 seconds timeout
                # connectTimeoutMS=5000,  # 5 seconds timeout
                # serverSelectionTimeoutMS=5000,  # 5 seconds timeout
                # ssl=True,  # Enable SSL
            )
        # Return the existing or new instance
        return cls._instance

    @property
    def connection(self):
        # Return the MongoDB connection object
        return self._instance._connection
