from sqlite3.dbapi2 import Timestamp
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header, Query
from typing import Optional
from datetime import datetime
from google.protobuf.timestamp_pb2 import Timestamp

from app.schemas.payment import (
    CreateCheckoutSessionRequestSchema,
    CreateCheckoutSessionResponseSchema,
    CreateCustomerPortalSessionRequestSchema,
    CreateCustomerPortalSessionResponseSchema,
    SubscriptionResponseSchema,
    CancelSubscriptionRequestSchema,
    GenericResponseSchema,
    DeductCreditsRequestSchema,
    DeductCreditsResponseSchema,
    CreditBalanceResponseSchema,
    GetTokenUsageResponseSchema,
    PaymentPlanCreateSchema,
    PaymentPlanSchema,
    CalculateCreditsRequestSchema,
    CalculateCreditsResponseSchema,
    # Topup schemas
    TopupPlanSchema,
    CreateTopupCheckoutSessionRequestSchema,
    CreateTopupCheckoutSessionResponseSchema,
    ListTopupPlansResponseSchema,
    TopupPlanCreateSchema,
)
from app.services.payment_service import PaymentServiceClient
from app.core.auth_guard import role_required
from app.grpc_ import payment_pb2
from typing import List

payment_service = PaymentServiceClient()
payment_router = APIRouter(prefix="/payments", tags=["Payments"])

def proto_timestamp_to_datetime(ts) -> Optional[datetime]:
    """Convert protobuf timestamp to datetime, handling None and empty timestamps"""
    if ts is None:
        return None
    
    # Check if timestamp has any meaningful value
    if not hasattr(ts, 'seconds') or not hasattr(ts, 'nanos'):
        return None
        
    # Check for the default "zero" timestamp
    if ts.seconds == 0 and ts.nanos == 0:
        return None
        
    try:
        # Convert to datetime and remove timezone info
        dt = ts.ToDatetime()
        return dt.replace(tzinfo=None)
    except Exception as e:
        # Handle potential conversion errors if the timestamp is invalid
        print(f"Error converting timestamp: {e}, ts: {ts}")
        return None

@payment_router.post(
    "/plans",
    response_model=PaymentPlanSchema,
    summary="Create a new payment plan",
    dependencies=[Depends(role_required(["admin"]))],
)
async def create_plan(plan_data: PaymentPlanCreateSchema):
    grpc_response = await payment_service.create_plan(plan_data.dict())
    return grpc_response

@payment_router.get(
    "/plans",
    response_model=List[PaymentPlanSchema],
    summary="List all payment plans",
)
async def list_plans():
    grpc_response = await payment_service.list_plans()
    return grpc_response.plans

@payment_router.post(
    "/checkout",
    response_model=CreateCheckoutSessionResponseSchema,
    summary="Create Stripe Checkout Session",
)
async def create_checkout_session(
    request_data: CreateCheckoutSessionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.create_checkout_session(
        org_id=request_data.organisation_id,
        plan_id_code=request_data.plan_id_code,
        success_url=request_data.success_url,
        cancel_url=request_data.cancel_url,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.post(
    "/customer-portal",
    response_model=CreateCustomerPortalSessionResponseSchema,
    summary="Create Stripe Customer Portal Session",
)
async def create_customer_portal(
    request_data: CreateCustomerPortalSessionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.create_customer_portal_session(
        org_id=request_data.organisation_id,
        return_url=request_data.return_url,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.get(
    "/subscription/{organisation_id}",
    response_model=SubscriptionResponseSchema,
    summary="Get Subscription Details",
)
async def get_subscription(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    print(f"Debug - organisation_id: {organisation_id}")
    grpc_response = await payment_service.get_subscription(org_id=organisation_id)
    print(f"Debug - grpc_response: {grpc_response}")
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=grpc_response.error_message)
    
    sub = grpc_response.subscription
    start_date = proto_timestamp_to_datetime(sub.current_period_start)
    end_date = proto_timestamp_to_datetime(sub.current_period_end)

    response_data = {
        "id": sub.id,
        "organisation_id": sub.organisation_id,
        "plan_id_code": sub.plan_id_code.upper(),
        "status": payment_pb2.SubscriptionStatus.Name(sub.status),
        "current_period_start": start_date.isoformat() if isinstance(start_date, datetime) else None,
        "current_period_end": end_date.isoformat() if isinstance(end_date, datetime) else None,
        "subscription_credits": sub.subscription_credits,
        "current_credits": sub.current_credits,
    }
    
    print(f"Debug - response_data: {response_data}")
    return response_data

@payment_router.post(
    "/subscription/cancel",
    response_model=GenericResponseSchema,
    summary="Cancel Subscription",
)
async def cancel_subscription(
    request_data: CancelSubscriptionRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.cancel_subscription(org_id=request_data.organisation_id)
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.post(
    "/credits/deduct",
    response_model=DeductCreditsResponseSchema,
    summary="Deduct Credits for Token Usage",
)
async def deduct_credits(
    request_data: DeductCreditsRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.deduct_credits(
        org_id=request_data.organisation_id,
        user_id=current_user.get("user_id"),
        agent_id=request_data.agent_id,
        input_tokens=request_data.input_tokens,
        output_tokens=request_data.output_tokens,
        consumed_credits=request_data.consumed_credits,
        description=request_data.description,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=grpc_response.error_message)
    return DeductCreditsResponseSchema(
        success=grpc_response.success,
        new_balance=grpc_response.new_balance_after_deduction,
        error_message=grpc_response.error_message
    )

@payment_router.get(
    "/credits/{organisation_id}",
    response_model=CreditBalanceResponseSchema,
    summary="Get Credit Balance",
)
async def get_credit_balance(
    organisation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    grpc_response = await payment_service.get_credit_balance(org_id=organisation_id)
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=grpc_response.error_message)
    return grpc_response

@payment_router.get(
    "/token-usage/{organisation_id}",
    response_model=GetTokenUsageResponseSchema,
    summary="Get Token Usage Logs",
)
async def get_token_usage(
    organisation_id: str,
    user_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: dict = Depends(role_required(["user"])),
):
    print("Debug - Payment Router: get_token_usage called")
    grpc_response = await payment_service.get_token_usage(
        org_id=organisation_id,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=grpc_response.error_message)
    return grpc_response

@payment_router.post(
    "/stripe/webhook",
    response_model=GenericResponseSchema,
    summary="Handle Stripe Webhooks",
)
async def handle_stripe_webhook(
    request: Request,
    stripe_signature: Optional[str] = Header(None, alias="Stripe-Signature"),
):
    if not stripe_signature:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Missing Stripe-Signature header.")

    payload = await request.body()
    grpc_response = await payment_service.handle_stripe_webhook(
        payload=payload,
        signature=stripe_signature,
    )
    if not grpc_response.success:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=grpc_response.error_message)
    return GenericResponseSchema(success=True, message="Webhook processed successfully.")

@payment_router.post(
    "/calculate-credits",
    response_model=CalculateCreditsResponseSchema,
    summary="Calculate Credits for Token Usage",
)
async def calculate_credits(
    request_data: CalculateCreditsRequestSchema,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Calculate credits based on model pricing information.

    This endpoint accepts model information (model_name, provider_name) and token counts,
    fetches the pricing details from the provider service, and returns the calculated
    credits and cost in USD.
    """
    # Import provider service here to avoid circular imports
    from app.services.provider_service import ModelServiceClient

    # Create model service client to fetch pricing information
    model_service = ModelServiceClient()

    try:
        # Get all models to find the one matching model_name and provider_name
        models_response = await model_service.list_models(page=1, page_size=1000)

        if not models_response.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch models from provider service"
            )

        # Find the model that matches both model_name and provider_name
        target_model = None
        for model in models_response.models:
            if (model.model.lower() == request_data.model_name.lower() and
                model.provider.provider.lower() == request_data.provider_name.lower()):
                target_model = model
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model '{request_data.model_name}' not found for provider '{request_data.provider_name}'"
            )

        # Extract pricing information
        input_price_per_token = target_model.inputPricePerToken
        output_price_per_token = target_model.outputPricePerToken

        # Call payment service to calculate credits
        grpc_response = await payment_service.calculate_credits(
            input_tokens=request_data.input_tokens,
            output_tokens=request_data.output_tokens,
            input_price_per_token=input_price_per_token,
            output_price_per_token=output_price_per_token,
        )

        if not grpc_response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=grpc_response.error_message)

        return CalculateCreditsResponseSchema(
            success=grpc_response.success,
            error_message=grpc_response.error_message,
            credits=grpc_response.credits,
            cost_in_usd=grpc_response.cost_in_usd,
            input_price_per_token=grpc_response.input_price_per_token,
            output_price_per_token=grpc_response.output_price_per_token,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )
    finally:
        model_service.close()


# --- Topup Plan Routes ---

@payment_router.get("/topup-plans", response_model=ListTopupPlansResponseSchema)
async def list_topup_plans(
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    """List all available topup plans."""
    try:
        grpc_response = await payment_service.list_topup_plans()

        topup_plans = []
        for plan in grpc_response.topup_plans:
            topup_plans.append(TopupPlanSchema(
                id=plan.id,
                plan_id_code=plan.plan_id_code,
                name=plan.name,
                credit_amount=plan.credit_amount,
                price=plan.price,
                stripe_price_id=plan.stripe_price_id,
            ))

        return ListTopupPlansResponseSchema(
            topup_plans=topup_plans,
            success=grpc_response.success,
            error_message=grpc_response.error_message if not grpc_response.success else None,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )


@payment_router.post("/topup/checkout", response_model=CreateTopupCheckoutSessionResponseSchema)
async def create_topup_checkout_session(
    request: CreateTopupCheckoutSessionRequestSchema,
    current_user: dict = Depends(role_required(["user", "admin"]))
):
    """Create a checkout session for topup purchase."""
    try:
        grpc_response = await payment_service.create_topup_checkout_session(
            org_id=request.organisation_id,
            topup_plan_id_code=request.topup_plan_id_code,
            success_url=request.success_url,
            cancel_url=request.cancel_url,
        )

        return CreateTopupCheckoutSessionResponseSchema(
            checkout_url=grpc_response.checkout_url,
            session_id=grpc_response.session_id,
            success=grpc_response.success,
            error_message=grpc_response.error_message if not grpc_response.success else None,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )


@payment_router.post("/topup-plans", response_model=TopupPlanSchema)
async def create_topup_plan(
    request: TopupPlanCreateSchema,
    current_user: dict = Depends(role_required(["admin"]))
):
    """Create a new topup plan (admin only)."""
    try:
        plan_data = {
            "name": request.name,
            "credit_amount": request.credit_amount,
            "price": request.price,
            "stripe_price_id": request.stripe_price_id,
        }

        grpc_response = await payment_service.create_topup_plan(plan_data)

        return TopupPlanSchema(
            id=grpc_response.id,
            plan_id_code=grpc_response.plan_id_code,
            name=grpc_response.name,
            credit_amount=grpc_response.credit_amount,
            price=grpc_response.price,
            stripe_price_id=grpc_response.stripe_price_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )
