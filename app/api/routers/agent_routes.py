import asyncio
from typing import Optional
from app.services.google_drive_service import GoogleDriveServiceClient
from app.services.mcp_service import MCPServiceClient
from app.services.workflow_service import WorkflowServiceClient
from app.utils.GCSUtility.gcs import GCSUtility
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.agent_service import AgentServiceClient
from app.services.agent_graph_service import AgentGraphServiceClient
from app.schemas.agent import (
    AgentCoreDetailsUpdatePayload,
    AgentKnowledgeUpdatePayload,
    AgentMcpServersUpdatePayload,
    AgentPartUpdateResponseAPI,
    AgentSettingsUpdatePayload,
    AgentWorkflowsUpdatePayload,
    AgentCapabilitiesUpdatePayload,
    CategoryEnum,
    CreateAgentFromTemplateResponse,
    PaginatedWorkflowResponse,
    PaginationMetadata,
    PreSignedUrlRequest,
    PreSignedUrlResponse,
    StatusEnum,
    ToggleAgentVisibilityResponseAPI,
    AgentResponse,
    Agent<PERSON><PERSON>,
    AgentInDB,
    DeleteAgentResponse,
    AgentWithMCPsResponse,
    AgentWithMCPsInDB,
    VisibilityEnum,
    AgentVariablesUpdatePayload,
    ListAgentVersionsResponse,
    GetAgentVersionResponse,
    SwitchAgentVersionResponse,
    CreateAgentVersionRequest,
    CreateAgentVersionResponse,
    AgentVersionInDB,
    AgentsByIdsRequest,
    AgentsByIdsResponse,
)
from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import validate_agent_platform_auth_key
import json

from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
from app.api.routers.workflow_routes import prepare_workflow_dict

agent_router = APIRouter(prefix="/agents", tags=["agents"])
agent_service = AgentServiceClient()
agent_graph_service = AgentGraphServiceClient()
user_service = UserServiceClient()
mcp_service = MCPServiceClient()
workflow_service = WorkflowServiceClient()
google_drive_service = GoogleDriveServiceClient()


@agent_router.post("", response_model=AgentResponse)
async def create_agent(
    agent_data: AgentCreate, current_user: dict = Depends(role_required(["user"]))
):
    try:
        print(f"[DEBUG] Agent data: {agent_data}")
        # Validate user and get user details
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]
        organization_id = current_user["organisation_id"]

        capabilities_payload = None
        if agent_data.capabilities_data:
            capabilities_payload = agent_data.capabilities_data.model_dump(exclude_none=True)

        variables_payload_for_service = None
        if agent_data.variables:  # agent_data.variables is List[AgentVariableCreate]
            variables_payload_for_service = [
                var.model_dump(exclude_none=True, by_alias=False) for var in agent_data.variables
            ]

        response = await agent_service.createAgent(
            name=agent_data.name,
            description=agent_data.description,
            avatar=agent_data.avatar,
            owner_type=current_user["role"],
            system_message=agent_data.system_message,
            model_provider=agent_data.model_provider,
            model_name=agent_data.model_name,
            temperature=agent_data.temperature,
            max_tokens=agent_data.max_tokens,
            workflow_ids=agent_data.workflow_ids,
            mcp_server_ids=agent_data.mcp_server_ids,
            agent_topic_type=agent_data.agent_topic_type,
            visibility=agent_data.visibility,
            tags=agent_data.tags,
            owner_details=user_details,
            department=agent_data.department,
            organization_id=organization_id,
            tone=agent_data.tone,
            files=agent_data.files,
            urls=agent_data.urls,
            is_a2a=agent_data.is_a2a,
            is_customizable=agent_data.is_customizable,
            capabilities_data=capabilities_payload,
            example_prompts=agent_data.example_prompts,
            category=agent_data.category,
            variables_data=variables_payload_for_service,
            status=agent_data.status,
        )

        if not response.success:
            print(f"[ERROR] Agent not found: {response.message}")
            raise HTTPException(status_code=404, detail=response.message)

        agent_dict = MessageToDict(response.agent, preserving_proto_field_name=True)

        print(f"[DEBUG] Agent data converted to dict: {response.agent}")

        url_list = agent_dict.get("urls") or []
        print(f"[url list] {url_list}")

        file_entries = agent_dict.get("files") or []
        print(f"[file_entries] {file_entries}")

        # Combine both
        all_urls = url_list + file_entries
        if len(all_urls) >= 1:
            try:
                asyncio.create_task(
                    google_drive_service.sync_file_by_url(
                        drive_urls=all_urls,
                        agent_id=agent_dict.get("id"),
                        organisation_id=agent_dict.get("organization_id"),
                        user_id=current_user["user_id"],
                    )
                )
                print(f"[DEBUG] Background sync task triggered for URLS: {all_urls}")
            except Exception as sync_err:
                print(f"[WARNING] Failed to trigger background sync for URLS {all_urls}: {sync_err}")

        # After successful agent creation, create the agent in the graph service
        try:
            print(f"[DEBUG] Creating agent in graph service with ID: {agent_dict.get('id')}")
            graph_response = await agent_graph_service.create_agent(
                agent_id=agent_dict.get("id"),
                name=agent_data.name,
                description=agent_data.description,
                department=agent_data.department or "",
                owner_id=current_user["user_id"],
                user_ids=[current_user["user_id"]],  # Initially, only the creator has access
                visibility=agent_data.visibility or "private",
                status=agent_data.status or "active",
                creator_role=current_user["role"],
            )

            if graph_response.success:
                print(
                    f"[DEBUG] Agent successfully created in graph service: {graph_response.message}"
                )
            else:
                print(
                    f"[WARNING] Failed to create agent in graph service: {graph_response.message}"
                )

        except Exception as graph_error:
            # Log the error but don't fail the main agent creation
            print(f"[WARNING] Exception while creating agent in graph service: {str(graph_error)}")

        return AgentResponse(
            success=response.success, message=response.message, agent=AgentInDB(**agent_dict)
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@agent_router.get("/{agent_id}", response_model=AgentWithMCPsResponse)
async def get_agent(agent_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Retrieve an agent by its ID.

    :param agent_id: The unique identifier of the agent to retrieve.
    :type agent_id: int
    :param current_user: The currently authenticated user, extracted using dependency injection.
    :type current_user: dict

    :raises HTTPException 404: If the agent is not found.
    :raises HTTPException 500: If an unexpected server error occurs.

    :return: A response containing the agent details if found.
    :rtype: AgentResponse
    """
    print(f"[DEBUG] get_agent route called with agent_id: {agent_id}")
    try:
        response = await agent_service.getAgentById(agent_id=agent_id)
        print(
            f"[DEBUG] Service response received: success={response.success}, message={response.message}"
        )

        if not response.success:
            print(f"[ERROR] Agent not found: {response.message}")
            raise HTTPException(status_code=404, detail=response.message)

        agent_dict = MessageToDict(response.agent, preserving_proto_field_name=True)
        print(f"[DEBUG] Agent data converted to dict: {agent_dict}")

        mcp_ids = agent_dict.get("mcp_server_ids")
        mcps = []

        if mcp_ids:
            try:
                mcp_response = await mcp_service.getMCPsByIds(mcp_ids)
                print(f"[DEBUG] MCPs response: {mcp_response}")

                if hasattr(mcp_response, "mcps"):
                    for mcp in mcp_response.mcps:
                        mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

                        mcps.append(mcp_dict)
            except Exception as e:
                print(f"[WARNING] Error fetching MCPs in get_agent_for_agent_platform: {str(e)}")

        # Add MCPs to agent dict
        agent_dict["mcps"] = mcps

        workflow_ids = agent_dict.get("workflow_ids")
        workflows = []
        if workflow_ids:

            try:
                workflow_response = await workflow_service.get_workflows_by_ids(workflow_ids)
                print(f"[DEBUG] Workflows response: {workflow_response}")

                if hasattr(workflow_response, "workflows"):
                    for workflow in workflow_response.workflows:
                        workflow_dict = prepare_workflow_dict(
                            MessageToDict(workflow, preserving_proto_field_name=True)
                        )
                        workflows.append(workflow_dict)
            except Exception as e:
                print(
                    f"[WARNING] Error fetching workflows in get_agent_for_agent_platform: {str(e)}"
                )
                raise HTTPException(status_code=404, detail=response.message)

        agent_dict["workflows"] = workflows

        return AgentWithMCPsResponse(
            success=response.success,
            message=response.message,
            agent=AgentWithMCPsInDB(**agent_dict),
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in get_agent: {str(e)}")
        error_details = parse_error(str(e))
        print(f"[DEBUG] Parsed error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.get(
    "/agent-platform/{agent_id}",
    response_model=AgentWithMCPsResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_agent_for_agent_platform(agent_id: str):
    """
    Retrieve an agent by its ID for the agent platform.

    This endpoint fetches an agent's details using its unique identifier. Access to this endpoint
    requires a valid agent platform authentication key.

    :param agent_id: The unique identifier of the agent to retrieve.
    :type agent_id: int

    :raises HTTPException 404: If the agent is not found.
    :raises HTTPException 500: If an unexpected server error occurs.

    :return: A response containing the agent details if found.
    :rtype: AgentWithMCPsResponse
    """
    try:
        response = await agent_service.getAgentById(agent_id=agent_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        agent_dict = MessageToDict(response.agent, preserving_proto_field_name=True)
        print(f"[DEBUG] Agent data converted to dict: {json.dumps(agent_dict, indent=2)}")

        mcp_ids = agent_dict.get("mcp_server_ids")
        mcps = []
        if mcp_ids:
            try:
                mcp_response = await mcp_service.getMCPsByIds(mcp_ids)
                print(f"[DEBUG] MCPs response: {mcp_response}")

                if hasattr(mcp_response, "mcps"):
                    for mcp in mcp_response.mcps:
                        mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

                        mcps.append(mcp_dict)
            except Exception as e:
                print(f"[WARNING] Error fetching MCPs in get_agent_for_agent_platform: {str(e)}")
                raise HTTPException(status_code=404, detail=response.message)

        # Add MCPs to agent dict
        agent_dict["mcps"] = mcps

        workflow_ids = agent_dict.get("workflow_ids")
        workflows = []
        if workflow_ids:

            try:
                workflow_response = await workflow_service.get_workflows_by_ids(workflow_ids)
                print(f"[DEBUG] Workflows response: {workflow_response}")

                if hasattr(workflow_response, "workflows"):
                    for workflow in workflow_response.workflows:
                        workflow_dict = prepare_workflow_dict(
                            MessageToDict(workflow, preserving_proto_field_name=True)
                        )
                        workflows.append(workflow_dict)
            except Exception as e:
                print(
                    f"[WARNING] Error fetching workflows in get_agent_for_agent_platform: {str(e)}"
                )
                raise HTTPException(status_code=404, detail=response.message)

        agent_dict["workflows"] = workflows

        print(f"[DEBUG] Agent data with MCPs and workflows: {json.dumps(agent_dict, indent=2)}")
        return AgentWithMCPsResponse(
            success=response.success,
            message=response.message,
            agent=AgentWithMCPsInDB(**agent_dict),
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in get_agent_for_agent_platform: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post(
    "/agent-platform/by-ids",
    response_model=AgentsByIdsResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_agents_by_ids_for_agent_platform(request: AgentsByIdsRequest):
    """
    Retrieve multiple agents by their IDs for the agent platform.

    This endpoint fetches multiple agents' details using their unique identifiers. Access to this endpoint
    requires a valid agent platform authentication key.

    :param request: The request containing the list of agent IDs to retrieve.
    :type request: AgentsByIdsRequest

    :raises HTTPException 400: If the request is invalid or an error occurs.
    :raises HTTPException 500: If an unexpected server error occurs.

    :return: A response containing the list of agents if found.
    :rtype: AgentsByIdsResponse
    """
    try:
        response = await agent_service.getAgentsByIds(agent_ids=request.ids)
        
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        # Convert protobuf agents to dict format
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            agents.append(AgentInDB(**agent_dict))

        return AgentsByIdsResponse(
            success=response.success,
            message=response.message,
            agents=agents,
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in get_agents_by_ids_for_agent_platform: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.delete("/{agent_id}", response_model=DeleteAgentResponse)
async def delete_agent(agent_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Delete an agent by its ID.

    This endpoint allows users with the "user" role to delete an agent based on its unique identifier.

    :param agent_id: The unique identifier of the agent to be deleted.
    :type agent_id: int
    :param current_user: The authenticated user performing the deletion (requires "user" role).
    :type current_user: dict

    :raises HTTPException 404: If the agent is not found.
    :raises HTTPException 500: If an unexpected server error occurs.

    :return: A response indicating whether the deletion was successful.
    :rtype: DeleteAgentResponse"
    """
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        response = await agent_service.deleteAgentById(
            agent_id=agent_id, owner_details=user_details
        )

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        return DeleteAgentResponse(success=response.success, message=response.message)

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.get("", response_model=PaginatedWorkflowResponse)
async def list_agents(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    department: Optional[str] = Query(None, description="Filter by department"),
    status: Optional[str] = Query(None, description="Filter by status (active/inactive)"),
    visibility: Optional[str] = Query(None, description="Filter by visibility (public/private)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    current_user: dict = Depends(role_required(["user", "admin"])),
    organization_id: Optional[str] = Query(None, description="Filter by organization ID"),
    is_bench_employee: Optional[bool] = Query(None, description="Filter by bench employee"),
    is_a2a: Optional[bool] = Query(None, description="Filter by A2A agent"),
    is_customizable: Optional[bool] = Query(None, description="Filter by customizable agent"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
):
    """
    Retrieve a paginated list of agents.

    This endpoint allows administrators to fetch a paginated list of agents, with metadata
    about total pages, current page, and navigation details.

    :param page: The page number to retrieve (must be >= 1).
    :type page: int
    :param page_size: The number of agents per page (must be between 1 and 100).
    :type page_size: int
    :param current_user: The authenticated admin user performing the request.
    :type current_user: dict

    :raises HTTPException 500: If an unexpected server error occurs.

    :return: A paginated response containing a list of agents and pagination metadata.
    :rtype: PaginatedWorkflowResponse"
    """
    try:
        owner_id_filter: Optional[str] = None
        if current_user.get("role") == "user":
            owner_id_filter = current_user.get("user_id")
            if not owner_id_filter:
                raise HTTPException(status_code=403, detail="User ID not found for user role.")

        if status and status not in [s.value for s in StatusEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        if visibility and visibility not in [v.value for v in VisibilityEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid visibility: {visibility}")

        if category and category not in [c.value for c in CategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        response = await agent_service.list_agents(
            page=page,
            page_size=page_size,
            department=department,
            status=status,
            visibility=visibility,
            user_id=owner_id_filter,
            organization_id=organization_id,
            is_bench_employee=is_bench_employee,
            is_a2a=is_a2a,
            is_customizable=is_customizable,
            category=category,
            search=search,
        )

        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)
            agents.append(AgentInDB(**agent_dict))  # Convert to Pydantic model

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=page_size,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )
        print(f"[DEBUG] Returning agents: {agents}")

        return PaginatedWorkflowResponse(data=agents, metadata=metadata)

    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in list_agents: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.get(
    "/getAgentsByUserId/{user_id}",
    response_model=PaginatedWorkflowResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_list_of_agents_by_user_id(
    user_id: str,
    page: int = Query(1, description="Page number"),
    page_size: int = Query(10, description="Page size"),
):
    """
    Retrieve a paginated list of agents owned by a specific user for agent platform.

    This endpoint fetches agents associated with a given user, ensuring that
    only authorized users can access the data. The response includes pagination
    metadata to facilitate frontend integration.

    Args:
        user_id (dict): The authenticated user's ID extracted from the token.
        page (int, optional): The page number for pagination. Defaults to 1.
        page_size (int, optional): The number of agents per page. Defaults to 10.

    Returns:
        PaginatedWorkflowResponse: A response object containing the list of agents
        and pagination metadata.

    Raises:
        HTTPException:
            - 500: If an internal server error occurs during processing."
    """
    try:
        response = await agent_service.listAgentByUserId(
            owner_id=user_id, page=page, page_size=page_size
        )
        print(f"[DEBUG] Service response received: success={response.agents}")
        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)

            if "visibility" in agent_dict:  # Add a check
                agent_dict["visibility"] = agent_dict["visibility"].lower()

            if "category" in agent_dict:  # Add a check
                agent_dict["category"] = agent_dict["category"].lower()

            if "status" in agent_dict:  # Add a check
                agent_dict["status"] = agent_dict["status"].lower()

            agents.append(AgentInDB(**agent_dict))  # Convert to Pydantic model

        print(f"[DEBUG] Returning agents: {agents}")

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=page_size,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )

        return PaginatedWorkflowResponse(data=agents, metadata=metadata)

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post("/media/presigned-url", response_model=PreSignedUrlResponse)
async def generate_presigned_url(
    payload: PreSignedUrlRequest, current_user: dict = Depends(role_required(["user"]))
):
    try:
        gcs = GCSUtility()

        result = gcs.generate_presigned_url(
            file_name=payload.fileName, file_type=payload.fileType, file_path=payload.filePath
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["message"])

        return PreSignedUrlResponse(success=result["success"], url=result["url"])
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.patch("/{agent_id}/core-details", response_model=AgentPartUpdateResponseAPI)
async def update_agent_core_details_api(
    agent_id: str,
    payload: AgentCoreDetailsUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),  # Your auth
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=400, detail="No fields provided for core details update."
            )

        print(f"[DEBUG] Update data: {update_data}")
        grpc_response = await agent_service.update_agent_core_details(
            agent_id=agent_id, owner_details=user_details, update_payload=update_data
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        agent_dict = MessageToDict(grpc_response.agent, preserving_proto_field_name=True)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            agent=AgentInDB(**agent_dict),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_core_details_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.patch("/{agent_id}/knowledge", response_model=AgentPartUpdateResponseAPI)
async def update_agent_knowledge_api(
    agent_id: str,
    payload: AgentKnowledgeUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=400, detail="No knowledge fields (files/urls) provided for update."
            )

        grpc_response = await agent_service.update_agent_knowledge(
            agent_id=agent_id, owner_details=user_details, update_payload=update_data
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        agent_dict = MessageToDict(grpc_response.agent, preserving_proto_field_name=True)

        url_list = agent_dict.get("urls") or []
        print(f"[url list] {url_list}")

        file_entries = agent_dict.get("files") or []
        print(f"[file_entries] {file_entries}")

        # Combine both
        all_urls = url_list + file_entries
        if len(all_urls) >= 1:
            try:
                asyncio.create_task(
                    google_drive_service.sync_file_by_url(
                        drive_urls=all_urls,
                        agent_id=agent_dict.get("id"),
                        organisation_id=agent_dict.get("organization_id"),
                        user_id=current_user["user_id"],
                    )
                )
                print(f"[DEBUG] Background sync task triggered for URLS: {all_urls}")
            except Exception as sync_err:
                print(f"[WARNING] Failed to trigger background sync for URLS {all_urls}: {sync_err}")

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            agent=AgentInDB(**agent_dict),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_knowledge_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.patch("/{agent_id}/capabilities", response_model=AgentPartUpdateResponseAPI)
async def update_agent_capabilities_api(
    agent_id: str,
    payload: AgentCapabilitiesUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=400, detail="No capabilities fields provided for update."
            )

        grpc_response = await agent_service.update_agent_capabilities(
            agent_id=agent_id, owner_details=user_details, update_payload=update_data
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        agent_dict = MessageToDict(grpc_response.agent, preserving_proto_field_name=True)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            agent=AgentInDB(**agent_dict),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_capabilities_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.put("/{agent_id}/mcp-servers", response_model=AgentPartUpdateResponseAPI)
async def update_agent_mcp_servers_api(
    agent_id: str,
    payload: AgentMcpServersUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        grpc_response = await agent_service.update_agent_mcp_servers(
            agent_id=agent_id, owner_details=user_details, mcp_server_ids=payload.mcp_server_ids
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.patch("/{agent_id}/variables", response_model=AgentPartUpdateResponseAPI)
async def update_agent_variables_api(
    agent_id: str,
    payload: AgentVariablesUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)
        if not update_data or "variables" not in update_data:
            raise HTTPException(status_code=400, detail="No variables provided for update.")
        print("going to agent service in api gateway to update variables")
        grpc_response = await agent_service.update_agent_variables(
            agent_id=agent_id, owner_details=user_details, variables=update_data["variables"]
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        agent_dict = MessageToDict(grpc_response.agent, preserving_proto_field_name=True)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            agent=AgentInDB(**agent_dict),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_variables_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.put("/{agent_id}/workflows", response_model=AgentPartUpdateResponseAPI)
async def update_agent_workflows_api(
    agent_id: str,
    payload: AgentWorkflowsUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        grpc_response = await agent_service.update_agent_workflows(
            agent_id=agent_id, owner_details=user_details, workflow_ids=payload.workflow_ids
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_core_details_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.patch("/{agent_id}/settings", response_model=AgentPartUpdateResponseAPI)
async def update_agent_settings_api(
    agent_id: str,
    payload: AgentSettingsUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for settings update.")

        grpc_response = await agent_service.update_agent_settings(
            agent_id=agent_id, owner_details=user_details, update_payload=update_data
        )
        if not grpc_response.success:
            raise HTTPException(status_code=400, detail=grpc_response.message)

        return AgentPartUpdateResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_core_details_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post("/{agent_id}/toggle-visibility", response_model=ToggleAgentVisibilityResponseAPI)
async def toggle_agent_visibility_api(
    agent_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response.get("success"):
            raise HTTPException(status_code=400, detail=validate_response.get("message"))
        user_details = validate_response["user"]

        grpc_response = await agent_service.toggle_agent_visibility(
            agent_id=agent_id, owner_details=user_details
        )
        # ... (Error handling and response conversion like in workflow service's toggle) ...
        if not grpc_response.success:
            # Map gRPC error to HTTP error
            raise HTTPException(status_code=400, detail=grpc_response.message)  # Placeholder

        return ToggleAgentVisibilityResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in update_agent_core_details_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post("/{template_id}/create-agent", response_model=CreateAgentFromTemplateResponse)
async def create_agent_from_template(
    template_id: str, current_user: dict = Depends(role_required(["user"]))
):
    try:
        # Validate user and get user details
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        response = await agent_service.createAgentFromTemplate(
            template_id=template_id, owner_type=current_user["role"], owner_details=user_details
        )
        return response
    except HTTPException as e:
        print(f"[ERROR] Error in create_agent_from_template: {str(e)}")
        raise e
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Error in create_agent_from_template: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Agent Version Management Endpoints


def handle_grpc_response_status(response, success_code=200):
    """Handle common gRPC response status code mapping."""
    if not response.success:
        http_status_code = 400
        if "not found" in response.message.lower():
            http_status_code = 404
        elif "permission denied" in response.message.lower():
            http_status_code = 403
        elif "invalid argument" in response.message.lower():
            http_status_code = 400
        else:
            http_status_code = 500
        raise HTTPException(status_code=http_status_code, detail=response.message)
    return success_code


def prepare_agent_version_dict(raw_dict, agent_visibility=None):
    """Prepare agent version dict for response"""
    # Handle any JSON string fields that need parsing
    if "tags" in raw_dict and isinstance(raw_dict["tags"], str):
        try:
            raw_dict["tags"] = json.loads(raw_dict["tags"])
        except json.JSONDecodeError:
            raw_dict["tags"] = []

    # Ensure list fields are properly handled
    if "workflow_ids" in raw_dict and raw_dict["workflow_ids"] is None:
        raw_dict["workflow_ids"] = []
    if "mcp_server_ids" in raw_dict and raw_dict["mcp_server_ids"] is None:
        raw_dict["mcp_server_ids"] = []
    if "example_prompts" in raw_dict and raw_dict["example_prompts"] is None:
        raw_dict["example_prompts"] = []

    # Add visibility from parent agent since AgentVersion doesn't have visibility field
    if agent_visibility is not None:
        raw_dict["visibility"] = agent_visibility

    return raw_dict


@agent_router.get("/{agent_id}/versions", response_model=ListAgentVersionsResponse)
async def list_agent_versions(
    agent_id: str,
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of versions per page (1-100)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List all versions of an agent.

    This endpoint returns a paginated list of all versions for a specific agent.
    Both agent owners and marketplace users can access this endpoint, but:
    - Owners can see all versions of their agents
    - Marketplace users can only see versions of public agents

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent

    ## Query Parameters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of versions per page (1-100)

    ## Response
    Returns a paginated list of agent versions with metadata.

    ## Errors
    - 403: Permission denied (private agent accessed by non-owner)
    - 404: Agent not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # First get the agent to get its visibility
        agent_response = await agent_service.getAgentById(agent_id=agent_id)
        if not agent_response.success:
            raise HTTPException(status_code=404, detail=agent_response.message)

        agent_dict = MessageToDict(agent_response.agent, preserving_proto_field_name=True)
        agent_visibility = agent_dict.get("visibility", "private")

        response = await agent_service.list_agent_versions(
            agent_id=agent_id, user_id=user_id, page=page, page_size=page_size
        )

        handle_grpc_response_status(response)

        # Convert protobuf versions to dict format
        versions = []
        for version in response.versions:
            version_dict = prepare_agent_version_dict(
                MessageToDict(version, preserving_proto_field_name=True),
                agent_visibility=agent_visibility,
            )
            versions.append(AgentVersionInDB.model_validate(version_dict))

        print(f"[DEBUG] Agent versions: {versions}")
        return ListAgentVersionsResponse(
            success=response.success,
            message=response.message,
            versions=versions,
            total=response.total,
            page=response.page,
            total_pages=response.total_pages,
            current_version_id=response.current_version_id,
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in list_agent_versions: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.get("/{agent_id}/versions/{version_id}", response_model=GetAgentVersionResponse)
async def get_agent_version(
    agent_id: str,
    version_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get details of a specific agent version.

    This endpoint returns detailed information about a specific version of an agent.
    Both agent owners and marketplace users can access this endpoint, but:
    - Owners can see all versions of their agents
    - Marketplace users can only see versions of public agents

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent
    - **version_id**: The unique identifier of the version

    ## Response
    Returns detailed information about the specified agent version.

    ## Errors
    - 403: Permission denied (private agent accessed by non-owner)
    - 404: Agent or version not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # First get the agent to get its visibility
        agent_response = await agent_service.getAgentById(agent_id=agent_id)
        if not agent_response.success:
            raise HTTPException(status_code=404, detail=agent_response.message)

        agent_dict = MessageToDict(agent_response.agent, preserving_proto_field_name=True)
        agent_visibility = agent_dict.get("visibility", "private")

        response = await agent_service.get_agent_version(
            agent_id=agent_id, version_id=version_id, user_id=user_id
        )

        handle_grpc_response_status(response)

        # Convert protobuf version to dict format
        version_dict = prepare_agent_version_dict(
            MessageToDict(response.version, preserving_proto_field_name=True),
            agent_visibility=agent_visibility,
        )
        version = AgentVersionInDB.model_validate(version_dict)

        return GetAgentVersionResponse(
            success=response.success, message=response.message, version=version
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in get_agent_version: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post(
    "/{agent_id}/versions/{version_id}/switch", response_model=SwitchAgentVersionResponse
)
async def switch_agent_version(
    agent_id: str,
    version_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Switch the current version of an agent.

    This endpoint allows agent owners to switch the current active version of their agent.
    Only the agent owner can perform this operation.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent
    - **version_id**: The unique identifier of the version to switch to

    ## Response
    Returns success status and details of the new current version.

    ## Errors
    - 403: Permission denied (only agent owner can switch versions)
    - 404: Agent or version not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # First get the agent to get its visibility
        agent_response = await agent_service.getAgentById(agent_id=agent_id)
        if not agent_response.success:
            raise HTTPException(status_code=404, detail=agent_response.message)

        agent_dict = MessageToDict(agent_response.agent, preserving_proto_field_name=True)
        agent_visibility = agent_dict.get("visibility", "private")

        response = await agent_service.switch_agent_version(
            agent_id=agent_id, version_id=version_id, user_id=user_id
        )

        handle_grpc_response_status(response)

        # Convert protobuf version to dict format
        new_current_version_dict = prepare_agent_version_dict(
            MessageToDict(response.new_current_version, preserving_proto_field_name=True),
            agent_visibility=agent_visibility,
        )
        new_current_version = AgentVersionInDB.model_validate(new_current_version_dict)

        return SwitchAgentVersionResponse(
            success=response.success,
            message=response.message,
            new_current_version=new_current_version,
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in switch_agent_version: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@agent_router.post("/{agent_id}/create-version-and-publish")
async def create_version_and_publish(
    agent_id: str,
    publish_to_marketplace: bool = Query(
        False, description="Whether to update marketplace listing with new version"
    ),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Create a new version for an agent and optionally publish to marketplace.

    This is the comprehensive solution that provides user-controlled versioning where
    users explicitly decide when to create versions and whether to publish to marketplace.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to create version for

    ## Query Parameters
    - **publish_to_marketplace**: Whether to update marketplace listing with new version (default: false)

    ## Response
    Returns version creation and marketplace publishing status.

    ## Requirements
    - User must be the owner of the agent
    - Agent must have pending changes (is_updated = true)
    - For marketplace publishing: agent must be public and have existing marketplace listing

    ## Errors
    - 400: Bad request (no pending changes, agent not public for marketplace publishing)
    - 403: Permission denied (not the owner of the agent)
    - 404: Agent not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # Call the gRPC service to create version and optionally publish
        grpc_response = await agent_service.create_version_and_publish(
            agent_id=agent_id, user_id=user_id, publish_to_marketplace=publish_to_marketplace
        )

        handle_grpc_response_status(grpc_response)

        return {
            "success": grpc_response.success,
            "message": grpc_response.message,
            "version_created": grpc_response.version_created,
            "marketplace_updated": grpc_response.marketplace_updated,
            "version_number": (
                grpc_response.version_number if hasattr(grpc_response, "version_number") else None
            ),
            "version_id": (
                grpc_response.version_id if hasattr(grpc_response, "version_id") else None
            ),
            "marketplace_listing_id": (
                grpc_response.marketplace_listing_id
                if hasattr(grpc_response, "marketplace_listing_id")
                else None
            ),
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in create_version_and_publish: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
