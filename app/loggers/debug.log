2024-12-04 18:50:01,199 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-04 18:50:01,199 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-04 18:53:57,424 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-04 18:53:57,424 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-04 18:55:02,106 - werkzeug - INFO [_internal.py:97 - _log] - 127.0.0.1 - - [04/Dec/2024 18:55:02] "GET /api/media/media/musics?page=1&per_page=10&is_public=true HTTP/1.1" 200 -
2024-12-04 18:56:21,948 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-04 18:56:21,949 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-04 18:57:00,409 - werkzeug - INFO [_internal.py:97 - _log] - 127.0.0.1 - - [04/Dec/2024 18:57:00] "GET /api/media/media/musics?page=1&per_page=10&is_public=true HTTP/1.1" 200 -
2024-12-04 18:57:58,693 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-04 18:57:58,693 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-04 18:58:07,782 - werkzeug - INFO [_internal.py:97 - _log] - 127.0.0.1 - - [04/Dec/2024 18:58:07] "GET /api/media/media/musics?page=1&per_page=10&is_public=true HTTP/1.1" 200 -
2024-12-09 16:13:19,563 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-09 16:13:19,566 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-09 16:19:00,108 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-09 16:19:00,108 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-09 16:20:45,286 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-09 16:20:45,286 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-10 18:26:31,814 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-10 18:26:31,815 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
2024-12-10 18:57:26,048 - werkzeug - INFO [_internal.py:97 - _log] - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2024-12-10 18:57:26,049 - werkzeug - INFO [_internal.py:97 - _log] - Press CTRL+C to quit
