import logging
import os
import re
from typing import Any

# Default log level if not specified or invalid
DEFAULT_LOG_LEVEL = logging.ERROR

# Map log level names to their corresponding logging constants
LOG_LEVEL_MAPPING = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

# Fields to be masked in logs
SENSITIVE_FIELDS = ["api_key", "password", "token"]


def get_log_level() -> int:
    """Determine the log level based on environment variables."""
    env_level = os.getenv("LOG_LEVEL", "").upper()
    if os.getenv("DEBUG") == "true":
        return logging.ERROR
    return LOG_LEVEL_MAPPING.get(env_level, DEFAULT_LOG_LEVEL)


def setup_logger(log_file: str = "debug.log") -> logging.Logger:
    """Set up and configure the logger."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"

    # Create a log handler for file output
    file_handler = logging.FileHandler(
        filename=os.path.join(os.path.dirname(__file__), log_file),
        mode="a",
        encoding="utf-8",
    )

    # Apply the custom format to the handler
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    file_handler.addFilter(filter_sensitive_fields)

    # Create a logger and add the handler
    logger = logging.getLogger()
    logger.addHandler(file_handler)
    logger.setLevel(get_log_level())

    # Set pymongo logger to a higher level to reduce verbosity
    logging.getLogger("pymongo").setLevel(logging.ERROR)
    logging.getLogger("pymongo.topology").setLevel(logging.ERROR)
    logging.getLogger("openai._base_client").setLevel(logging.ERROR)
    logging.getLogger("httpcore.http11").setLevel(logging.ERROR)
    logging.getLogger("httpcore.connection").setLevel(logging.ERROR)

    # Set httpx logger to ERROR level to suppress DEBUG messages
    logging.getLogger("httpx").setLevel(logging.ERROR)

    return logger


def filter_sensitive_fields(record: logging.LogRecord) -> bool:
    """Filter sensitive fields from log records."""
    if isinstance(record.args, dict):
        record.args = {k: mask_sensitive_value(k, v) for k, v in record.args.items()}
    elif isinstance(record.args, tuple):
        record.args = tuple(mask_sensitive_value(str(arg), arg) for arg in record.args)

    if isinstance(record.msg, str):
        record.msg = remove_ansi_escape_sequences(record.msg)

    return True


def mask_sensitive_value(key: str, value: Any) -> Any:
    """Mask sensitive values."""
    return "*****" if any(field in key.lower() for field in SENSITIVE_FIELDS) else value


def remove_ansi_escape_sequences(text: str) -> str:
    """Remove ANSI escape sequences from text."""
    return re.sub(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])", "", text)
