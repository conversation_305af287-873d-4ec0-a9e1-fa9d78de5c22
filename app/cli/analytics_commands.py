"""
CLI Commands for Analytics Management
Command-line interface for managing analytics data and aggregations
"""

import click
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.analytics_aggregation_service import AnalyticsAggregationService
from app.utils.analytics_data_seeder import AnalyticsDataSeeder
import logging

logger = logging.getLogger(__name__)


@click.group()
def analytics():
    """Analytics management commands"""
    pass


@analytics.command()
@click.option("--days", default=7, help="Number of days to generate data for")
@click.option("--events-per-day", default=100, help="Number of events per day")
@click.option("--user-ids", help="Comma-separated list of user IDs (optional)")
@click.option(
    "--run-aggregation/--no-aggregation", default=True, help="Run aggregation after seeding"
)
def seed_data(days: int, events_per_day: int, user_ids: str, run_aggregation: bool):
    """
    Seed the database with sample analytics data

    This command generates realistic sample data for testing and development:
    - API request events
    - System activities
    - App credit usage
    - Optionally runs daily aggregation
    """
    click.echo(f"🌱 Seeding analytics data for {days} days...")

    # Parse user IDs if provided
    user_id_list = None
    if user_ids:
        user_id_list = [uid.strip() for uid in user_ids.split(",")]
        click.echo(f"Using user IDs: {user_id_list}")

    # Get database session
    db = next(get_db())

    try:
        seeder = AnalyticsDataSeeder(db)

        summary = seeder.run_full_seed(
            days=days, user_ids=user_id_list, run_aggregation=run_aggregation
        )

        click.echo("✅ Data seeding completed successfully!")
        click.echo(f"📊 Summary:")
        click.echo(f"  - Days: {summary['days']}")
        click.echo(f"  - API Events: {summary['api_events_count']}")
        click.echo(f"  - System Activities: {summary['system_activities_count']}")
        click.echo(f"  - App Usage Records: {summary['app_usage_count']}")
        click.echo(f"  - Total Records: {summary['total_records']}")

        if run_aggregation and "aggregation_results" in summary:
            successful_aggregations = sum(
                1 for result in summary["aggregation_results"] if result["status"] == "success"
            )
            click.echo(
                f"  - Aggregations: {successful_aggregations}/{len(summary['aggregation_results'])} successful"
            )

    except Exception as e:
        click.echo(f"❌ Error seeding data: {str(e)}")
        logger.error(f"Data seeding failed: {str(e)}")
        raise click.ClickException(f"Data seeding failed: {str(e)}")

    finally:
        db.close()


@analytics.command()
@click.option("--date", help="Date to aggregate (YYYY-MM-DD format, defaults to yesterday)")
@click.option("--user-id", help="User ID for user-specific aggregation (optional)")
@click.option("--force", is_flag=True, help="Force re-aggregation even if data exists")
def aggregate_daily(date: str, user_id: str, force: bool):
    """
    Run daily aggregation for analytics metrics

    This command aggregates raw analytics data into daily metrics:
    - Dashboard overview metrics
    - Credit usage breakdown
    - Agent performance metrics
    - Workflow utilization metrics
    """
    # Parse date
    if date:
        try:
            target_date = datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            raise click.ClickException("Invalid date format. Use YYYY-MM-DD")
    else:
        target_date = datetime.utcnow() - timedelta(days=1)

    click.echo(f"📈 Running daily aggregation for {target_date.date()}")
    if user_id:
        click.echo(f"👤 User ID: {user_id}")

    # Get database session
    db = next(get_db())

    try:
        service = AnalyticsAggregationService(db)

        # Check if aggregation already exists (unless force is used)
        if not force:
            from app.models.dashboard_analytics import DashboardMetrics

            existing = db.query(DashboardMetrics).filter(
                DashboardMetrics.date == target_date.date()
            )
            if user_id:
                existing = existing.filter(DashboardMetrics.user_id == user_id)

            if existing.first():
                click.echo("⚠️  Aggregation already exists for this date.")
                if not click.confirm(
                    "Do you want to re-aggregate (this will update existing data)?"
                ):
                    click.echo("Aggregation cancelled.")
                    return

        result = service.run_daily_aggregation(target_date, user_id)

        if result["status"] == "success":
            click.echo("✅ Daily aggregation completed successfully!")
            click.echo(f"📊 Results:")
            click.echo(f"  - Dashboard Metrics ID: {result['dashboard_metrics_id']}")
            click.echo(f"  - Credit Breakdowns: {result['credit_breakdowns_count']}")
            click.echo(f"  - Agent Metrics: {result['agent_metrics_count']}")
            click.echo(f"  - Workflow Metrics: {result['workflow_metrics_count']}")
        else:
            click.echo(f"❌ Aggregation failed: {result.get('error', 'Unknown error')}")
            raise click.ClickException("Daily aggregation failed")

    except Exception as e:
        click.echo(f"❌ Error during aggregation: {str(e)}")
        logger.error(f"Daily aggregation failed: {str(e)}")
        raise click.ClickException(f"Daily aggregation failed: {str(e)}")

    finally:
        db.close()


@analytics.command()
@click.option("--start-date", required=True, help="Start date (YYYY-MM-DD format)")
@click.option("--end-date", required=True, help="End date (YYYY-MM-DD format)")
@click.option("--user-id", help="User ID for user-specific aggregation (optional)")
def backfill_aggregations(start_date: str, end_date: str, user_id: str):
    """
    Backfill aggregations for a date range

    This command runs daily aggregation for multiple dates in sequence.
    Useful for historical data processing or catching up on missed aggregations.
    """
    # Parse dates
    try:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError:
        raise click.ClickException("Invalid date format. Use YYYY-MM-DD")

    if start_dt > end_dt:
        raise click.ClickException("Start date must be before or equal to end date")

    days_count = (end_dt - start_dt).days + 1
    click.echo(f"🔄 Backfilling aggregations from {start_date} to {end_date} ({days_count} days)")
    if user_id:
        click.echo(f"👤 User ID: {user_id}")

    # Get database session
    db = next(get_db())

    try:
        service = AnalyticsAggregationService(db)

        with click.progressbar(length=days_count, label="Processing dates") as bar:
            results = service.backfill_aggregations(start_dt, end_dt, user_id)

            successful = 0
            failed = 0

            for result in results:
                if result["status"] == "success":
                    successful += 1
                else:
                    failed += 1
                    click.echo(
                        f"\n❌ Failed for {result.get('date', 'unknown')}: {result.get('error', 'Unknown error')}"
                    )

                bar.update(1)

        click.echo(f"\n✅ Backfill completed!")
        click.echo(f"📊 Results:")
        click.echo(f"  - Successful: {successful}")
        click.echo(f"  - Failed: {failed}")
        click.echo(f"  - Total: {len(results)}")

        if failed > 0:
            click.echo("⚠️  Some aggregations failed. Check logs for details.")

    except Exception as e:
        click.echo(f"❌ Error during backfill: {str(e)}")
        logger.error(f"Backfill failed: {str(e)}")
        raise click.ClickException(f"Backfill failed: {str(e)}")

    finally:
        db.close()


@analytics.command()
@click.option("--user-id", help="User ID filter (optional)")
@click.option("--days", default=7, help="Number of days to show data for")
def show_overview(user_id: str, days: int):
    """
    Show current dashboard overview metrics

    Displays the current state of dashboard metrics for verification.
    """
    click.echo(f"📊 Dashboard Overview (last {days} days)")
    if user_id:
        click.echo(f"👤 User ID: {user_id}")

    # Get database session
    db = next(get_db())

    try:
        from app.services.dashboard_analytics_service import DashboardAnalyticsService

        service = DashboardAnalyticsService(db)

        overview = service.get_dashboard_overview(user_id=user_id, days=days)

        click.echo("\n📈 Metrics:")
        click.echo(f"  Active Agents: {overview['active_agents']}")
        click.echo(f"  Credit Usage: ${overview['credit_usage']:.2f}")
        click.echo(f"  Agent Requests: {overview['agent_requests']}")
        click.echo(f"  Workflow Requests: {overview['workflow_requests']}")
        click.echo(f"  Custom MCPs: {overview['custom_mcps']}")
        click.echo(f"  Total Cost: ${overview['total_cost']:.2f}")

        click.echo("\n📊 Changes:")
        click.echo(f"  Credit Usage Change: ${overview['credit_usage_change']:.2f}")
        click.echo(f"  Agent Requests Change: {overview['agent_requests_change_pct']:.1f}%")
        click.echo(f"  Workflow Requests Change: {overview['workflow_requests_change_pct']:.1f}%")
        click.echo(f"  Custom MCPs Change: {overview['custom_mcps_change']}")

        # Show credit breakdown
        breakdown = service.get_credit_usage_breakdown(user_id=user_id, days=days)
        if breakdown:
            click.echo("\n💳 Credit Usage Breakdown:")
            for item in breakdown:
                click.echo(
                    f"  {item['category'].title()}: {item['credits_used']:.1f} credits (${item['cost']:.2f})"
                )

    except Exception as e:
        click.echo(f"❌ Error getting overview: {str(e)}")
        logger.error(f"Overview failed: {str(e)}")
        raise click.ClickException(f"Overview failed: {str(e)}")

    finally:
        db.close()


@analytics.command()
@click.option("--grpc-port", default=50051, help="gRPC server port")
@click.option("--rest-port", default=8000, help="REST API server port")
@click.option("--grpc-only", is_flag=True, help="Start only gRPC server")
@click.option("--rest-only", is_flag=True, help="Start only REST API server")
def serve(grpc_port: int, rest_port: int, grpc_only: bool, rest_only: bool):
    """
    Start the analytics service servers

    This command starts both gRPC and REST API servers for the analytics service.
    You can choose to start only one type of server using the flags.
    """
    import threading
    import time

    if grpc_only and rest_only:
        raise click.ClickException("Cannot specify both --grpc-only and --rest-only")

    servers = []

    def start_grpc_server():
        """Start the gRPC server"""
        try:
            from app.grpc.server import AnalyticsGRPCServer

            server = AnalyticsGRPCServer(port=grpc_port)
            click.echo(f"🚀 Starting gRPC server on port {grpc_port}...")
            server.start()
        except Exception as e:
            click.echo(f"❌ Failed to start gRPC server: {e}")
            logger.error(f"gRPC server failed: {e}")

    def start_rest_server():
        """Start the REST API server"""
        try:
            import uvicorn

            click.echo(f"🌐 Starting REST API server on port {rest_port}...")
            uvicorn.run("app.api.main:app", host="0.0.0.0", port=rest_port, log_level="info")
        except Exception as e:
            click.echo(f"❌ Failed to start REST API server: {e}")
            logger.error(f"REST API server failed: {e}")

    try:
        if not rest_only:
            # Start gRPC server in a separate thread
            grpc_thread = threading.Thread(target=start_grpc_server, daemon=True)
            grpc_thread.start()
            servers.append("gRPC")
            time.sleep(1)  # Give gRPC server time to start

        if not grpc_only:
            # Start REST API server (this will block)
            servers.append("REST API")
            if not rest_only:
                click.echo(f"✅ Started {', '.join(servers)} servers")
                click.echo("📊 Dashboard Analytics available at:")
                click.echo(f"   - gRPC: localhost:{grpc_port}")
                click.echo(f"   - REST: http://localhost:{rest_port}/docs")

            start_rest_server()
        else:
            # Only gRPC server, wait for it
            click.echo(f"✅ Started gRPC server on port {grpc_port}")
            click.echo("📊 Dashboard Analytics gRPC service is running")
            click.echo("Press Ctrl+C to stop")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                click.echo("\n🛑 Shutting down...")

    except KeyboardInterrupt:
        click.echo("\n🛑 Shutting down servers...")
    except Exception as e:
        click.echo(f"❌ Server error: {e}")
        logger.error(f"Server error: {e}")
        raise click.ClickException(f"Server failed: {e}")


@analytics.command()
def test_grpc():
    """
    Test the gRPC analytics service

    This command runs a quick test of the gRPC analytics service to verify
    it's working correctly.
    """
    click.echo("🧪 Testing gRPC Analytics Service...")

    try:
        from app.utils.grpc_client_example import AnalyticsGRPCClient

        client = AnalyticsGRPCClient()
        client.connect()

        # Test basic functionality
        click.echo("1. Testing dashboard metrics...")
        metrics = client.get_dashboard_metrics()

        click.echo("2. Testing API request recording...")
        event_id = client.record_api_request("/api/test", "success", "test_user")

        click.echo("3. Testing system activity recording...")
        activity_id = client.record_system_activity("Test Activity", "test", "test_user")

        client.disconnect()

        if metrics is not None and event_id and activity_id:
            click.echo("✅ gRPC service test completed successfully!")
        else:
            click.echo("⚠️  Some tests failed, check the logs")

    except Exception as e:
        click.echo(f"❌ gRPC test failed: {e}")
        logger.error(f"gRPC test failed: {e}")
        raise click.ClickException(f"gRPC test failed: {e}")


if __name__ == "__main__":
    analytics()
