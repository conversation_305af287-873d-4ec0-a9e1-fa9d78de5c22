from app.controller.auth.utils import decode_auth_token
from app.cache import get_cache_provider

from app.repositories.user_repository import AuthRepository
from app.exceptions.common import ForbiddenAccessError, InvalidAuthError


def authorize_user(token) -> str:
    user_id, _, _, _ = decode_auth_token(auth_token=token)

    session_key = f"sess_{user_id}"
    if get_cache_provider().get(session_key) != token:
        raise InvalidAuthError(description="Session expired or does not exist.")

    # Ensure the user ID corresponds to a valid user in the database
    if not AuthRepository().user_already_exists(user_id):
        raise ForbiddenAccessError(description="Invalid user ID.")

    return user_id
