class CustomParametersException(Exception):
    def __init__(
        self, message="Required parameters are missing.", field=None, code=400
    ):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(self.message)


class CustomRequiredParametersException(Exception):
    def __init__(
        self, message="Required parameters are missing.", field=None, code=400
    ):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(self.message)
