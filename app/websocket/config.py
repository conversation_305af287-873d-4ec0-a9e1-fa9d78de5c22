import json
import threading
import requests
import websockets
import config


# Set the default timeout for the WebSocket connection to 3600 seconds (1 hour)
DEFAULT_TIMEOUT = 3600

NEXT_AGENT = ["next", "confirm", "continue", "proceed", "accept", "ok", "okay"]


class UserThread:
    def __init__(self):
        self.event = threading.Event()
        self.message = None


user_threads = {}
active_requests = {}


async def handler(websocket):
    print("hello websocket")
    try:
        client_ip = get_user_ip()
        print(f"New connection from IP: {client_ip}")

        async for message in websocket:
            user_id = None  # Ensure user_id is defined within this scope
            try:
                received_message = json.loads(message)
                print("received_message", received_message)
                return

            except json.JSONDecodeError as e:
                print("JSON decode error: ", e)
                await websocket.send("Invalid JSON format.")
            except Exception as e:
                print("ERROR: ", e)
                if user_id in active_requests:
                    active_requests.pop(user_id, None)

    except websockets.exceptions.ConnectionClosed as e:
        print(f"Connection closed: {e}")
    except Exception as e:
        print("ERROR: ", e)
    finally:

        if user_id and user_id in active_requests:
            active_requests.pop(user_id, None)


def get_user_ip():
    try:
        response = requests.get("https://api.ipify.org?format=json")
        if response.status_code == 200:
            return response.json()["ip"]
        else:
            return "Unable to get IP address"
    except requests.RequestException:
        return "Error: Unable to connect to the IP lookup service"


def websocket_server():
    headers = [
        ("Access-Control-Allow-Origin", "*"),
        ("Access-Control-Allow-Methods", "GET, POST, OPTIONS"),
        ("Access-Control-Allow-Headers", "Content-Type"),
    ]

    start_server = websockets.serve(
        handler,
        config.HOST,
        config.WEBSOCKET_PORT,
        extra_headers=headers,
        ping_timeout=DEFAULT_TIMEOUT,
    )

    return start_server
