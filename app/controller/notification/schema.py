from .namespace import notification_namespace

from flask_restx import fields, reqparse, inputs


# Define notification model
create_notification_model = notification_namespace.model(
    "CreateNotification",
    {
        "title": fields.String(required=True, description="Notification title"),
        "description": fields.String(
            required=True, description="Notification description"
        ),
        "type": fields.String(
            required=True,
            description="Notification type",
        ),
    },
)

# Define notification model
update_notification_model = notification_namespace.model(
    "UpdateNotification",
    {
        "is_read": fields.Boolean(
            required=True,
            description="Notification type",
            default=True
        ),
    },
)


# Define notification model
notification_model = notification_namespace.model(
    "Notification",
    {
        "id": fields.String(description="Voice ID"),
        "user_id": fields.String(description="User ID"),
        "title": fields.String(required=True, description="Notification title"),
        "description": fields.String(
            required=True, description="Notification description"
        ),
        "type": fields.String(
            required=True,
            description="Notification type",
        ),
        "is_read": fields.<PERSON><PERSON>an(description="Whether the voice is cloned or not"),
        "create_at": fields.Date(description=" creation time"),
        "updated_at": fields.Date(description=" updated time"),
    },
)

error_response_model = notification_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)


# Pagination model
pagination_model = notification_namespace.model(
    "Pagination",
    {
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of items per page"),
        "total_count": fields.Integer(description="Total number of items"),
        "total_pages": fields.Integer(description="Total number of pages"),
    },
)

# Response model for notification list
notification_list_response_model = notification_namespace.model(
    "VoiceListResponse",
    {
        "message": fields.String(description="Response message"),
        "data": fields.List(
            fields.Nested(notification_model), description="List of notifications"
        ),
        "pagination": fields.Nested(
            pagination_model, description="Pagination information"
        ),
        "code": fields.Integer(description="Response code"),
    },
)

# Input model for notification list query parameters
notification_list_input_model = reqparse.RequestParser()
notification_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
notification_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
notification_list_input_model.add_argument(
    "type",
    type=str,
    required=False,
    choices=["news", "alert", "update", "reminder"],
    help="Filter by type",
)
notification_list_input_model.add_argument(
    "is_read", type=inputs.boolean, required=False, help="Filter by read status"
)
