from flask import Blueprint
from flask_restx import Api

from .endpoint import notification_namespace



from app.exceptions.exception_handler import handle_error

from app.exceptions.common import (
    BaseExceptionClass,
    ForbiddenAccessError,
    InternalError,
    InvalidAuthError,
    InvalidAuthTokenFormatError,
    InvalidTokenError,
    InvalidValueError,
)

notification_blueprint = Blueprint("notification_blueprint", __name__)


# Define the authorization method to be used in the Swagger UI documentation
# This specifies that the API uses Bearer Token authentication for securing endpoints
authorizations: dict[str, dict[str, str]] = {
    "Bearer Auth": {
        "type": "apiKey",  # Specifies the type of security scheme
        "in": "header",  # Specifies where the API key is passed (in this case, the HTTP header)
        "name": "Authorization",  # Name of the header field to be used
        "denotificationion": "Type in the *'Value'* input box below: **'Bearer &lt;JWT_TOKEN&gt;'**, where JWT_TOKEN is your authentication token.",
    },
}

api = Api(
    app=notification_blueprint,
    title="Notification API",
    description="API endpoints for notification.",
    default="Notification API",
    authorizations=authorizations,  # Apply the defined authorization method
    default_label="Operations related to notification",
)

api.add_namespace(notification_namespace)

# Register the error handler for multiple exceptions
api.errorhandler(BaseExceptionClass)(handle_error)
api.errorhandler(InvalidValueError)(handle_error)
api.errorhandler(InvalidAuthTokenFormatError)(handle_error)
api.errorhandler(InvalidAuthError)(handle_error)
api.errorhandler(ForbiddenAccessError)(handle_error)
api.errorhandler(InvalidTokenError)(handle_error)
api.errorhandler(InternalError)(handle_error)
api.errorhandler(handle_error)
