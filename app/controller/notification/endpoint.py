from .namespace import notification_namespace
from flask_restx import Resource
from flask import g, request
from app.controller.auth import login_required, is_admin
from werkzeug.exceptions import NotFound
from app.exceptions import handle_errors

from app.service.notification import NotificationService

from .schema import (
    error_response_model,
    notification_model,
    notification_list_response_model,
    notification_list_input_model,
    update_notification_model,
    create_notification_model,
)

from app.repositories.notification_repository import NotificationRepository

notification_repository = NotificationRepository()

notification_service = NotificationService()


@notification_namespace.route("/")
class CreateNotification(Resource):
    @notification_namespace.doc(security="Bearer Auth")
    @notification_namespace.expect(create_notification_model)
    @notification_namespace.response(
        201, "Notification created successfully", notification_model
    )
    @notification_namespace.response(400, "Validation error")
    @is_admin
    @handle_errors
    def post(self):
        """Create a new notification"""
        data = request.json

        # Validate input
        if not all(key in data for key in ["title", "description", "type"]):
            return {"message": "Missing required fields"}, 400

        notification = {
            "title": data["title"],
            "description": data["description"],
            "type": data["type"],
            "is_read": False,
        }

        result = notification_repository.create_notification(notification)

        return {
            "message": "Notification created successfully",
            "data": notification_repository.to_dict(result),
            "code": 201,
        }, 201


@notification_namespace.doc(security="Bearer Auth")
@notification_namespace.route("/<string:id>")
@notification_namespace.param("id", "The notification identifier")
class Notification(Resource):
    @notification_namespace.response(
        200, "Notification fetch successfully", notification_model
    )
    @notification_namespace.response(404, "Notification not found")
    @login_required
    @handle_errors
    def get(self, id):
        """Fetch a notification"""
        notification = notification_repository.get_notification_by_id(id)
        if notification:
            return {
                "message": "Notification fetched successfully",
                "data": notification_repository.to_dict(notification),
                "code": 200,
            }, 200
        notification_namespace.abort(404, f"Notification {id} doesn't exist")

    @notification_namespace.expect(create_notification_model)
    @notification_namespace.response(
        200, "Notification fetch successfully", notification_model
    )
    @notification_namespace.response(400, "Validation error")
    @notification_namespace.response(404, "Notification not found")
    @is_admin
    @handle_errors
    def put(self, id):
        """Update a notification"""
        data = request.json
        if not notification_repository.get_notification_by_id(id):
            raise NotFound(f"Notification {id} doesn't exist")

        notification_repository.update_notification(id, data)
        return {"message": "Notification updated successfully"}

    @notification_namespace.response(200, "Notification deleted successfully")
    @notification_namespace.response(404, "Notification not found")
    @is_admin
    @handle_errors
    def delete(self, id):
        """Delete a notification"""
        result = notification_repository.delete_notification(id)
        if result.deleted_count > 0:
            return {"message": "Notification deleted successfully"}
        notification_namespace.abort(404, f"Notification {id} doesn't exist")


@notification_namespace.route("/notification_list")
class NotificationList(Resource):
    @notification_namespace.doc(security="Bearer Auth")
    @notification_namespace.expect(notification_list_input_model)
    @notification_namespace.response(
        200,
        "Successfully retrieved list of PlayHT notifications",
        notification_list_response_model,
    )
    @notification_namespace.response(401, "Unauthorized", error_response_model)
    @notification_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def get(self):
        """Get the list of PlayHT Notifications with pagination and optional filters"""

        args = notification_list_input_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        type = args.get("type", None)
        is_read = args.get("is_read", None)

        user_id = getattr(g, "user_id")

        result = notification_service.get_user_notifications(
            user_id, page, per_page, type, is_read
        )

        return {
            "message": "Notifications successfully fetched",
            "data": result["notifications"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200


@notification_namespace.doc(security="Bearer Auth")
@notification_namespace.route("update_read_status/<string:id>")
@notification_namespace.param("id", "The notification identifier")
class NotificationUpdate(Resource):
    @notification_namespace.expect(update_notification_model)
    @notification_namespace.response(
        200, "Notification fetch successfully", notification_model
    )
    @notification_namespace.response(400, "Validation error")
    @notification_namespace.response(404, "Notification not found")
    @login_required
    @handle_errors
    def put(self, id):
        """Update a notification"""

        user_id = getattr(g, "user_id")

        data = request.json
        if not notification_repository.get_notifications_by_user_and_id(id, user_id):
            raise NotFound(f"Notification {id} doesn't exist")

        notification_repository.update_notification(id, data)
        return {"message": "Notification updated successfully"}
