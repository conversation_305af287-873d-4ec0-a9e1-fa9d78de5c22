from .namespace import brand_namespace
from flask_restx import Resource
from flask import g, request
from app.controller.auth import login_required

from bson import ObjectId
from app.service.brand import BrandService

from app.exceptions.common import ForbiddenAccessError
from app.exceptions import handle_errors
from .schema import (
    error_response_model,
    brand_create_model,
    brand_response_model,
    brand_update_model,
)

from app.repositories.brand_repository import BrandRepository
from app.repositories.subscription_repository import SubscriptionRepository


brand_repository = BrandRepository()

brand_service = BrandService()
subscription_repository = SubscriptionRepository()


@brand_namespace.route("/add-brand")
class BrandManagement(Resource):
    @brand_namespace.doc(security="Bearer Auth")
    @brand_namespace.expect(brand_create_model)
    @brand_namespace.response(201, "Brand created successfully", brand_response_model)
    @brand_namespace.response(400, "Bad request", error_response_model)
    @brand_namespace.response(401, "Unauthorized", error_response_model)
    @brand_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def post(self):
        """Create a new brand"""

        user_id = getattr(g, "user_id")

        # user_subscription = subscription_repository.get_subscription_by_user_id(user_id)

        # if (
        #     not user_subscription
        #     or user_subscription["status"] != "active"
        #     # or user_subscription["amount_paid"] == 0
        # ):
        #     raise ForbiddenAccessError("User don't have active subscription.")

        data = request.json

        data["user_id"] = ObjectId(user_id)

        result = brand_service.create_brand(data)
        return {
            "message": "Brand created successfully",
            "code": 201,
            "data": result,
        }, 201


@brand_namespace.route("/brand")
class BrandUpdateManagement(Resource):
    @brand_namespace.doc(security="Bearer Auth")
    @brand_namespace.expect(brand_update_model)
    @brand_namespace.response(200, "Brand updated successfully", brand_response_model)
    @brand_namespace.response(400, "Bad request", error_response_model)
    @brand_namespace.response(401, "Unauthorized", error_response_model)
    @brand_namespace.response(404, "Brand not found", error_response_model)
    @brand_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def put(self):
        """Update an existing brand"""
        user_id = getattr(g, "user_id")
        data = request.json
        result = brand_service.update_brand(user_id, data)
        if result:
            return {
                "message": "Brand updated successfully",
                "code": 200,
                "data": result,
            }, 200
        else:
            return {"message": "Brand not found", "code": 404, "data": None}, 404

    @brand_namespace.doc(security="Bearer Auth")
    @brand_namespace.response(200, "Brand retrieved successfully", brand_response_model)
    @brand_namespace.response(401, "Unauthorized", error_response_model)
    @brand_namespace.response(404, "Brand not found", error_response_model)
    @brand_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def get(self):
        """Get a specific brand"""

        user_id = getattr(g, "user_id")
        result = brand_service.get_brand(user_id)
        if result:
            return {
                "message": "Brand retrieved successfully",
                "code": 200,
                "data": result,
            }, 200
        else:
            return {"message": "Brand not found", "code": 404, "data": None}, 404


@brand_namespace.route("/remove-brand")
class BrandCloneManagement(Resource):
    @brand_namespace.doc(security="Bearer Auth")
    @brand_namespace.response(200, "Brand deleted successfully")
    @brand_namespace.response(401, "Unauthorized", error_response_model)
    @brand_namespace.response(404, "Brand not found", error_response_model)
    @brand_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def delete(self):
        """Delete a cloned brand"""

        user_id = getattr(g, "user_id")
        result = brand_service.delete_brand(user_id)
        if result:
            return {"message": "Brand deleted successfully", "code": 200}
        else:
            return {"message": "Brand not found", "code": 404}, 404
