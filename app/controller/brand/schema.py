from flask_restx import fields
from .namespace import brand_namespace


# Enum for social media platforms
social_platforms = [
    "web",
    "linkedin",
    "facebook",
    "youtube",
    "twitter",
    "x",
    "instagram",
]

# Social Link model
social_link_model = brand_namespace.model(
    "SocialLink",
    {
        "platform": fields.String(
            required=True,
            description="Social media platform name",
            enum=social_platforms,
        ),
        "url": fields.String(
            required=True, description="URL of the social media profile"
        ),
    },
)

# Request model for creating/updating a brand
brand_create_model = brand_namespace.model(
    "BrandRequest",
    {
        "name": fields.String(required=True, description="Name of the brand"),
        "logo": fields.String(required=True, description="URL of the brand logo"),
        "intro_landscape": fields.String(
            required=False, description="Intro video URL for landscape orientation"
        ),
        "outro_landscape": fields.String(
            required=False, description="Outro video URL for landscape orientation"
        ),
        "intro_portrait": fields.String(
            required=False, description="Intro video URL for portrait orientation"
        ),
        "outro_portrait": fields.String(
            required=False, description="Outro video URL for portrait orientation"
        ),
        "social_links": fields.List(
            fields.Nested(social_link_model),
            required=False,
            description="List of social media links",
        ),
    },
)

# Response model for brand operations
brand_model = brand_namespace.model(
    "BrandResponse",
    {
        "_id": fields.String(description="Unique identifier for the brand"),
        "user_id": fields.String(description="ID of the user who owns the brand"),
        "name": fields.String(description="Name of the brand"),
        "logo": fields.String(description="URL of the brand logo"),
        "intro_landscape": fields.String(
            description="Intro video URL for landscape orientation"
        ),
        "outro_landscape": fields.String(
            description="Outro video URL for landscape orientation"
        ),
        "intro_portrait": fields.String(
            description="Intro video URL for portrait orientation"
        ),
        "outro_portrait": fields.String(
            description="Outro video URL for portrait orientation"
        ),
        "created_at": fields.DateTime(description="Timestamp of brand creation"),
        "updated_at": fields.DateTime(description="Timestamp of last brand update"),
    },
)

# Response model for single brand operations
brand_response_model = brand_namespace.model(
    "BrandResponse",
    {
        "message": fields.String(required=True, description="Response message"),
        "code": fields.Integer(required=True, description="Response code"),
        "data": fields.Nested(brand_model, description="Brand data"),
    },
)

# Response model for listing brands
brand_list_response_model = brand_namespace.model(
    "BrandListResponse",
    {
        "brands": fields.List(fields.Nested(brand_response_model)),
        "total": fields.Integer(description="Total number of brands"),
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of brands per page"),
    },
)

# Request model for updating a brand (all fields optional)
brand_update_model = brand_namespace.model(
    "BrandUpdateRequest",
    {
        "name": fields.String(description="Name of the brand"),
        "logo": fields.String(description="URL of the brand logo"),
        "intro_landscape": fields.String(
            description="Intro video URL for landscape orientation"
        ),
        "outro_landscape": fields.String(
            description="Outro video URL for landscape orientation"
        ),
        "intro_portrait": fields.String(
            description="Intro video URL for portrait orientation"
        ),
        "outro_portrait": fields.String(
            description="Outro video URL for portrait orientation"
        ),
    },
)

# Error response model
error_response_model = brand_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(required=True, description="Error message"),
        "code": fields.Integer(required=True, description="Error code"),
    },
)
