from flask_restx import Resource
from flask import g, request

from werkzeug.exceptions import NotFound

from .namespace import template_namespace

from app.exceptions.common import InvalidValueError, AuthenticationError
from app.controller.auth import login_required
from app.service.template import TemplateService
from ..auth import is_admin
from .schema import (
    error_response_model,
    template_model,
    template_list_response_model,
    template_list_input_model,
    template_create_model,
    template_update_model,
)

template_service = TemplateService()


@template_namespace.route("")
class TemplateListResource(Resource):
    @template_namespace.doc(security="Bearer Auth")
    @template_namespace.expect(template_list_input_model)
    @template_namespace.response(
        200, "Successfully retrieved list of templates", template_list_response_model
    )
    @template_namespace.response(401, "Unauthorized", error_response_model)
    @template_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def get(self):
        """Get the list of all templates with pagination"""
        try:
            args = template_list_input_model.parse_args()

            result = template_service.get_templates(
                args["page"], args["per_page"], args["type"]
            )

            return {
                "message": "Templates successfully fetched",
                "data": result["templates"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200
        except AuthenticationError:
            return {"message": "Unauthorized access", "code": 401}, 401
        except Exception as e:
            template_namespace.abort(500, f"An unexpected error occurred: {str(e)}")

    @template_namespace.doc(security="Bearer Auth")
    @template_namespace.expect(template_create_model)
    @template_namespace.response(201, "Template created successfully", template_model)
    @template_namespace.response(400, "Invalid parameters", error_response_model)
    @template_namespace.response(401, "Unauthorized", error_response_model)
    @template_namespace.response(500, "Internal server error", error_response_model)
    @is_admin
    def post(self):
        """Create a new template"""
        try:
            data = request.json
            user_id = g.user_id
            template_data = template_service.create_template(user_id, data)
            return {
                "message": "Template successfully created",
                "data": template_data,
                "code": 201,
            }, 201
        except InvalidValueError as ve:
            return {"message": str(ve), "code": 400}, 400
        except AuthenticationError:
            return {"message": "Unauthorized access", "code": 401}, 401
        except Exception as e:
            template_namespace.abort(500, f"An unexpected error occurred: {str(e)}")


@template_namespace.route("/<string:template_id>")
class TemplateResource(Resource):
    @template_namespace.doc(security="Bearer Auth")
    @template_namespace.response(200, "Template retrieved successfully", template_model)
    @template_namespace.response(401, "Unauthorized", error_response_model)
    @template_namespace.response(404, "Template not found", error_response_model)
    @template_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def get(self, template_id):
        """Get a specific template by ID"""
        try:
            template_data = template_service.get_template(template_id)
            return {
                "message": "Template retrieved successfully",
                "data": template_data,
                "code": 200,
            }, 200
        except NotFound:
            return {"message": "Template not found", "code": 404}, 404
        except AuthenticationError:
            return {"message": "Unauthorized access", "code": 401}, 401
        except Exception as e:
            template_namespace.abort(500, f"An unexpected error occurred: {str(e)}")

    @template_namespace.doc(security="Bearer Auth")
    @template_namespace.expect(template_update_model)
    @template_namespace.response(200, "Template updated successfully", template_model)
    @template_namespace.response(400, "Invalid parameters", error_response_model)
    @template_namespace.response(401, "Unauthorized", error_response_model)
    @template_namespace.response(404, "Template not found", error_response_model)
    @template_namespace.response(500, "Internal server error", error_response_model)
    @is_admin
    def put(self, template_id):
        """Update a specific template"""
        try:
            data = request.json
            updated_template = template_service.update_template(template_id, data)
            return {
                "message": "Template updated successfully",
                "data": updated_template,
                "code": 200,
            }, 200
        except NotFound:
            return {"message": "Template not found", "code": 404}, 404
        except InvalidValueError as ve:
            return {"message": str(ve), "code": 400}, 400
        except AuthenticationError:
            return {"message": "Unauthorized access", "code": 401}, 401
        except Exception as e:
            template_namespace.abort(500, f"An unexpected error occurred: {str(e)}")

    @template_namespace.doc(security="Bearer Auth")
    @template_namespace.response(200, "Template deleted successfully")
    @template_namespace.response(401, "Unauthorized", error_response_model)
    @template_namespace.response(404, "Template not found", error_response_model)
    @template_namespace.response(500, "Internal server error", error_response_model)
    @is_admin
    def delete(self, template_id):
        """Delete a specific template"""
        try:

            template_service.delete_template(template_id)
            return {"message": "Template deleted successfully", "code": 200}, 200
        except NotFound:
            return {"message": "Template not found", "code": 404}, 404
        except AuthenticationError:
            return {"message": "Unauthorized access", "code": 401}, 401
        except Exception as e:
            template_namespace.abort(500, f"An unexpected error occurred: {str(e)}")
