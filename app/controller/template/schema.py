from flask_restx import fields, reqparse
from .namespace import template_namespace

template_create_model = template_namespace.model(
    "TemplateCreate",
    {
        "title": fields.String(required=True, description="Template name"),
        "template_id": fields.String(required=True, description="Template id"),
        "type": fields.String(required=True, description="Template type/accent"),
        "description": fields.String(required=True, description="Template description"),
        "image": fields.String(required=False, description="Template image URL"),
        "video": fields.String(required=False, description="Template video URL"),
        "aspect_ratio": fields.Raw(required=False, description="Template aspect ratio"),
    },
)

template_update_model = template_namespace.model(
    "TemplateUpdate",
    {
        "title": fields.String(required=False, description="Template name"),
        "type": fields.String(required=False, description="Template type/accent"),
        "description": fields.String(
            required=False, description="Template description"
        ),
        "image": fields.String(required=False, description="Template image URL"),
        "video": fields.String(required=False, description="Template video URL"),
        "available": fields.Boolean(
            required=False, description="Template availability"
        ),
        "aspect_ratio": fields.Raw(required=False, description="Template aspect ratio"),
    },
)

template_model = template_namespace.model(
    "Template",
    {
        "_id": fields.String(required=True, description="Template ID"),
        "template_id": fields.String(
            required=True, description="Template ID (same as name)"
        ),
        "title": fields.String(
            required=True, description="Template title (same as name)"
        ),
        "type": fields.String(
            required=True, description="Template type (same as accent)"
        ),
        "created_at": fields.DateTime(required=True, description="Creation timestamp"),
        "updated_at": fields.DateTime(
            required=True, description="Last update timestamp"
        ),
        "description": fields.String(required=True, description="Template description"),
        "image": fields.String(required=False, description="Template image URL"),
        "video": fields.String(required=False, description="Template video URL"),
        "available": fields.Boolean(required=True, description="Template availability"),
        "aspect_ratio": fields.Raw(required=False, description="Template aspect ratio"),
    },
)

template_list_input_model = template_namespace.model(
    "TemplateListInput",
    {
        "page": fields.Integer(required=True, description="Page number"),
        "per_page": fields.Integer(required=True, description="Items per page"),
    },
)

# Input model for voice list query parameters
template_list_input_model = reqparse.RequestParser()
template_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
template_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
template_list_input_model.add_argument(
    "type",
    type=str,
    required=False,
    choices=["landscape", "portrait"],
    help="Filter by type",
)


template_list_response_model = template_namespace.model(
    "TemplateListResponse",
    {
        "message": fields.String(required=True, description="Response message"),
        "data": fields.List(
            fields.Nested(template_model),
            required=True,
            description="List of templates",
        ),
        "pagination": fields.Raw(required=True, description="Pagination information"),
        "code": fields.Integer(required=True, description="Response code"),
    },
)

error_response_model = template_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(required=True, description="Error message"),
        "code": fields.Integer(required=True, description="Error code"),
    },
)
