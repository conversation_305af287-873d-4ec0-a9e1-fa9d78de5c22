from flask_restx import fields

from app.controller.health.namespace import health_check_namespace

dependencies_model = health_check_namespace.model(
    "Dependencies",
    {
        "database": fields.<PERSON>olean(description="Database service."),
        "cache": fields.<PERSON><PERSON>an(description="Caching service."),
    },
)
health_check_data_model = health_check_namespace.model(
    "HealthCheckData",
    {
        "status": fields.String(
            description="Status of overall platform (success, failure).",
            enum=["success", "failure"],
        ),
        "dependencies": fields.Nested(dependencies_model),
    },
)
health_check_response_model = health_check_namespace.model(
    "HealthCheckResponse",
    {
        "message": fields.String(description="The success message."),
        "data": fields.Nested(health_check_data_model),
    },
)


error_response_model = health_check_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)
