from flask_restx import Resource

from ..auth import login_required

from .namespace import health_check_namespace
from .schema import health_check_response_model, error_response_model

from ...database.connection import MongoDBConnection
from ...cache import get_cache_provider


@health_check_namespace.route("")
class HealthCheckAPI(Resource):
    """
    Health Check API Resource.
    """

    @health_check_namespace.doc(security="Bearer Auth")
    @health_check_namespace.response(
        200,
        "Health Check data fetched successfully.",
        model=health_check_response_model,
    )
    @health_check_namespace.response(
        500,
        "Failed to fetch health check due to a server error.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """
        Health Check API endpoint.

        """
        # Database Health
        try:
            res = MongoDBConnection().connection.db.command("ping")
            db_service = True if "ok" in res else False
        except:
            db_service = False

        # Cache Health
        try:
            cache_client = get_cache_provider()
            cache_service = cache_client.redis_client.ping()
        except:
            cache_service = False

        return {
            "message": "Health Check.",
            "data": {
                "status": "success" if db_service and cache_service else "failure",
                "dependencies": {
                    "database": db_service,
                    "cache": cache_service,
                },
            },
            "code": 200,
        }
