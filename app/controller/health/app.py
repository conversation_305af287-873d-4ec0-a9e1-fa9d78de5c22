from flask import Blueprint
from flask_restx import Api

from .endpoint import health_check_namespace

health_check_blueprint = Blueprint("health_check_blueprint", __name__)

# Define the authorization method to be used in the Swagger UI documentation
# This specifies that the API uses Bearer Token authentication for securing endpoints
authorizations: dict[str, dict[str, str]] = {
    "Bearer Auth": {
        "type": "apiKey",  # Specifies the type of security scheme
        "in": "header",  # Specifies where the API key is passed (in this case, the HTTP header)
        "name": "Authorization",  # Name of the header field to be used
        "description": "Type in the *'Value'* input box below: **'Bearer &lt;JWT_TOKEN&gt;'**, where JWT_TOKEN is your authentication token.",
    },
}

api = Api(
    app=health_check_blueprint,
    title="Health Check API",
    description="API endpoints for health check.",
    authorizations=authorizations,  # Apply the defined authorization method
    security="Bearer Auth",  # Set the global security scheme to require <PERSON><PERSON> for all endpoints
    default="Health Check API",
    default_label="Operations related to health check",
)

api.add_namespace(health_check_namespace)
