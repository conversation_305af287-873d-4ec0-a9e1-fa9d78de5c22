from config import GOOG<PERSON>_CLIENT_ID, GOOGLE_CLIENT_SECRET, REDIRECT_URL
from app.exceptions.auth_exceptions import (
    EmailAlreadyExistsError,
    InvalidPasswordError,
    NoFieldsToUpdateError,
    PasswordNotComplexError,
    UserCreationError,
    UserListFetchError,
    UserNameExistError,
    UserNotFoundError,
)
from app.exceptions.common import (
    BaseExceptionClass,
    ForbiddenAccessError,
    InternalError,
    InvalidAuthError,
    InvalidAuthTokenFormatError,
    InvalidEmailError,
    InvalidEmailOrPasswordError,
    InvalidTokenError,
    InvalidValueError,
)
from app.exceptions.exception_handler import handle_error
from flask import Blueprint
from flask_restx import Api

from flask_dance.contrib.google import make_google_blueprint

from app.constant.google import Google

# Import namespaces from the respective modules
from .endpoints.admin import admin_namespace
from .endpoints.user import user_namespace
from .endpoints.google import google_namespace
from .endpoints.facebook import facebook_namespace
from .endpoints.setting import user_setting_namespace
from .endpoints.waitlist import waitlist_user_namespace
from .endpoints.linkedin import linkedin_namespace

# Create a Flask Blueprint for the authentication module
auth_blueprint = Blueprint("auth", __name__)


# Define the authorization method to be used in the Swagger UI documentation
# This specifies that the API uses Bearer Token authentication for securing endpoints
authorizations: dict[str, dict[str, str]] = {
    "Bearer Auth": {
        "type": "apiKey",  # Specifies the type of security scheme
        "in": "header",  # Specifies where the API key is passed (in this case, the HTTP header)
        "name": "Authorization",  # Name of the header field to be used
        "description": "Type in the *'Value'* input box below: **'Bearer &lt;JWT_TOKEN&gt;'**, where JWT_TOKEN is your authentication token.",
    },
}

# Create a Google Blueprint for Google login
google_login = make_google_blueprint(
    client_id=GOOGLE_CLIENT_ID,  # Replace with your Google client ID
    client_secret=GOOGLE_CLIENT_SECRET,  # Replace with your Google client secret
    redirect_url=REDIRECT_URL,
    scope=Google.LOGIN_SCOPE.value,
    offline=True,
    reprompt_consent=True,
)


# Initialize the Flask-RESTx API object, attaching it to the authentication Blueprint
# and configuring it with title, description, and authorization details
api = Api(
    auth_blueprint,
    title="Authentication Management API",
    description="API endpoints for user registration, login, and logout functionalities.",
    authorizations=authorizations,  # Apply the defined authorization method
    security="Bearer Auth",  # Set the global security scheme to require Bearer Auth for all endpoints
)


# Register the admin and user namespaces with the API to include their endpoints
# in the Swagger UI documentation under the Authentication Management API
api.add_namespace(admin_namespace)  # Add admin-related endpoints
api.add_namespace(user_namespace)  # Add user-related endpoints
api.add_namespace(waitlist_user_namespace)
api.add_namespace(google_namespace)  # Add user-related endpoints
api.add_namespace(facebook_namespace)  # Add user-related endpoints
api.add_namespace(user_setting_namespace)  # Add user-
api.add_namespace(linkedin_namespace)

# Register the error handler for multiple exceptions
api.errorhandler(BaseExceptionClass)(handle_error)
api.errorhandler(InvalidEmailError)(handle_error)
api.errorhandler(PasswordNotComplexError)(handle_error)
api.errorhandler(EmailAlreadyExistsError)(handle_error)
api.errorhandler(UserCreationError)(handle_error)
api.errorhandler(UserNotFoundError)(handle_error)
api.errorhandler(InvalidValueError)(handle_error)
api.errorhandler(NoFieldsToUpdateError)(handle_error)
api.errorhandler(UserListFetchError)(handle_error)
api.errorhandler(InvalidEmailOrPasswordError)(handle_error)
api.errorhandler(InvalidAuthTokenFormatError)(handle_error)
api.errorhandler(InvalidAuthError)(handle_error)
api.errorhandler(InvalidPasswordError)(handle_error)
api.errorhandler(ForbiddenAccessError)(handle_error)
api.errorhandler(InvalidTokenError)(handle_error)
api.errorhandler(InternalError)(handle_error)
api.errorhandler(UserNameExistError)(handle_error)
