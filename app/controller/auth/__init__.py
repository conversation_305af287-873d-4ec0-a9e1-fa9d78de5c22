from functools import wraps
from flask import g, request
from app.cache import get_cache_provider
from app.constant.user_enums import UserRole
from app.exceptions.common import (
    ForbiddenAccessError,
    InvalidAuthError,
    InvalidAuthTokenFormatError,
)
from app.repositories.user_repository import AuthRepository

from .utils import decode_auth_token

cache_provider = get_cache_provider()

auth_repository = AuthRepository()


def login_required(f):
    """
    Decorator function to enforce authentication on protected routes.

    This decorator checks for the presence and validity of an authentication token in the Authorization header of the request.
    It validates the token's format, decodes it to extract the user ID, IP address, and user agent,
    and verifies these details against the current request's context and stored session data in Redis.
    If the token is valid and corresponds to an active session, the user ID is attached to Flask's global proxy object `g` for use in the protected endpoint.
    If any check fails, an appropriate error message is returned.

    Parameters:
        f (function): The Flask route function to wrap with this decorator.

    Returns:
        function: A decorated function that executes the original function if authentication succeeds, or returns an error message if authentication fails.

    Raises:
        401 Unauthorized: If the authentication token is missing, invalid, expired, or if there's a mismatch in IP address or User-Agent.
        403 Forbidden: If the authentication token is missing from the request headers.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Retrieve the Authorization header from the request
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise InvalidAuthError(description="Authentication token is missing.")

        # Split the header to get the token part
        parts = auth_header.split(" ")
        if len(parts) != 2 or parts[0] != "Bearer":
            raise InvalidAuthTokenFormatError()

        # Decode the token to retrieve the user ID, IP address, and user agent
        token = parts[1]
        user_id, ip_address, user_agent, role = decode_auth_token(auth_token=token)

        # Initialize sales_user_id as None
        sales_user_id = None

        if role == UserRole.ADMIN.value:
            # Attempt to get sales_user_id from the request query parameters if the user is an admin
            if request.method in ["GET", "DELETE"] and request.args.get(
                "sales_user_id"
            ):
                sales_user_id = request.args.get("sales_user_id")
            # Attempt to get sales_user_id from the request body if the user is an admin
            if request.method in [
                "POST",
                "PUT",
            ] and request.json.get("sales_user_id"):
                sales_user_id = request.json.get("sales_user_id")

        # Verify the existence and validity of the session in Redis
        session_key = f"sess_{user_id}"
        if not cache_provider.exists(session_key):
            raise ForbiddenAccessError(description="Session expired or does not exist.")

        # # Validate the IP and User-Agent against the current request's context
        # request_ip = request.remote_addr
        # request_ua = request.headers.get("User-Agent")
        # if ip_address != request_ip or user_agent != request_ua:
        #     raise ForbiddenAccessError(description="IP or User Agent mismatch.")

        # Ensure the user ID corresponds to a valid user in the database
        if not auth_repository.user_already_exists(user_id):
            raise ForbiddenAccessError(description="Invalid user ID.")

        # # Validate the status of the user
        # if status != UserStatus.ACTIVE.value:
        #     raise ForbiddenAccessError(description="Unauthorized or inactive user.")

        # Attach the user_id to Flask's g global proxy object for use in the protected route
        g.user_id = user_id
        g.sales_user_id = sales_user_id

        # Proceed to the original function if authentication is successful
        return f(*args, **kwargs)

    return decorated_function


def is_admin(f):
    """
    Decorator that checks if the current user is an admin before allowing access to a protected route, in addition to enforcing the authentication on protected routes.

    This decorator extracts the user ID from the Authorization header's token, retrieves the corresponding user from the database,
    and checks if the user has an admin role. If the user is confirmed to be an admin, the protected route is executed.
    If the user is not an admin, or if any validation check fails (such as token expiration or invalid format), an appropriate error message is returned.

    Parameters:
        f (function): The view function or Flask route that the decorator is applied to.

    Returns:
        function: The decorated function, which includes the admin role check.

    Raises:
        401 Unauthorized: If the authorization token is missing, invalid, expired, or the user does not have an admin role.
        403 Forbidden: If the authorization token is missing from the request headers or the user is not authorized as an admin.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise InvalidAuthError(description="Authentication token is missing.")

        # Split the header to get the token part
        parts = auth_header.split(" ")
        if len(parts) != 2 or parts[0] != "Bearer":
            raise InvalidAuthTokenFormatError()

        # Decode the token to retrieve the user ID, IP address, and user agent
        token = parts[1]
        user_id, ip_address, user_agent, role = decode_auth_token(auth_token=token)

        # Verify the existence and validity of the session in Redis
        session_key = f"sess_{user_id}"
        if not cache_provider.exists(session_key):
            raise ForbiddenAccessError(description="Session expired or does not exist.")

        # Validate the IP and User-Agent against the current request's context
        request_ip = request.remote_addr
        request_ua = request.headers.get("User-Agent")
        if ip_address != request_ip or user_agent != request_ua:
            raise ForbiddenAccessError(description="IP or User Agent mismatch.")

        # Ensure the user ID corresponds to a valid user in the database
        if not auth_repository.user_already_exists(user_id):
            raise ForbiddenAccessError(description="Invalid user ID.")

        # # Validate the status of the user
        # if status != UserStatus.ACTIVE.value:
        #     raise ForbiddenAccessError(description="Unauthorized or inactive user.")

        # Check if user is an admin
        if role == UserRole.ADMIN.value:
            # Attach the user_id to Flask's g global proxy object for use in the protected route
            g.user_id = user_id
        else:
            raise ForbiddenAccessError(
                description="Unauthorized. Admin access required."
            )

        # Proceed to the original function if authentication is successful
        return f(*args, **kwargs)

    return decorated_function
