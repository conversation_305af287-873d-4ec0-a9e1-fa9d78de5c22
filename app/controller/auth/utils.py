from datetime import datetime, timedelta
import re
import secrets
import string
import ast
from flask_restx import ValidationError
import jwt
from typing import Optional
from cryptography.fernet import Fernet

import config
from app.cache import get_cache_provider

from app.exceptions.common import InternalError, InvalidAuthError, InvalidTokenError


from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

# Load the secret key from the configuration for encryption and token operations
SECRET_KEY = config.SECRET_KEY

cache_provider = get_cache_provider()


def generate_numeric_otp():
    # Generate a random integer between 0 and 999999
    otp = secrets.randbelow(1000000)
    # Convert to string and pad with leading zeros if necessary
    return f"{otp:06d}"


# TODO :- Add more validations
def validate_email(email_id: str) -> bool:
    # if not email_id:
    #     return False
    if len(email_id) == 0:
        return False
    return contains_substrings(email_id)


def contains_substrings(input_string):
    substrings = ast.literal_eval(config.EMAIL_SUBSTRING)
    return any(substring in input_string for substring in substrings)


def password_validator(password):
    if len(password) < 8:
        raise ValidationError("Password must be at least 8 characters long.")
    if not re.search(r"[A-Z]", password):
        raise ValidationError("Password must contain at least one uppercase letter.")
    if not re.search(r"[a-z]", password):
        raise ValidationError("Password must contain at least one lowercase letter.")
    if not re.search(r"\d", password):
        raise ValidationError("Password must contain at least one number.")
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationError("Password must contain at least one special character.")


def is_password_complex(password: str) -> bool:
    """
    Verifies if the provided password meets specified complexity requirements.

    A password is deemed complex if it:
    - Is at least 8 characters long.
    - Contains both uppercase and lowercase letters.
    - Includes at least one digit.
    - Has at least one special character, such as !@#$%^&*.

    Parameters:
    - password (str): The password to be validated.

    Returns:
    - bool: True if the password is complex according to the criteria, False otherwise.
    """

    min_length = 8
    if len(password) < min_length:
        return False

    if not re.search(r"[A-Z]", password):
        # Checks for presence of uppercase letters
        return False

    if not re.search(r"[a-z]", password):
        # Ensures at least one lowercase letter is included
        return False

    if not re.search(r"\d", password):
        # Verifies the inclusion of digits
        return False

    if not re.search(r"[!@#$%^&*]", password):
        # Confirms the presence of special characters
        return False

    return True


def encrypt_data(data: str) -> str:
    """
    Encrypts the provided data using Fernet symmetric encryption.

    Parameters:
    - data (str): The plaintext data to encrypt.

    Returns:
    - str: The encrypted data as a base64 encoded string.
    """

    fernet = Fernet(SECRET_KEY)
    encrypted = fernet.encrypt(data.encode())

    return encrypted.decode()


def decrypt_data(encrypted_data: str) -> str:
    """
    Decrypts the provided data using Fernet symmetric encryption.

    Parameters:
    - encrypted_data (str): The encrypted data as a base64 encoded string to decrypt.

    Returns:
    - str: The decrypted data in plaintext.
    """

    fernet = Fernet(SECRET_KEY)
    decrypted = fernet.decrypt(encrypted_data.encode())

    return decrypted.decode()


def encode_auth_token(user_id: str, user_agent: str, ip_address: str, role: str) -> str:
    """
    Generates an encrypted authentication token for a user, incorporating user ID, user agent, and IP address.

    The user ID is encrypted before being embedded in the token payload for enhanced security.

    Parameters:
    - user_id (str): The user's unique identifier.
    - user_agent (str): The user agent string of the user's device.
    - ip_address (str): The IP address of the user's device.
    - role (str): Specifies whether the user is a normal user or an admin.

    Returns:
    - str: The encrypted authentication token.

    Raises:
    - Exception: If there's an error during token generation or encryption.
    """

    encrypted_user_id = encrypt_data(user_id)

    try:
        payload = {
            "exp": datetime.utcnow() + timedelta(days=1, seconds=5),
            "iat": datetime.utcnow(),
            "sub": encrypted_user_id,
            "ip": ip_address,
            "ua": user_agent,
            "role": role,
        }

        auth_token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")
        encrypted_auth_token = encrypt_data(auth_token)

        return encrypted_auth_token

    except Exception as e:
        print(f"Exception in encoding auth token: {str(e)}")
        raise


def decode_auth_token(auth_token: str) -> tuple[str, str, str, str, str]:
    """
    Decodes an encrypted authentication token to retrieve the original user ID, IP address, user agent, status, and role.

    Parameters:
    - auth_token (str): The encrypted authentication token to decode.

    Returns:
    - tuple: On success, returns a tuple with the decrypted user ID, IP address, user agent, status, and role.
             On failure, returns a tuple with an error message as the first element and None for the other four.
    """

    try:
        decrypted_auth_token = decrypt_data(auth_token)
        payload = jwt.decode(decrypted_auth_token, SECRET_KEY, algorithms=["HS256"])
        encrypted_user_id = payload["sub"]

        user_id = decrypt_data(encrypted_data=encrypted_user_id)
        ip_address: str = payload["ip"]
        user_agent: str = payload["ua"]
        role: str = payload["role"]

        return user_id, ip_address, user_agent, role

    except Exception as e:
        raise InvalidAuthError()


def encode_refresh_token(user_id: str, exp: Optional[datetime] = None) -> str:
    """
    Generates an encrypted refresh token for a user, incorporating user ID and expiration time.

    The user ID is encrypted before being embedded in the token payload for enhanced security.

    Parameters:
    - user_id (str): The user's unique identifier.
    - exp (Optional[datetime]): The expiration time for the refresh token. If not provided,
      it defaults to 30 days from the current time.

    Returns:
    - str: The encrypted refresh token.

    Raises:
    - Exception: If there's an error during token generation or encryption.
    """

    encrypted_user_id = encrypt_data(user_id)

    try:
        if exp is None:
            exp = datetime.utcnow() + timedelta(days=30)

        payload = {
            "exp": exp,
            "iat": datetime.utcnow(),
            "sub": encrypted_user_id,
        }

        refresh_token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")
        encrypted_refresh_token = encrypt_data(refresh_token)

        return encrypted_refresh_token

    except Exception as e:
        print(f"Exception in encoding refresh token: {str(e)}")
        raise


def decode_refresh_token(refresh_token: str) -> str:
    """
    Decodes an encrypted refresh token to retrieve the original user ID.

    Parameters:
    - refresh_token (str): The encrypted refresh token to decode.

    Returns:
    - str: On success, returns the decrypted user ID.

    Raises:
    - InvalidAuthError: If the token is invalid or has expired.
    """
    try:
        decrypted_refresh_token = decrypt_data(refresh_token)
        payload = jwt.decode(decrypted_refresh_token, SECRET_KEY, algorithms=["HS256"])
        encrypted_user_id = payload["sub"]
        user_id = decrypt_data(encrypted_data=encrypted_user_id)
        return user_id

    except jwt.ExpiredSignatureError:
        raise InvalidAuthError("Refresh token has expired")
    except Exception as e:
        raise InvalidAuthError(f"Invalid refresh token: {str(e)}")


def generate_reset_token(email_id: str, expire_time_seconds: int = 3600) -> str:
    """
    Generates a secure, unique token for password reset purposes and stores it in Redis with an expiration.

    Parameters:
    - email_id (str): The email_id for which the reset token is generated.
    - expire_time_seconds (int): The time in seconds until the token expires. Default is 3600 seconds (1 hour).

    Returns:
    - str: The generated token which can be used to reset the password.

    Raises:
    - Exception: If there's an error during token generation or storage.
    """

    try:
        token = secrets.token_urlsafe()

        # Save the token in Redis with the specified expiration time
        # Use the token itself as the key and the email_id as the value
        cache_provider.setex(token, expire_time_seconds, email_id)

        return token

    except Exception as e:
        raise InternalError(description=f"Failed to store token.")


def verify_reset_token(token: str) -> str:
    """
    Verifies a given password reset token by checking its existence in Redis.

    Parameters:
    - token (str): The password reset token to verify.

    Returns:
    - str: The email_id associated with the valid token, or None if the token is invalid or expired.

    Raises:
    - InvalidTokenError: If the token is invalid or expired.
    """

    # Retrieve the email_id associated with the token from Redis
    email_id = cache_provider.get(token)
    if email_id:
        # Ensure the token is removed upon verification to prevent reuse
        cache_provider.delete(token)

        return email_id  # Decoding from bytes to string

    else:
        raise InvalidTokenError()


def serialize_user(user):
    """
    Serializes a single user document by converting '_id' and 'uploader_id' fields
    from ObjectId to str.

    Parameters:
    - user (dict): The user document to serialize.

    Returns:
    - dict: The serialized user document.
    """

    serialized_user = {
        "_id": str(user["_id"]),
        "email": user.get("email"),
        "name": user.get("name"),
        "role": user.get("role", "").lower(),
        "created_at": str(user.get("created_at")),
        "updated_at": str(user.get("updated_at")),
        "login_method": user.get("login_method"),
        "status": user.get("status"),
        "profile_picture": user.get("profile_picture"),
        "customer_id": user.get("customer_id"),
        "firebase_token": user.get("firebase_token", None),
        "account_status": user.get("account_status", None),
        "email_sent_time": str(user.get("email_sent_time")),
    }
    return serialized_user


def serialize_users(users):
    """
    Serializes a list of user documents by converting '_id' and 'uploader_id' fields
    from ObjectId to str for each user in the list.

    Parameters:
    - users (list of dict): The list of user documents to serialize.

    Returns:
    - list of dict: The list of serialized user documents.
    """

    return [serialize_user(user) for user in users]


def generate_password(length=16):
    """
    Generates a strong password.

    Parameters:
    - length (int): The length of the password. Default is 12 characters.

    Returns:
    - str: The generated password.
    """
    alphabet = string.ascii_letters + string.digits + string.punctuation
    password = "".join(secrets.choice(alphabet) for i in range(length))

    while not is_password_complex(password):
        password = "".join(secrets.choice(alphabet) for i in range(length))
    return password


def create_secret_key():
    # Define a password or secret text
    password = "cinny"

    # Specify the desired key length (e.g., 32 bytes for a 256-bit key)
    key_length = 32

    # Specify a salt value (ideally, this should be generated randomly and stored securely)
    salt = b"cinny"

    # Specify the number of iterations for the key derivation function
    iterations = 100000

    # Create a PBKDF2HMAC instance with the desired hash algorithm (e.g., SHA-256)
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=key_length,
        salt=salt,
        iterations=iterations,
    )

    # Derive the key from the password
    key = kdf.derive(password.encode())

    # Optionally, encode the key as a base64-encoded string
    base64_key = base64.b64encode(key).decode()

    print(f"Derived key (bytes): {key}")
    print(f"Derived key (base64): {base64_key}")


def get_token_from_request(request):
    auth_header = request.headers.get("Authorization")
    if auth_header:
        return auth_header.split(" ")[1]  # Assuming 'Bearer <token>'
    return None
