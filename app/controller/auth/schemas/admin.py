from flask_restx import fields

from ..namespaces.admin import admin_namespace

user_list_query_model_dict: dict[str, str] = {
    "uname": "Partial username to search",
    "query": "Partial email and name to search",
    "status": "Filter by user status (active, awaiting, draft)",
    "offset": "Offset of the list of users",
    "limit": "Limit number of users to retrieve",
    "days": "Filter users created or updated in the last N days",
    "role": "Filter by user role (user, admin)",
}

admin_signup_request_model = admin_namespace.model(
    "AdminSignup",
    {
        "email": fields.String(
            required=True, description="The admin's chosen email."
        ),
        "password": fields.String(
            required=True, description="The admin's chosen password."
        ),
        "name": fields.String(description="The name of the admin."),
    },
    strict=True,
)

user_update_request_model = admin_namespace.model(
    "UserUpdate",
    {
        "name": fields.String(description="The name of the user."),
        "email": fields.String(description="Email address of the user"),
        "password": fields.String(description="The admin's chosen password."),
    },
    strict=True,
)

admin_creation_response_model = admin_namespace.model(
    "AdminCreationResponse",
    {
        "message": fields.String(example="Admin created successfully."),
        "data": fields.Nested(
            admin_namespace.model(
                "UserId",
                {
                    "id": fields.String(),
                    "email": fields.String(),
                    "name": fields.String(),
                    "created_at": fields.String(),
                    "updated_at": fields.String(),
                    "role": fields.String(example="admin"),
                },
            )
        ),
        "code": fields.Integer(example=201),
    },
)

user_update_response_model = admin_namespace.model(
    "UserUpdateResponse",
    {
        "message": fields.String(example="User updated successfully"),
        "code": fields.Integer(example=200),
    },
)

activation_email_response_model = admin_namespace.model(
    "ActivationEmailResponse",
    {
        "message": fields.String(example="Activation email sent successfully"),
        "code": fields.Integer(example=200),
    },
)

user_counts_response_model = admin_namespace.model(
    "UserUpdateResponse",
    {
        "message": fields.String(example="User counts fetched successfully"),
        "data": fields.Nested(
            admin_namespace.model(
                "UserCounts",
                {
                    "active": fields.Integer(),
                    "total": fields.Integer(),
                },
            )
        ),
        "code": fields.Integer(example=200),
    },
)

user_list_response_model = admin_namespace.model(
    "UserListResponse",
    {
        "message": fields.String(description="The success message."),
        "data": fields.List(
            fields.Nested(
                admin_namespace.model(
                    "User",
                    {
                        "id": fields.String(
                            required=True,
                            description="The unique identifier of the user",
                        ),
                        "email": fields.String(
                            required=True, description="Email address of the user"
                        ),
                        "name": fields.String(description="Name of the user"),
                        "role": fields.String(description="Role of the user"),
                    },
                )
            ),
        ),
        "meta": fields.Nested(
            admin_namespace.model(
                "Meta",
                {
                    "count": fields.Integer(description="Total count of users"),
                },
            )
        ),
        "code": fields.Integer(example=200),
    },
)

admin_error_response_model = admin_namespace.model(
    "AdminErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)
