from flask_restx import fields, reqparse

# from app.constant.user_enums import ProfileImageExtension
from ..namespaces.user import user_namespace, waitlist_user_namespace
from ..namespaces.setting import user_setting_namespace


user_profile_response_model = user_namespace.model(
    "UserProfile",
    {
        "message": fields.String(description="The success message."),
        "data": fields.Nested(
            user_namespace.model(
                "User",
                {
                    "id": fields.String(
                        required=True, description="The unique identifier of the user"
                    ),
                    "email": fields.String(
                        required=True, description="Email address of the user"
                    ),
                    "contact": fields.Integer(example=9876543210),
                    "name": fields.String(description="Name of the user"),
                    "status": fields.String(description="Current status of the user"),
                    "role": fields.String(description="Role of the user"),
                },
            )
        ),
        "code": fields.Integer(example=200),
    },
)

user_signup_request_model = user_namespace.model(
    "UserSignup",
    {
        "email": fields.String(required=True, description="The user's chosen email."),
        "password": fields.String(
            required=True,
            description="The user's chosen password.",
        ),
        "name": fields.String(required=True, description="The name of the user."),
    },
    strict=True,
)


user_creation_response_model = user_namespace.model(
    "UserCreationResponse",
    {
        "message": fields.String(example="User created successfully."),
        "data": fields.Nested(
            user_namespace.model(
                "UserId",
                {
                    "id": fields.String(),
                    "email": fields.String(),
                    "name": fields.String(),
                    "created_at": fields.String(),
                    "updated_at": fields.String(),
                },
            )
        ),
        "code": fields.Integer(example=201),
    },
)

user_verification_response_model = user_namespace.model(
    "UserCreationResponse",
    {
        "message": fields.String(example="User account verify successfully."),
        "code": fields.Integer(example=201),
    },
)


presigned_url_response_model = user_namespace.model(
    "PresignedUrlResponse",
    {
        "presigned_url": fields.String(
            description="The presigned URL for uploading the file"
        ),
        "code": fields.Integer(example=201),
    },
)


post_presigned_model = user_namespace.model(
    "PostPresigned",
    {
        "folder": fields.String(required=True, description="Folder path in S3"),
        "file_name": fields.String(
            required=True, description="Name of the file to upload"
        ),
    },
)

presigned_url_model = user_namespace.model(
    "PresignedURL",
    {
        "folder": fields.String(required=True, description="Folder path in S3"),
        "file_name": fields.String(required=True, description="Name of the file in S3"),
        "content_type": fields.String(
            required=False, description="Content type of the file like image/png"
        ),
    },
)

user_login_request_model = user_namespace.model(
    "UserLogin",
    {
        "email": fields.String(required=True, description="The user's email id."),
        "password": fields.String(required=True, description="The user's password."),
    },
    strict=True,
)

change_password_request_model = user_namespace.model(
    "PasswordChange",
    {
        "current_password": fields.String(
            required=True, description="The user's current password."
        ),
        "new_password": fields.String(
            required=True, description="The user's new password."
        ),
    },
    strict=True,
)

forget_password_request_model = user_namespace.model(
    "PasswordResetRequest",
    {
        "email": fields.String(required=True, description="The user's email id."),
    },
    strict=True,
)

reset_password_request_model = user_namespace.model(
    "PasswordReset",
    {
        "new_password": fields.String(required=True, description="The new password."),
        "token": fields.String(
            required=True, description="The reset token for password reset."
        ),
    },
    strict=True,
)

auth_token_response_model = user_namespace.model(
    "AuthTokenResponse",
    {
        "auth_token": fields.String(
            required=True, description="The authentication token for the user"
        ),
        "code": fields.Integer(example=200),
    },
)

logout_response_model = user_namespace.model(
    "LogoutResponse",
    {
        "message": fields.String(
            required=True, description="Confirmation message for successful logout"
        ),
        "code": fields.Integer(example=200),
    },
)

password_change_response_model = user_namespace.model(
    "PasswordChangeResponse",
    {
        "message": fields.String(
            required=True,
            description="Confirmation message for successful password change",
        ),
        "code": fields.Integer(example=200),
    },
)

forget_password_response_model = user_namespace.model(
    "ForgetPasswordResponse",
    {
        "message": fields.String(
            required=True,
            description="Confirmation message that password reset email has been sent",
        ),
        "code": fields.Integer(example=200),
    },
)

reset_password_response_model = user_namespace.model(
    "ResetPasswordResponse",
    {
        "message": fields.String(
            required=True,
            description="Confirmation message for successful password reset",
        ),
        "code": fields.Integer(example=200),
    },
)

error_response_model = user_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)

update_profile_request_model = user_namespace.model(
    "UpdateProfile",
    {
        "name": fields.String(required=False, description="The name of the user"),
        "profile_picture": fields.String(
            required=False, description="The profile picture of the user"
        ),
        "firebase_token": fields.String(
            required=False, description="firebase cloud token update"
        ),
    },
)

user_setting_model = user_setting_namespace.model(
    "UserSetting",
    {
        "_id": fields.String(
            readonly=True, description="The user setting unique identifier"
        ),
        "user_id": fields.String(readonly=True, description="The user ID"),
        "default_avatar": fields.String(
            required=False, description="The default avatar"
        ),
        "default_voice": fields.String(required=False, description="The default voice"),
        "default_template": fields.String(
            required=False, description="The default template"
        ),
        "auto_thumbnail": fields.Boolean(
            required=False, description="auto thumbnail image generation"
        ),
        "auto_youtube_metadata": fields.Boolean(
            required=False, description="auto description generate"
        ),
        "push_notifications": fields.Boolean(
            required=False, description="push notifications update"
        ),
        "email_notifications": fields.Boolean(
            required=False, description="email notifications update"
        ),
        "user_interest_domain": fields.List(
            fields.String, required=False, description="User interest domains"
        ),
        "created_at": fields.DateTime(readonly=True, description="Creation timestamp"),
        "updated_at": fields.DateTime(
            readonly=True, description="Last update timestamp"
        ),
    },
)

update_user_setting_model = user_setting_namespace.model(
    "UpdateUserSetting",
    {
        "default_avatar": fields.String(
            required=False, description="The default avatar"
        ),
        "default_voice": fields.String(required=False, description="The default voice"),
        "default_template": fields.String(
            required=False, description="The default template"
        ),
        "user_interest_domain": fields.List(
            fields.String, required=False, description="User interest domains"
        ),
        "auto_thumbnail": fields.Boolean(
            required=False, description="auto thumbnail image generation"
        ),
        "auto_youtube_metadata": fields.Boolean(
            required=False, description="auto description generate"
        ),
        "push_notifications": fields.Boolean(
            required=False, description="push notifications update"
        ),
        "email_notifications": fields.Boolean(
            required=False, description="email notifications update"
        ),
    },
)

refresh_token_request_model = user_namespace.model(
    "RefreshTokenRequest",
    {"refresh_token": fields.String(required=True, description="Refresh token")},
)


deactivate_account_response_model = user_namespace.model(
    "DeactivateAccountResponse",
    {
        "message": fields.String(description="Success message"),
        "code": fields.Integer(description="HTTP status code"),
    },
)


waitlist_request_model = waitlist_user_namespace.model(
    "UserWaitListModel",
    {
        "email": fields.String(required=True, description="The user's chosen email."),
        "organization": fields.String(
            required=False,
            description="The user's organization.",
        ),
        "job_title": fields.String(
            required=False,
            description="The user's job_title.",
        ),
        "name": fields.String(required=True, description="The name of the user."),
    },
    strict=True,
)

update_waitlist_request_model = waitlist_user_namespace.model(
    "UpdateWaitListModel",
    {
        "status": fields.String(required=True, description="The user's chosen email."),
    },
    strict=True,
)


# Input model for avatar list query parameters
waitlist_list_input_model = reqparse.RequestParser()
waitlist_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
waitlist_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
waitlist_list_input_model.add_argument(
    "status", type=str, required=False, help="waitlist status"
)
