from flask import g, make_response, request, redirect
from flask_restx import Resource
from functools import wraps
import requests
import json
import os
from datetime import datetime
import urllib.parse
from typing import Dict, Any
from .. import login_required
from ..namespaces.linkedin import linkedin_namespace
from app.repositories.user_repository import AuthRepository

from config import FRONTEND_ENDPOINT

user_repository = AuthRepository()


# Helper functions
class ResponseHandler:
    @staticmethod
    def success_response(message: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        response = {"status": "success", "message": message, "data": data}
        return response


class HelperService:
    @staticmethod
    def convert_timestamp_to_seconds(timestamp: float) -> int:
        return int(timestamp / 1000)


class ConfigService:
    @staticmethod
    def get(key: str) -> str:
        return os.getenv(key)


# Authentication decorator
def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            linkedin_namespace.abort(401, "Authorization header is missing")

        # Add your authentication logic here
        # For example, verify the Bearer token

        return f(*args, **kwargs)

    return decorated


@linkedin_namespace.route("/linkedin/auth")
class LinkedInAuth(Resource):
    @linkedin_namespace.doc(security="Bearer Auth")
    @login_required
    def get(self):
        """LinkedIn authentication endpoint"""
        user_id = getattr(g, "user_id")

        linkedin_auth_url = "https://www.linkedin.com/oauth/v2/authorization"
        params = {
            "response_type": "code",
            "client_id": ConfigService.get("LINKEDIN_CLIENT_ID"),
            "redirect_uri": ConfigService.get("LINKEDIN_CALLBACK_URL"),
            "scope": "openid email profile w_member_social",
            "state": user_id,
        }

        auth_url = f"{linkedin_auth_url}?{urllib.parse.urlencode(params)}"

        return {
            "message": "Authentication URL generated successfully",
            "redirect_link": auth_url,
        }


@linkedin_namespace.route("/linkedin/callback")
class LinkedInCallback(Resource):
    @linkedin_namespace.param("code", "Authorization code from LinkedIn")
    @linkedin_namespace.param("state", "State parameter containing redirect URL")
    def get(self):
        """LinkedIn callback endpoint"""
        code = request.args.get("code")
        state = request.args.get("state")

        if not code:
            linkedin_namespace.abort(400, "Missing required 'code' parameter")
        if not state:
            linkedin_namespace.abort(400, "Missing required 'state' parameter")

        try:
            user_id = state
            # Exchange code for access token
            token_response = requests.post(
                "https://www.linkedin.com/oauth/v2/accessToken",
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "redirect_uri": ConfigService.get("LINKEDIN_CALLBACK_URL"),
                    "client_id": ConfigService.get("LINKEDIN_CLIENT_ID"),
                    "client_secret": ConfigService.get("LINKEDIN_CLIENT_SECRET"),
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
            token_response.raise_for_status()
            token_data = token_response.json()

            # Get user profile
            profile_response = requests.get(
                "https://api.linkedin.com/v2/userinfo",
                headers={
                    "Authorization": f"Bearer {token_data['access_token']}",
                    "Content-Type": "application/json",
                },
            )
            profile_response.raise_for_status()
            profile_data = profile_response.json()

            # Combine token and profile data
            token_data.update(profile_data)
            token_data["expires_in"] = (
                HelperService.convert_timestamp_to_seconds(
                    datetime.now().timestamp() * 1000
                )
                + 1800
            )  # 30 min limited

            data_to_update = {"linkedin_credentials": token_data}

            user_repository.update_user(user_id, **data_to_update)

            redirect_url = f"{FRONTEND_ENDPOINT}/auth/linkedin-success"

            return make_response(redirect(redirect_url))

        except json.JSONDecodeError:
            linkedin_namespace.abort(400, "Invalid state parameter format")
        except requests.exceptions.RequestException as e:
            error_message = str(e)
            if hasattr(e.response, "json"):
                error_data = e.response.json()
                error_message = str(error_data)
            return error_message
