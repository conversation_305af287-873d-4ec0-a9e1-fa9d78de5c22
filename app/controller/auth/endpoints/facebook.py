from flask import request, redirect
from flask_restx import Resource, fields
import requests


from ..namespaces.facebook import facebook_namespace

from config import META_CLIENT_ID, META_CLIENT_SECRET, META_REDIRECT_URI


# Replace these with your actual values
API_VERSION = "v20.0"  # Use the latest version

# Define models
upload_model = facebook_namespace.model(
    "UploadVideo",
    {
        "access_token": fields.String(required=True, description="The access token"),
        "video_url": fields.String(
            required=True, description="The URL of the video to upload"
        ),
        "caption": fields.String(
            required=True, description="The caption for the video"
        ),
    },
)


@facebook_namespace.route("/facebook/connect")
class ConnectInstagram(Resource):
    def get(self):
        """Initiate the Facebook Login process for Instagram Business"""
        scopes = "pages_show_list,pages_read_engagement,business_management,instagram_basic,instagram_content_publish"
        auth_url = f"https://www.facebook.com/{API_VERSION}/dialog/oauth"
        params = {
            "client_id": META_CLIENT_ID,
            "redirect_uri": META_REDIRECT_URI,
            "scope": scopes,
            "response_type": "code",
            "auth_type": "reauthenticate,rerequest",
            "display": "page",
        }
        full_auth_url = auth_url + "?" + "&".join(f"{k}={v}" for k, v in params.items())
        return redirect(full_auth_url)


@facebook_namespace.route("/facebook/callback")
class InstagramCallback(Resource):
    def get(self):
        """Handle the OAuth callback and get the access token"""
        code = request.args.get("code")
        if not code:
            return {"message": "No code provided"}, 400

        token_url = f"https://graph.facebook.com/{API_VERSION}/oauth/access_token"
        params = {
            "client_id": META_CLIENT_ID,
            "client_secret": META_CLIENT_SECRET,
            "redirect_uri": META_REDIRECT_URI,
            "code": code,
        }

        response = requests.get(token_url, params=params)
        if response.status_code != 200:
            return {
                "message": "Failed to get access token",
                "details": response.text,
            }, 400

        access_token = response.json().get("access_token")
        return {"access_token": access_token}, 200
