from flask import request
import logging
from flask_restx import Resource
from werkzeug.exceptions import NotFound
from app.exceptions import handle_errors
from app.repositories.waitlist_repository import WaitlistRepository
from .. import is_admin
from ..namespaces.user import waitlist_user_namespace
from ..schemas.user import (
    waitlist_request_model,
    error_response_model,
    update_waitlist_request_model,
    waitlist_list_input_model,
)
from app.constant.user_enums import WaitlistStatus


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

waitlist_repository = WaitlistRepository()


@waitlist_user_namespace.route("/add-waitlist")
class UserWaitlistResource(Resource):
    @waitlist_user_namespace.doc("create_or_update_waitlist")
    @waitlist_user_namespace.expect(waitlist_request_model)
    @waitlist_user_namespace.response(
        201, "Waitlist created successfully", model=waitlist_request_model
    )
    @waitlist_user_namespace.response(
        200, "Waitlist updated successfully", model=waitlist_request_model
    )
    @waitlist_user_namespace.response(400, "Bad request", model=error_response_model)
    @waitlist_user_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @waitlist_user_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @handle_errors
    def post(self):
        """Create or update waitlist"""

        data = request.json
        email = data.get("email")

        existing_setting = waitlist_repository.get_waitlist_by_email(email)

        if existing_setting:
            # Update existing setting
            updated_setting = waitlist_repository.update_waitlist(
                str(existing_setting["_id"]), data
            )
            return {
                "message": "Waitlist updated successfully",
                "data": waitlist_repository.to_dict(updated_setting),
                "code": 200,
            }, 200
        else:
            data["status"] = WaitlistStatus.PENDING.value
            # Create new setting
            new_waitlist = waitlist_repository.create_waitlist(data)
            return {
                "message": "Waitlist created successfully",
                "data": waitlist_repository.to_dict(new_waitlist),
                "code": 201,
            }, 201


@waitlist_user_namespace.route("/waitlist/<string:waitlist_id>")
class WaitlistResource(Resource):
    @waitlist_user_namespace.doc(security="Bearer Auth")
    @waitlist_user_namespace.doc("get_waitlist")
    @waitlist_user_namespace.response(
        200, "Waitlist retrieved successfully", model=waitlist_request_model
    )
    @waitlist_user_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @waitlist_user_namespace.response(
        404, "Waitlist not found", model=error_response_model
    )
    @waitlist_user_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @is_admin
    @handle_errors
    def get(self, waitlist_id):
        """Fetch the waitlist for the current user"""

        waitlist = waitlist_repository.get_waitlist_by_id(waitlist_id)

        if not waitlist:
            return {
                "message": "Waitlist not exit",
                "data": None,
                "code": 200,
            }, 200

        return {
            "message": "Waitlist successfully fetched",
            "data": waitlist_repository.to_dict(waitlist),
            "code": 200,
        }, 200

    @waitlist_user_namespace.doc(security="Bearer Auth")
    @waitlist_user_namespace.doc("update_waitlist")
    @waitlist_user_namespace.expect(update_waitlist_request_model)
    @waitlist_user_namespace.response(
        200, "Waitlist updated successfully", model=waitlist_request_model
    )
    @waitlist_user_namespace.response(400, "Bad request", model=error_response_model)
    @waitlist_user_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @waitlist_user_namespace.response(
        404, "Waitlist not found", model=error_response_model
    )
    @waitlist_user_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @is_admin
    @handle_errors
    def put(self, waitlist_id):
        """Update the waitlist for the current user"""
        data = request.json

        updated_waitlist = waitlist_repository.update_waitlist(waitlist_id, data)
        if not updated_waitlist:
            raise NotFound("Waitlist not found")

        return {
            "message": "Waitlist updated successfully",
            "data": waitlist_repository.to_dict(updated_waitlist),
            "code": 200,
        }, 200

    @waitlist_user_namespace.doc(security="Bearer Auth")
    @waitlist_user_namespace.doc("delete_waitlist")
    @waitlist_user_namespace.response(204, "Waitlist deleted")
    @waitlist_user_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @waitlist_user_namespace.response(
        404, "Waitlist not found", model=error_response_model
    )
    @waitlist_user_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @is_admin
    @handle_errors
    def delete(self, waitlist_id):
        """Delete the waitlist for the current user"""

        if not waitlist_repository.delete_waitlist(waitlist_id):
            raise NotFound("Waitlist not found")

        return {
            "message": "Waitlist delete successfully",
            "code": 204,
        }, 204


@waitlist_user_namespace.route("/waitlist")
class UserPopulatedWaitlistResource(Resource):
    @waitlist_user_namespace.doc(security="Bearer Auth")
    @waitlist_user_namespace.doc("get_populated_waitlist")
    @waitlist_user_namespace.expect(waitlist_list_input_model)
    @waitlist_user_namespace.response(
        200, "Waitlist retrieved successfully", model=waitlist_request_model
    )
    @waitlist_user_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @waitlist_user_namespace.response(
        404, "Waitlist not found", model=error_response_model
    )
    @waitlist_user_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @is_admin
    @handle_errors
    def get(self):
        """Fetch the waitlist for the current user"""
        args = waitlist_list_input_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        status = args["status"]

        result = waitlist_repository.get_waitlists_paginated(page, per_page, status)

        if not result:
            return {
                "message": "Waitlist not exit",
                "data": None,
                "code": 200,
            }, 200

        return {
            "message": "Avatar successfully fetched",
            "data": result["waitlists"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200
