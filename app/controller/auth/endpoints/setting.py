from flask import request, g
from flask_restx import Resource
from werkzeug.exceptions import NotFound
from app.exceptions import handle_errors
from app.repositories.user_setting_repository import UserSettingRepository
from app.repositories.avatar_repository import AvatarRepository
from app.repositories.voice_repository import VoiceRepository
from app.repositories.template_repository import TemplateRepository
from ..namespaces.setting import user_setting_namespace
from ..schemas.user import (
    user_setting_model,
    error_response_model,
    update_user_setting_model,
)
from .. import login_required

from bson import ObjectId

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

user_setting_repository = UserSettingRepository()
avatar_repository = AvatarRepository()
voice_repository = VoiceRepository()
template_repository = TemplateRepository()


@user_setting_namespace.route("/user-settings")
class UserSettingResource(Resource):

    @user_setting_namespace.doc(security="Bearer Auth")
    @user_setting_namespace.doc("create_or_update_user_setting")
    @user_setting_namespace.expect(user_setting_model)
    @user_setting_namespace.response(
        201, "User setting created successfully", model=user_setting_model
    )
    @user_setting_namespace.response(
        200, "User setting updated successfully", model=user_setting_model
    )
    @user_setting_namespace.response(400, "Bad request", model=error_response_model)
    @user_setting_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @user_setting_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def post(self):
        """Create or update user setting"""
        user_id = getattr(g, "user_id")
        data = request.json
        data["user_id"] = ObjectId(user_id)

        if not data.get("default_avatar"):
            avatar = avatar_repository.get_default_avatar()
            if avatar:
                data["default_avatar"] = str(avatar["_id"])

        if not data.get("default_voice"):
            voice = voice_repository.get_default_voice()
            if voice:
                data["default_voice"] = str(voice["_id"])

        if not data.get("default_template"):
            template = template_repository.get_default_template()
            if template:
                data["default_template"] = str(template["_id"])

        if not data.get("auto_thumbnail"):
            data["auto_thumbnail"] = False

        if not data.get("auto_youtube_metadata"):
            data["auto_youtube_metadata"] = False

        if not data.get("push_notifications"):
            data["push_notifications"] = True

        if not data.get("email_notifications"):
            data["email_notifications"] = True

        for field in ["default_avatar", "default_voice", "default_template"]:
            if data.get(field):
                data[field] = ObjectId(data.get(field))

        existing_setting = user_setting_repository.get_user_settings_by_user(user_id)

        if existing_setting:
            # Update existing setting
            updated_setting = user_setting_repository.update_user_setting_by_user(
                user_id, data
            )
            return {
                "message": "Setting updated successfully",
                "data": user_setting_repository.to_dict(updated_setting),
                "code": 200,
            }, 200
        else:
            # Create new setting
            new_user_setting = user_setting_repository.create_user_setting(data)
            return {
                "message": "Setting created successfully",
                "data": user_setting_repository.to_dict(new_user_setting),
                "code": 201,
            }, 201

    @user_setting_namespace.doc(security="Bearer Auth")
    @user_setting_namespace.doc("get_user_setting")
    @user_setting_namespace.response(
        200, "User setting retrieved successfully", model=user_setting_model
    )
    @user_setting_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @user_setting_namespace.response(
        404, "User setting not found", model=error_response_model
    )
    @user_setting_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self):
        """Fetch the user setting for the current user"""
        user_id = getattr(g, "user_id")
        user_setting = user_setting_repository.get_user_settings_by_user(user_id)

        if not user_setting:
            return {
                "message": "Setting not exit",
                "data": None,
                "code": 200,
            }, 200

        return {
            "message": "Setting successfully fetched",
            "data": user_setting_repository.to_dict(user_setting),
            "code": 200,
        }, 200

    @user_setting_namespace.doc(security="Bearer Auth")
    @user_setting_namespace.doc("update_user_setting")
    @user_setting_namespace.expect(update_user_setting_model)
    @user_setting_namespace.response(
        200, "User setting updated successfully", model=user_setting_model
    )
    @user_setting_namespace.response(400, "Bad request", model=error_response_model)
    @user_setting_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @user_setting_namespace.response(
        404, "User setting not found", model=error_response_model
    )
    @user_setting_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def put(self):
        """Update the user setting for the current user"""
        user_id = getattr(g, "user_id")
        data = request.json

        updated_user_setting = user_setting_repository.update_user_setting_by_user(
            user_id, data
        )
        if not updated_user_setting:
            raise NotFound("User setting not found")

        return {
            "message": "Setting updated successfully",
            "data": user_setting_repository.to_dict(updated_user_setting),
            "code": 200,
        }, 200

    @user_setting_namespace.doc(security="Bearer Auth")
    @user_setting_namespace.doc("delete_user_setting")
    @user_setting_namespace.response(204, "User setting deleted")
    @user_setting_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @user_setting_namespace.response(
        404, "User setting not found", model=error_response_model
    )
    @user_setting_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def delete(self):
        """Delete the user setting for the current user"""
        user_id = getattr(g, "user_id")
        if not user_setting_repository.delete_user_setting_by_user(user_id):
            raise NotFound("User setting not found")
        return {
            "message": "Setting delete successfully",
            "code": 204,
        }, 204


@user_setting_namespace.route("/user-settings-details")
class UserPopulatedSettingResource(Resource):
    @user_setting_namespace.doc(security="Bearer Auth")
    @user_setting_namespace.doc("get_populated_user_setting")
    @user_setting_namespace.response(
        200, "User setting retrieved successfully", model=user_setting_model
    )
    @user_setting_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @user_setting_namespace.response(
        404, "User setting not found", model=error_response_model
    )
    @user_setting_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self):
        """Fetch the user setting for the current user"""
        user_id = getattr(g, "user_id")
        user_setting = user_setting_repository.get_populated_user_settings(user_id)

        if not user_setting:
            return {
                "message": "Setting not exit",
                "data": None,
                "code": 200,
            }, 200

        return {
            "message": "Setting successfully fetched",
            "data": user_setting,
            "code": 200,
        }, 200
