import os
import secrets
from flask import g, request, url_for, session, redirect
from flask_restx import Resource
from werkzeug.exceptions import InternalServerError
from flask_dance.contrib.google import google
from google_auth_oauthlib.flow import Flow
from google.oauth2.credentials import Credentials  # Add this import
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from datetime import datetime, timedelta
import logging
from .. import login_required
from ..namespaces.google import google_namespace

from app.constant.google import Google

from app.service.google import google_provider


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

YOUTUBE_SCOPES = [
    "https://www.googleapis.com/auth/youtube.upload",
    "https://www.googleapis.com/auth/youtube.readonly",
]

# TODO: Remove in production
os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "1"
os.environ["OAUTHLIB_RELAX_TOKEN_SCOPE"] = "1"


def handle_error(error, status_code=500):
    logger.error(f"Error: {str(error)}")
    return {"message": str(error)}, status_code


@google_namespace.errorhandler(InternalServerError)
def handle_internal_server_error(error):
    return handle_error(error, 500)


@google_namespace.errorhandler(Exception)
def handle_unexpected_error(error):
    return handle_error(error, 500)


@google_namespace.route("/google-login")
@google_namespace.route("/google/callback")
class GoogleCallback(Resource):
    def get(self):
        try:
            logger.info("Google callback initiated")
            if not google.authorized:
                logger.warning("Google not authorized, redirecting to login")
                return redirect(url_for(Google.GOOGLE_LOGIN.value))

            return google_provider.login(request)

        except Exception as e:
            logger.error(f"Error in Google callback: {str(e)}", exc_info=True)
            raise InternalServerError("An error occurred during Google authentication")


# Add this route for testing
@google_namespace.route("/protected")
class ProtectedRoute(Resource):
    def get(self):
        if "user_id" not in session:
            return redirect(url_for("google_login.get"))
        return f"Hello, {session['email']}! This is a protected route."


@google_namespace.route("/youtube/auth")
class YouTubeAuth(Resource):
    @google_namespace.doc(security="Bearer Auth")
    @login_required
    def get(self):

        user_id = getattr(g, "user_id")

        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": os.getenv("GOOGLE_CLIENT_ID"),
                    "client_secret": os.getenv("GOOGLE_CLIENT_SECRET"),
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                }
            },
            scopes=YOUTUBE_SCOPES,
            redirect_uri=url_for(
                "auth.Google-Login_you_tube_auth_callback", _external=True
            ),
        )

        # Create a state parameter that includes both a random state and the user_id
        random_state = secrets.token_urlsafe(16)  # Generate random state
        combined_state = f"{random_state}_{user_id}"  # Combine with user_id

        authorization_url, state = flow.authorization_url(
            access_type="offline",
            include_granted_scopes="true",
            prompt="consent",  # Add this line to force the consent screen
            state=user_id,
        )

        session["youtube_auth_state"] = combined_state

        return {"redirect_link": authorization_url}


@google_namespace.route("/youtube/auth/callback")
class YouTubeAuthCallback(Resource):
    def get(self):
        # if "youtube_auth_state" not in session:
        #     return {"message": "Invalid state parameter"}, 400

        # Get the combined state from the callback
        user_id = request.args.get("state", "")

        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": os.getenv("GOOGLE_CLIENT_ID"),
                    "client_secret": os.getenv("GOOGLE_CLIENT_SECRET"),
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                }
            },
            scopes=YOUTUBE_SCOPES,
            state=user_id,
            redirect_uri=url_for(
                "auth.Google-Login_you_tube_auth_callback", _external=True
            ),
        )

        flow.fetch_token(authorization_response=request.url)

        credentials = flow.credentials

        # Calculate the refresh token expiration time
        # Assuming the refresh token doesn't expire, set it to 30 days from now
        refresh_token_expires_at = datetime.now() + timedelta(days=7)

        session["youtube_credentials"] = {
            "token": credentials.token,
            "refresh_token": credentials.refresh_token,
            "token_uri": credentials.token_uri,
            "scopes": credentials.scopes,
            "expires_at": credentials.expiry.isoformat(),
            "refresh_token_expires_at": refresh_token_expires_at.isoformat(),
        }

        # Clear the auth-specific session variables
        session.pop("youtube_auth_state", None)

        return google_provider.update_youtube_credentials_using_id(
            user_id, session["youtube_credentials"]
        )


@google_namespace.route("/youtube/credentials")
class YouTubeCredentials(Resource):
    def get(self):
        if "youtube_credentials" not in session:
            return {
                "message": "No YouTube credentials found. Please authenticate first."
            }, 401

        return session["youtube_credentials"]


@google_namespace.route("/youtube/refresh_token")
class YouTubeRefreshToken(Resource):
    def post(self):
        if "youtube_credentials" not in session:
            return {
                "message": "No YouTube credentials found. Please authenticate first."
            }, 401

        # Calculate the refresh token expiration time
        # Assuming the refresh token doesn't expire, set it to 30 days from now
        refresh_token_expires_at = datetime.now() + timedelta(days=7)
        youtube_credentials = session["youtube_credentials"]
        credentials = Credentials(
            token=youtube_credentials.get("token"),
            refresh_token=youtube_credentials.get("refresh_token"),
            token_uri=youtube_credentials.get("token_uri"),
            client_id=youtube_credentials.get("client_id"),
            client_secret=youtube_credentials.get("client_secret"),
            scopes=youtube_credentials.get("scopes"),
        )

        old_credentials = session["youtube_credentials"]

        if not credentials.valid:
            if credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
                session["youtube_credentials"] = {
                    "token": credentials.token,
                    "refresh_token": credentials.refresh_token,
                    "token_uri": credentials.token_uri,
                    "scopes": credentials.scopes,
                    "expires_at": credentials.expiry.isoformat(),
                    "refresh_token_expires_at": old_credentials.get(
                        "refresh_token_expires_at", refresh_token_expires_at.isoformat()
                    ),
                }
                return google_provider.update_youtube_credentials(
                    request, session["youtube_credentials"]
                )

                # return {"message": "Token refreshed successfully"}, 200
            else:
                return {
                    "message": "Unable to refresh token. Please reauthenticate."
                }, 401

        return {"message": "Token is still valid"}, 200


@google_namespace.route("/youtube/channels")
class YouTubeChannels(Resource):
    def get(self):
        if "youtube_credentials" not in session:
            return {
                "message": "Not authorized for YouTube. Please authenticate for YouTube first."
            }, 401

        youtube_credentials = session["youtube_credentials"]
        credentials = Credentials(
            token=youtube_credentials.get("token"),
            refresh_token=youtube_credentials.get("refresh_token"),
            token_uri=youtube_credentials.get("token_uri"),
            client_id=youtube_credentials.get("client_id"),
            client_secret=youtube_credentials.get("client_secret"),
            scopes=youtube_credentials.get("scopes"),
        )
        refresh_token_expires_at = datetime.now() + timedelta(days=7)

        old_credentials = session["youtube_credentials"]

        if not credentials.valid:
            if credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
                session["youtube_credentials"] = {
                    "token": credentials.token,
                    "refresh_token": credentials.refresh_token,
                    "token_uri": credentials.token_uri,
                    "scopes": credentials.scopes,
                    "expires_at": credentials.expiry.isoformat(),
                    "refresh_token_expires_at": old_credentials.get(
                        "refresh_token_expires_at", refresh_token_expires_at.isoformat()
                    ),
                }
                google_provider.update_youtube_credentials(
                    request, session["youtube_credentials"]
                )
            else:
                return {"message": "Token expired. Please reauthenticate."}, 401

        youtube = build("youtube", "v3", credentials=credentials)
        try:
            channels = (
                youtube.channels()
                .list(part="snippet,contentDetails,statistics", mine=True)
                .execute()
            )
            return {"channels": channels["items"]}
        except Exception as e:
            return {"message": str(e)}, 500
