from flask import g, request, session, redirect, make_response, jsonify
from botocore.exceptions import ClientError
import sys
import logging
from flask_restx import Resource
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime, timedelta
from app.cache import get_cache_provider
from app.constant.user_enums import LoginEnums, EmailType, AccountStatus
from app.exceptions.auth_exceptions import (
    InvalidPasswordError,
    PasswordNotComplexError,
    EmailAlreadyExistsError,
    NotWaitlistedExistsError,
)
from app.repositories.waitlist_repository import WaitlistRepository
from app.exceptions.common import (
    InvalidAuthError,
    InvalidAuthTokenFormatError,
    InvalidEmailOrPasswordError,
    InvalidValueError,
    InvalidEmailError,
    InactiveAccountError,
    AuthenticationError,
    TokenExpiredError,
)
from app.repositories.user_repository import AuthRepository
from app.helper.mailer import EmailService
from app.helper.jwt import JwtService
from app.helper.redis import RedisService
from app.service.subscription.stripe import StripeService

from .. import login_required
from ..namespaces.user import user_namespace
from ..schemas.user import (
    auth_token_response_model,
    change_password_request_model,
    error_response_model,
    forget_password_request_model,
    forget_password_response_model,
    logout_response_model,
    password_change_response_model,
    presigned_url_response_model,
    reset_password_request_model,
    reset_password_response_model,
    update_profile_request_model,
    user_login_request_model,
    user_profile_response_model,
    user_signup_request_model,
    user_creation_response_model,
    presigned_url_model,
    post_presigned_model,
    refresh_token_request_model,
    deactivate_account_response_model,
)
from ..utils import (
    decode_auth_token,
    encode_auth_token,
    is_password_complex,
    serialize_user,
    validate_email,
    generate_numeric_otp,
    encode_refresh_token,
    decode_refresh_token,
)
from app.helper.s3_manager import S3Uploader
from app.constant.user_enums import WaitlistStatus

from config import (
    DOMAIN,
    USER_JWT_COOKIE,
    API_GATEWAY_URL,
    FRONTEND_ENDPOINT,
    WAITLIST_ENABLED,
)

from app.service.users import UserService

cache_provider = get_cache_provider()
waitlist_repository = WaitlistRepository()
auth_repository = AuthRepository()
email_service = EmailService()
user_service = UserService()

s3_helper = S3Uploader()
stripe_service = StripeService()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,  # directing logs to the standard output
)
logger = logging.getLogger(__name__)


@user_namespace.route("/me")
class UserProfile(Resource):
    """
    Retrieves the currently authenticated user's profile information based on the authentication token provided in the request headers. This endpoint is accessible only to users who are logged in and have a valid authentication token.

    **Authorization:** A valid Bearer token must be included in the Authorization header.

    **Responses:**
    - 200: Successfully retrieves the user's profile information.
    - 401: Unauthorized access due to missing or invalid authentication token.
    - 404: User profile not found, indicating the user ID from the token does not match any existing user.
    - 500: Internal server error occurred while processing the request.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.response(
        200,
        "Current user profile fetched successfully.",
        model=user_profile_response_model,
    )
    @user_namespace.response(
        401,
        "Unauthorized access. A valid authentication token is required.",
        model=error_response_model,
    )
    @user_namespace.response(
        404,
        "User profile not found. The user ID from the token does not match any existing user.",
        model=error_response_model,
    )
    @user_namespace.response(
        500,
        "An internal server error occurred while processing the request. Please try again later.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """
        Fetches and returns the profile of the currently authenticated user.

        The response includes essential user information such as email ID, username, status, and role. Sensitive information like passwords is not included in the response.

        **Authorization:**

        - This endpoint requires a valid Bearer token to be included in the Authorization header.
        """

        user_id = getattr(g, "user_id")

        user = auth_repository.get_user_details(user_id)

        return {
            "message": "Current user profile fetched successfully.",
            "data": user,
            "code": 200,
        }, 200

    @user_namespace.expect(update_profile_request_model, validate=True)
    @user_namespace.response(
        200,
        "Profile updated successfully.",
        model=user_profile_response_model,
    )
    @user_namespace.response(
        404, "User not found or invalid id provided.", model=error_response_model
    )
    @user_namespace.response(
        500, "Internal server error occurred.", model=error_response_model
    )
    @login_required
    def put(self):
        """
        Update the user's profile using the provided new data for user name etc.

        **Input Fields:**
        - `name` (String, Required:True): The name of user password.
        """
        user_id = getattr(g, "user_id")

        data = request.json

        payload = {}

        if "name" in data:
            payload["name"] = data["name"]

        if "profile_picture" in data:
            payload["profile_picture"] = data["profile_picture"]

        if "firebase_token" in data:
            payload["firebase_token"] = data["firebase_token"]

        auth_repository.update_user(user_id, **payload)

        user_data = auth_repository.find_user_by_id(user_id)

        return {
            "message": "User profile updated successfully",
            "data": serialize_user(user_data),
            "code": 200,
        }, 200


@user_namespace.route("/signup")
class UserSignup(Resource):
    """
    User Registration Endpoint.

    Enables the creation of a new user account with details including email, password, user name.
    The password must meet complexity requirements.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.expect(user_signup_request_model, validate=True)
    @user_namespace.response(
        201, "User account created successfully.", user_creation_response_model
    )
    @user_namespace.response(409, "Email address already in use.", error_response_model)
    @user_namespace.response(
        500,
        "Server error encountered during user account creation.",
        error_response_model,
    )
    def post(self):
        """
        Registers a new user.

        * Email must be unique across all user accounts.
        * Password must be complex (contain uppercase, lowercase, digits, and special characters).

        **Input Fields:**

        - `email` (string, **required**): The user's chosen email. Must be unique.
        - `name` (string): The name of the user.
        """

        # user_id = getattr(g, "user_id")

        data = request.json

        email = data["email"]
        password = data["password"]
        name = data["name"]

        send_mail = True

        if WAITLIST_ENABLED == "true":

            existing_setting = waitlist_repository.get_waitlist_by_email(email)

            if (
                not existing_setting
                or existing_setting.get("status") != WaitlistStatus.APPROVED.value
            ):
                raise NotWaitlistedExistsError()

        if not validate_email(email):
            raise InvalidEmailError()

        # Check password complexity
        if not is_password_complex(password):
            raise PasswordNotComplexError()

        # Check if email already exists
        user = auth_repository.find_user_by_email(email)

        password_hash = generate_password_hash(password)  # Hash the plaintext password

        if user:

            user = auth_repository.to_dict(user)

            if user["status"] == str(AccountStatus.ACTIVE.value):
                raise EmailAlreadyExistsError()

            data_to_update = {
                "email": email,
                "password": password_hash,
                "name": name,
                "status": AccountStatus.INACTIVE,
                "role": "user",
                "verified_email": False,
                "login_method": "auth",
                "account_status": AccountStatus.ACTIVE,
            }

            current_time = datetime.now()
            email_sent_time = user.get("email_sent_time", None)
            if email_sent_time:
                # Convert string to datetime object
                email_sent_time = datetime.fromisoformat(email_sent_time)
                comparison_time = email_sent_time + timedelta(minutes=5)
                if current_time > comparison_time:
                    data_to_update["email_sent_time"] = current_time
                else:
                    send_mail = False
            else:
                data_to_update["email_sent_time"] = current_time

            if not user.get("customer_id"):

                customer_id = stripe_service.create_customer(
                    {"email": email, "name": name}
                )

                data_to_update["customer_id"] = customer_id
            # Create new user
            auth_repository.update_or_create_user(data_to_update)

            id = str(user["_id"])
        else:

            customer_id = stripe_service.create_customer({"email": email, "name": name})

            # Create new user
            id = auth_repository.create_user(
                {
                    "email": email,
                    "password": password_hash,
                    "name": name,
                    "status": AccountStatus.INACTIVE,
                    "role": "user",
                    "verified_email": False,
                    "login_method": "auth",
                    "customer_id": customer_id,
                    "account_status": AccountStatus.ACTIVE,
                    "email_sent_time": datetime.now(),
                }
            )

        otp = generate_numeric_otp()

        # Create a JWT token with otp and email
        encrypted_otp = JwtService().sign(
            {"otp": str(otp), "email": email}, {"expiresIn": 3600}
        )

        # Update or save encryptedOtp in Redis
        RedisService().set_data_in_redis_with_ttl(
            f"signup_verification:{email}", encrypted_otp, 300
        )

        # Add server URL prefix in front of encryptedOtp
        link = f"{API_GATEWAY_URL}/api/auth/signup_verification/{encrypted_otp}"

        if send_mail:
            email_service.send_email_with_template(
                name, email, link, EmailType.SIGNUP.value
            )

        user = auth_repository.find_user_without_password(identifier=id, search_by="id")

        user["_id"] = str(user["_id"])
        user["created_at"] = str(user["created_at"])
        user["updated_at"] = str(user["updated_at"])
        user["email_sent_time"] = str(user["email_sent_time"])

        return {
            "message": "User created successfully",
            "data": user,
            "code": 201,
        }, 201


@user_namespace.route("/signup_verification/<string:encrypted_otp>")
class SignupVerification(Resource):
    """
    User Registration Verification Endpoint.

    Verifies the user's email address using the encrypted OTP sent during registration.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.response(200, "Email verified successfully.")
    @user_namespace.response(
        400, "Invalid or expired verification token.", error_response_model
    )
    @user_namespace.response(
        500, "Server error encountered during verification.", error_response_model
    )
    def get(self, encrypted_otp):
        try:
            user_service.update_email_verified_details({"token": encrypted_otp})
            return redirect(
                f"{FRONTEND_ENDPOINT}/auth/verify-account?success=true&type=email"
            )
        except Exception as error:
            print(f"Error during email verification: {str(error)}")
            return redirect(
                f"{FRONTEND_ENDPOINT}/auth/verify-account?success=false&type=email"
            )


@user_namespace.route("/login")
class Login(Resource):
    """
    Authenticates users and provides them with a token for accessing protected endpoints.
    The endpoint validates the provided email and password, checks if the user account is active,
    and upon successful authentication, returns an authentication token.
    """

    @user_namespace.expect(user_login_request_model, validate=True)
    @user_namespace.response(
        200,
        "User successfully authenticated. Token provided.",
        model=auth_token_response_model,
    )
    @user_namespace.response(
        400,
        "Invalid credentials or inactive account. Authentication failed.",
        model=error_response_model,
    )
    @user_namespace.response(
        500,
        "An unexpected error occurred during authentication. Please try again later.",
        model=error_response_model,
    )
    def post(self):
        """
        Authenticates a user and issues an authentication token for subsequent API requests.

        **Input Fields:**

        - `email` (string, **required**): The email ID of the user attempting to log in.
        - `password` (string, **required**): The password of the user.
        """

        data = request.json
        email = data["email"]
        password = data["password"]
        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        try:

            if WAITLIST_ENABLED == "true":

                existing_setting = waitlist_repository.get_waitlist_by_email(email)

                if (
                    not existing_setting
                    or existing_setting.get("status") != WaitlistStatus.APPROVED.value
                ):
                    raise NotWaitlistedExistsError()

            user = auth_repository.find_user_by_email(email)

            if not user:
                raise InvalidEmailOrPasswordError()

            # Check if user status is active
            if user.get("status") != AccountStatus.ACTIVE.value:
                raise InactiveAccountError()

            if not check_password_hash(user["password"], password):
                raise InvalidEmailOrPasswordError()

            # Check if user status is active
            if user.get("account_status") != AccountStatus.ACTIVE.value:
                raise InactiveAccountError("Account is not active.")

            user_id = str(user["_id"])

            role = user["role"]

            auth_token = encode_auth_token(
                user_id=user_id,
                user_agent=user_agent,
                ip_address=ip_address,
                role=role,
            )

            # Generate refresh token with one month validity
            refresh_token = encode_refresh_token(
                user_id=user_id, exp=datetime.utcnow() + timedelta(days=30)
            )

            if auth_token:
                cache_provider.set(
                    f"sess_{user_id}", auth_token, LoginEnums.LOGIN_EXPIRE_TIME.value
                )

                cache_provider.set(
                    f"refresh_{user_id}",
                    refresh_token,
                    LoginEnums.REFRESH_EXPIRE_TIME.value,
                )

                return {
                    "message": "Logged in successfully",
                    "data": {
                        "auth_token": auth_token,
                        "refresh_token": refresh_token,
                        "expires_at": int(datetime.now().timestamp())
                        + int(timedelta(days=1).total_seconds()),
                    },
                    "code": 200,
                }, 200

            raise AuthenticationError()

        except InvalidEmailOrPasswordError:
            user_namespace.abort(
                400, "Invalid email or password. Authentication failed."
            )
        except InactiveAccountError:
            user_namespace.abort(
                400, "Account is not active. Please verify your email."
            )
        except AuthenticationError:
            user_namespace.abort(500, "Failed to generate authentication token.")
        except Exception as e:
            print("login", str(e))
            user_namespace.abort(500, f"An unexpected error occurred: {str(e)}")


@user_namespace.route("/logout")
class Logout(Resource):
    """
    Invalidates the current user's authentication token and removes all associated cookies.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.response(
        200, "User successfully logged out.", model=logout_response_model
    )
    @user_namespace.response(
        400, "Invalid or missing authentication token.", model=error_response_model
    )
    @user_namespace.response(
        500, "Internal server error occurred.", model=error_response_model
    )
    def post(self):
        """
        Logs out the current user by invalidating their authentication token and clearing cookies.
        """
        try:
            # Get and validate auth token
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                raise InvalidAuthError(description="Authentication token is missing.")

            parts = auth_header.split(" ")
            if len(parts) != 2 or parts[0] != "Bearer":
                raise InvalidAuthTokenFormatError()

            auth_token = parts[1]
            user_id, _, _, _ = decode_auth_token(auth_token)

            # Clear cached sessions
            cache_provider.delete(f"sess_{user_id}")
            cache_provider.delete(f"refresh_{user_id}")

            # Clear server-side session
            session.clear()

            # Create response
            response = make_response(
                jsonify({"message": "Successfully logged out", "data": {}, "code": 200})
            )

            # List of cookies to remove
            cookies_to_remove = [USER_JWT_COOKIE, "rt", "exp"]

            # Remove cookies with matching settings from login
            for cookie_name in cookies_to_remove:
                response.set_cookie(
                    cookie_name,
                    "",  # Empty value
                    expires=0,  # Expire immediately
                    max_age=0,  # No max age
                    domain=DOMAIN,
                    path="/",
                    secure=True,
                    httponly=True,
                    samesite="None",
                )
                logger.info(f"Cookie removed: {cookie_name}")

            return response

        except (InvalidAuthError, InvalidAuthTokenFormatError) as e:
            logger.error(f"Authentication error during logout: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"Unexpected error during logout: {str(e)}", exc_info=True)
            raise InvalidValueError(f"Logout failed: {str(e)}")


@user_namespace.route("/change-password")
class ChangePassword(Resource):
    """
    Allows authenticated users to change their password. The endpoint requires the current password for verification and a new password that meets complexity requirements.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.expect(change_password_request_model, validate=True)
    @user_namespace.response(
        200, "Password changed successfully.", model=password_change_response_model
    )
    @user_namespace.response(
        400,
        "Invalid request. Password does not meet complexity requirements or current password incorrect.",
        model=error_response_model,
    )
    @user_namespace.response(
        401,
        "Invalid authentication. The current password entered is incorrect.",
        model=error_response_model,
    )
    @user_namespace.response(
        500,
        "Internal server error occurred while changing the password. Please try again later.",
        model=error_response_model,
    )
    @login_required
    def post(self):
        """
        Changes the password for the authenticated user.

        **Input Fields:**

        - `current_password` (string, **required**): The current password of the user.
        - `new_password` (string, **required**): The new password for the user. Must be different from the current password and meet complexity requirements.

        **Requirements:**

        - The user must be authenticated and provide the current password for verification.
        - The new password must meet defined complexity requirements (e.g., include uppercase and lowercase letters, numbers, and symbols).
        """

        user_id = getattr(g, "user_id")

        data = request.json
        current_password = data["current_password"]
        new_password = data["new_password"]

        # Retrieve user from the database
        user = auth_repository.find_user_by_id(user_id)

        # Verify current password is correct
        if not check_password_hash(user["password"], current_password):
            raise InvalidPasswordError(
                description="The current password entered is incorrect."
            )

        # Ensure new password is different from the current password
        if current_password == new_password:
            raise PasswordNotComplexError(
                description="New password cannot be the same as the current password."
            )

        # Check new password complexity
        if not is_password_complex(new_password):
            raise PasswordNotComplexError()

        # Update user's password in the database
        auth_repository.update_user_password(user_id, new_password)

        return {"message": "Password has been changed successfully"}, 200


@user_namespace.route("/forget-password")
class ForgetPassword(Resource):
    """
    Initiates the password reset process by sending a reset link or token via email.
    """

    @user_namespace.expect(forget_password_request_model, validate=True)
    @user_namespace.response(
        200, "Password reset email sent.", model=forget_password_response_model
    )
    @user_namespace.response(404, "User not found.", model=error_response_model)
    @user_namespace.response(
        500, "Internal server error occurred.", model=error_response_model
    )
    def post(self):
        """
        Sends a password reset email to the user with the provided email.

        **Input Fields:**
        - `email` (string): The user's email id.
        """

        data = request.json
        email = data["email"]

        if not validate_email(email):
            raise InvalidEmailError()

        user = auth_repository.find_user_by_email(email)

        if not user:
            raise AuthenticationError("User not found.")

        otp = generate_numeric_otp()

        # Create a JWT token with otp and email
        encrypted_otp = JwtService().sign(
            {"otp": str(otp), "email": email}, {"expiresIn": 3600}
        )

        # Update or save encryptedOtp in Redis
        RedisService().set_data_in_redis_with_ttl(
            f"reset_password_verification:{email}", encrypted_otp, 3600
        )

        # Add server URL prefix in front of encryptedOtp
        link = f"{FRONTEND_ENDPOINT}/auth/reset-password?token={encrypted_otp}"

        # Send the token to the user's email
        email_service.send_email_with_template(
            user["name"], user["email"], link, EmailType.FORGOT_PASSWORD.value
        )

        return {
            "message": "Password reset email sent.",
            "data": {},
            "code": 200,
        }, 200


@user_namespace.route("/reset-password")
class ResetPassword(Resource):
    """
    Allows a user to reset their password using a valid reset token received via email.
    """

    @user_namespace.expect(reset_password_request_model, validate=True)
    @user_namespace.response(
        200,
        "Password has been reset successfully.",
        model=reset_password_response_model,
    )
    @user_namespace.response(
        400,
        "Password does not meet complexity requirements.",
        model=error_response_model,
    )
    @user_namespace.response(
        404, "User not found or token invalid.", model=error_response_model
    )
    @user_namespace.response(
        422, "Invalid or expired auth token.", model=error_response_model
    )
    @user_namespace.response(
        500, "Internal server error occurred.", model=error_response_model
    )
    def post(self):
        """
        Resets the user's password using the provided reset token and new password.

        **Input Fields:**
        - `new_password` (string): The new password.
        - `token` (string): The reset token.
        """

        data = request.json
        new_password = data["new_password"]
        token = data["token"]

        if not token:
            raise InvalidValueError(description="The reset token is missing.")

        if not is_password_complex(new_password):
            raise PasswordNotComplexError()

        password_hash = generate_password_hash(
            new_password
        )  # Hash the new plaintext password

        return user_service.verify_reset_token_and_update_password(token, password_hash)


@user_namespace.route("/post-presigned")
class PostPresignedResource(Resource):

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.expect(post_presigned_model)
    @user_namespace.doc("generate_post_presigned")
    @login_required
    def post(self):
        data = user_namespace.payload
        try:
            result = s3_helper.generate_post_presigned(
                folder=data["folder"],
                file_name=data["file_name"],
            )
            return result, 200
        except ClientError as e:
            return {"message": str(e)}, 400


@user_namespace.route("/presigned-url")
class PresignedURLResource(Resource):

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.expect(presigned_url_model)
    @user_namespace.doc("generate_presigned_url")
    @user_namespace.response(200, "Success", presigned_url_response_model)
    @user_namespace.response(400, "Bad Request")
    @login_required
    def post(self):
        data = user_namespace.payload
        try:
            url = s3_helper.generate_presigned_url(
                folder=data["folder"],
                file_name=data["file_name"],
                content_type=data["content_type"],
            )
            return {"presigned_url": url, "code": 200}, 200
        except ClientError as e:
            return {"message": str(e), "code": 400}, 400

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.expect(presigned_url_model)
    @user_namespace.doc("generate_presigned_url")
    @user_namespace.response(200, "Success", presigned_url_response_model)
    @user_namespace.response(400, "Bad Request")
    @user_namespace.response(404, "File Not Found")
    @user_namespace.response(500, "Internal Server Error")
    @login_required
    def get(self):
        data = user_namespace.payload
        try:
            # Validate input data
            if not data or "folder" not in data or "file_name" not in data:
                return {"message": "Missing required fields", "code": 400}, 400

            folder = data["folder"]
            file_name = data["file_name"]

            # Generate presigned URL
            url = s3_helper.generate_presigned_url_for_download(
                folder=folder,
                file_name=file_name,
            )

            return {"presigned_url": url, "code": 200}, 200

        except ClientError as e:
            user_namespace.logger.error(f"ClientError: {str(e)}")
            return {
                "message": "An error occurred while generating the presigned URL",
                "code": 400,
            }, 400
        except Exception as e:
            user_namespace.logger.error(f"Unexpected error: {str(e)}")
            return {"message": "An unexpected error occurred", "code": 500}, 500


@user_namespace.route("/refresh-token")
class RefreshToken(Resource):
    """
    Refreshes the JWT token for authenticated users.
    """

    @user_namespace.expect(refresh_token_request_model, validate=True)
    @user_namespace.response(
        200, "Token refreshed successfully.", model=auth_token_response_model
    )
    @user_namespace.response(
        401, "Invalid or expired token.", model=error_response_model
    )
    @user_namespace.response(
        500,
        "An unexpected error occurred. Please try again later.",
        model=error_response_model,
    )
    def post(self):
        """
        Refreshes the authentication token for the current user.

        The current token must be provided in the Authorization header.
        """

        data = request.json
        refresh_token = data.get("refresh_token")

        try:
            # Decode and validate the refresh token
            user_id = decode_refresh_token(refresh_token)

            # Check if the refresh token is in the cache
            cached_refresh_token = cache_provider.get(f"refresh_{user_id}")
            if not cached_refresh_token or cached_refresh_token != refresh_token:
                raise AuthenticationError("Invalid refresh token.")

            # Retrieve user from the database
            user = auth_repository.find_user_by_id(user_id)

            if not user:
                raise AuthenticationError("User not found.")

            role = user["role"]

            # Generate a new auth token
            ip_address = request.remote_addr
            user_agent = request.headers.get("User-Agent")
            new_auth_token = encode_auth_token(
                user_id=user_id,
                user_agent=user_agent,
                ip_address=ip_address,
                role=role,
            )

            if new_auth_token:
                # Update the cache with the new tokens
                cache_provider.set(
                    f"sess_{user_id}",
                    new_auth_token,
                    LoginEnums.LOGIN_EXPIRE_TIME.value,
                )

                return {
                    "message": "Tokens refreshed successfully",
                    "data": {
                        "auth_token": new_auth_token,
                        "expires_at": int(datetime.now().timestamp())
                        + int(timedelta(days=1).total_seconds()),
                    },
                    "code": 200,
                }, 200

            raise AuthenticationError("Failed to generate new tokens.")

        except TokenExpiredError:
            user_namespace.abort(401, "Token has expired. Please log in again.")
        except AuthenticationError as e:
            user_namespace.abort(401, str(e))
        except Exception as e:
            print("refresh_token", str(e))
            user_namespace.abort(500, f"An unexpected error occurred: {str(e)}")


@user_namespace.route("/deactivate")
class DeactivateAccount(Resource):
    """
    Deactivates the account of the currently authenticated user.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.response(
        200,
        "Account deactivated successfully.",
        model=deactivate_account_response_model,
    )
    @user_namespace.response(
        400, "Invalid password or account already inactive.", model=error_response_model
    )
    @user_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @user_namespace.response(
        500, "Internal server error occurred.", model=error_response_model
    )
    @login_required
    def post(self):
        """
        Deactivates the account of the currently authenticated user.

        This endpoint requires authentication and the user's current password for security.
        The user's account status will be changed to 'inactive', and all active sessions will be terminated.

        **Authorization:**
        - This endpoint requires a valid Bearer token to be included in the Authorization header.

        **Input Fields:**
        - `password` (string, required): The user's current password for verification.
        """
        try:
            user_id = getattr(g, "user_id")

            # Retrieve user details
            user = auth_repository.find_user_by_id(user_id)

            if not user:
                raise InvalidAuthError("User not found.")

            email = user["email"]

            # Check if the account is already inactive
            if user.get("account_status") == AccountStatus.INACTIVE.value:
                raise InactiveAccountError("Account is already inactive.")

            # Update user status to inactive
            auth_repository.update_user(
                user_id, account_status=AccountStatus.INACTIVE.value
            )

            # Invalidate all active sessions
            cache_provider.delete(f"sess_{user_id}")
            cache_provider.delete(f"refresh_{user_id}")

            session.clear()

            otp = generate_numeric_otp()

            # Create a JWT token with otp and email
            encrypted_otp = JwtService().sign(
                {"otp": str(otp), "email": email}, {"expiresIn": 2592000}
            )

            # Update or save encryptedOtp in Redis
            RedisService().set_data_in_redis_with_ttl(
                f"account_activation:{email}", encrypted_otp, 2592000
            )

            # Add server URL prefix in front of encryptedOtp
            link = f"{API_GATEWAY_URL}/api/auth/activate_account/{encrypted_otp}"

            # Send confirmation email
            email_service.send_email_with_template(
                user["name"],
                user["email"],
                link,
                EmailType.DEACTIVATE.value,
            )

            return {"message": "Account deactivated successfully.", "code": 200}, 200

        except InvalidAuthError as e:
            user_namespace.abort(401, str(e))
        except (InvalidPasswordError, InactiveAccountError) as e:
            user_namespace.abort(400, str(e))
        except Exception as e:
            user_namespace.abort(500, f"An unexpected error occurred: {str(e)}")


@user_namespace.route("/activate_account/<string:encrypted_otp>")
class AccountActivationVerification(Resource):
    """
    User Registration Verification Endpoint.

    Verifies the user's email address using the encrypted OTP sent during registration.
    """

    @user_namespace.doc(security="Bearer Auth")
    @user_namespace.response(200, "Email verified successfully.")
    @user_namespace.response(
        400, "Invalid or expired verification token.", error_response_model
    )
    @user_namespace.response(
        500, "Server error encountered during verification.", error_response_model
    )
    def get(self, encrypted_otp):
        try:
            user_service.account_activate_verified_details({"token": encrypted_otp})
            return redirect(
                f"{FRONTEND_ENDPOINT}/signup/verify-account?success=true&type=email"
            )
        except Exception as error:
            print(f"Error during email verification: {str(error)}")
            return redirect(
                f"{FRONTEND_ENDPOINT}/signup/verify-account?success=false&type=email"
            )
