from flask import request
from flask_restx import Resource, reqparse
from werkzeug.security import check_password_hash

from app.constant.user_enums import LoginEnums, UserRole
from app.exceptions.auth_exceptions import (
    EmailAlreadyExistsError,
    NoFieldsToUpdateError,
    PasswordNotComplexError,
    UserNotFoundError,
)
from app.exceptions.common import (
    InvalidEmailError,
    InvalidEmailOrPasswordError,
    InvalidAuthError,
)
from app.repositories.user_repository import AuthRepository

from .. import is_admin, cache_provider
from ..namespaces.admin import admin_namespace
from ..schemas.admin import (
    admin_creation_response_model,
    admin_error_response_model,
    admin_signup_request_model,
    user_list_query_model_dict,
    user_list_response_model,
    user_update_request_model,
    user_update_response_model,
    user_counts_response_model,
)
from ..schemas.user import (
    user_login_request_model,
    auth_token_response_model,
    error_response_model,
)
from ..utils import (
    is_password_complex,
    serialize_users,
    validate_email,
    encode_auth_token,
)

auth_repository = AuthRepository()


@admin_namespace.route("/admin-login")
class Login(Resource):
    """
    Authenticates users and provides them with a token for accessing protected endpoints. The endpoint validates the provided email and password, and upon successful authentication, returns an authentication token.
    """

    @admin_namespace.expect(user_login_request_model, validate=True)
    @admin_namespace.response(
        200,
        "User successfully authenticated. Token provided.",
        model=auth_token_response_model,
    )
    @admin_namespace.response(
        401, "Invalid credentials. Authentication failed.", model=error_response_model
    )
    @admin_namespace.response(
        500,
        "An unexpected error occurred during authentication. Please try again later.",
        model=error_response_model,
    )
    def post(self):
        """
        Authenticates the admin only and issues an authentication token for subsequent API requests.

        **Input Fields:**

        - `email` (string, **required**): The email ID of the user attempting to log in.
        - `password` (string, **required**): The password of the user.
        """

        data = request.json
        email = data["email"]
        password = data["password"]

        ip_address = request.remote_addr
        user_agent = request.headers.get("User-Agent")

        user = auth_repository.find_user_by_email(email)

        if user and check_password_hash(user["password"], password):
            user_id = str(user["_id"])
            role = user["role"]

            if role != UserRole.ADMIN:
                raise InvalidAuthError("User has no admin access!")

            auth_token = encode_auth_token(
                user_id=user_id,
                user_agent=user_agent,
                ip_address=ip_address,
                role=role,
            )

            if auth_token:
                cache_provider.set(
                    f"sess_{user_id}", auth_token, LoginEnums.LOGIN_EXPIRE_TIME.value
                )
                return {
                    "message": "Logged in successfully",
                    "data": {"auth_token": auth_token, "code": 200},
                }, 200

        raise InvalidEmailOrPasswordError()


@admin_namespace.route("/admin-signup")
class AdminSignup(Resource):
    """
    Endpoint for admin registration.

    Allows the creation of a new admin account with a specified email ID and password.
    The password must meet the defined complexity requirements.
    """

    @admin_namespace.doc(security="Bearer Auth")
    @admin_namespace.expect(admin_signup_request_model, validate=True)
    @admin_namespace.response(
        201, "Admin account created successfully.", admin_creation_response_model
    )
    @admin_namespace.response(
        400, "Invalid request due to incorrect input.", admin_error_response_model
    )
    @admin_namespace.response(
        409, "Email address already in use.", admin_error_response_model
    )
    @admin_namespace.response(
        500,
        "Server error encountered during admin account creation.",
        admin_error_response_model,
    )
    @is_admin
    def post(self):
        """
        Registers a new admin.

        * Requires authentication.
        * Email must be unique across all admin accounts.
        * Password must be complex (contain uppercase, lowercase, digits, and special characters).

        **Input Fields:**

        - `email` (string, **required**): The admin's chosen email. Must be unique.
        - `password` (string, **required**): The admin's chosen password. Must meet complexity requirements.
        - `name` (string): The name of the admin.
        """

        data = request.json
        email = data["email"]
        password = data["password"]
        name = data.get("name", None)

        if not validate_email(email):
            raise InvalidEmailError()

        # Check password complexity
        if not is_password_complex(password):
            raise PasswordNotComplexError()

        # Check if email already exists
        email_already_exists = auth_repository.email_already_exists(email)

        if email_already_exists:
            raise EmailAlreadyExistsError()

        # Create new admin
        id = auth_repository.create_admin(email, password, name)

        admin = auth_repository.find_user_without_password(
            identifier=id, search_by="id"
        )

        admin["_id"] = str(admin["_id"])
        admin["created_at"] = str(admin["created_at"])
        admin["role"] = admin["role"].lower()

        return {
            "message": "Admin created successfully",
            "data": admin,
            "code": 201,
        }, 201


@admin_namespace.route("/users")
class UserList(Resource):
    """
    Retrieves a paginated list of users based on query parameters. Supports filtering by username, status, role, and allows specifying the number of results to return and an offset for pagination. Useful for admins to manage user accounts effectively.
    """

    @admin_namespace.doc(security="Bearer Auth")
    @admin_namespace.doc(params=user_list_query_model_dict)
    @admin_namespace.response(
        200, "List of users fetched successfully.", model=user_list_response_model
    )
    @admin_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=admin_error_response_model,
    )
    @admin_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=admin_error_response_model,
    )
    @is_admin
    def get(self):
        """
        Retrieves users filtered by the provided parameters. Admins can use this endpoint to view and manage users efficiently.

        **Query Parameters:**

        - `uname` (string, optional): Filter users by a partial match of their username.
        - `query` (string, optional): Filter users by a partial match of their username and email.
        - `status` (string, optional): Filter users by their account status (e.g., active, awaiting, draft).
        - `role` (string, optional): Filter users by their role (e.g., user, admin).
        - `offset` (int, optional, default=0): The starting point for pagination (number of items to skip).
        - `limit` (int, optional, default=10): The maximum number of users to return.
        - `days` (int, optional): Filter users who were created or updated within the last N days.
        """

        parser = reqparse.RequestParser()
        parser.add_argument("uname", type=str)
        parser.add_argument("query", type=str)
        parser.add_argument(
            "status", choices=("active", "awaiting", "draft", "blocked")
        )
        parser.add_argument(
            "role", choices=("user", "admin"), help="Filter by user role"
        )
        parser.add_argument("offset", type=int, default=0)
        parser.add_argument("limit", type=int, default=10)
        parser.add_argument("days", type=int)
        args = parser.parse_args()

        query = args.pop("query", None)

        filters = {k: v for k, v in args.items() if v is not None}

        users, total_count = auth_repository.fetch_users(
            query, filters, args["offset"], args["limit"]
        )

        users_data = serialize_users(users)

        return {
            "message": "List of users fetched successfully.",
            "data": users_data,
            "meta": {
                "count": total_count,
            },
            "code": 200,
        }


@admin_namespace.route("/user-stats")
class UserStats(Resource):
    """
    Retrieves a paginated list of users based on query parameters. Supports filtering by username, status, role, and allows specifying the number of results to return and an offset for pagination. Useful for admins to manage user accounts effectively.
    """

    @admin_namespace.doc(security="Bearer Auth")
    @admin_namespace.response(
        200,
        "List of user counts fetched successfully.",
        model=user_counts_response_model,
    )
    @admin_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=admin_error_response_model,
    )
    @admin_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=admin_error_response_model,
    )
    @is_admin
    def get(self):
        """
        Fetch user counts : total and active users

        """
        total_users = auth_repository.fetch_user_count({})

        return {
            "message": "List of users counts fetched successfully.",
            "data": {"total": total_users},
            "code": 200,
        }


@admin_namespace.route("/user/<string:user_id>")
class UserUpdate(Resource):
    """
    Endpoint for updating user details.
    """

    @admin_namespace.doc(security="Bearer Auth")
    @admin_namespace.expect(user_update_request_model, validate=True)
    @admin_namespace.response(
        200, "User account updated successfully.", user_update_response_model
    )
    @admin_namespace.response(
        400,
        "Invalid request due to incorrect or missing input.",
        admin_error_response_model,
    )
    @admin_namespace.response(
        404, "User account not found.", admin_error_response_model
    )
    @admin_namespace.response(
        500,
        "Server error encountered during user account update.",
        admin_error_response_model,
    )
    @is_admin
    def put(self, user_id):
        """
        Updates an existing user's details.

        * Requires admin authentication.
        * Only fields provided in the request will be updated.

        **Path Parameters:**

        - `user_id` (string): The unique identifier of the user to update.

        **Input Fields:**

        - `email` (string): New email for the user.
        - `password` (string): New password for the user.
        - `name` (string): New name of the user.
        - `contact` (integer): New contact number of the user.
        - `organisation_name` (string): New organisation name the user belongs to.
        - `logo` (string): New link to the file representing the organisation's logo.
        - `status` (string): New status of the user. (draft, awaiting, active, blocked)
        """

        # Check if the user_id exists for a user to update
        if not auth_repository.user_already_exists(user_id):
            raise UserNotFoundError(description=f"User with ID {user_id} not found.")

        user = auth_repository.find_user_by_id(user_id)
        email = user["email"]

        data = request.json
        update_fields = {}

        # Only include fields that are present in the request

        if "name" in data:
            update_fields["name"] = data["name"]

        if "organisation_name" in data:
            update_fields["organisation_name"] = data["organisation_name"]

        if "logo" in data:
            update_fields["logo"] = data["logo"]

        if "contact" in data:
            update_fields["contact"] = data["contact"]

        if "email" in data:
            email = data["email"]
            if not validate_email(email):
                raise InvalidEmailError()

            # Check if email already exists
            email_already_exists = auth_repository.email_already_exists(email)
            if email_already_exists:
                raise EmailAlreadyExistsError()

            update_fields["email"] = email

        # Check password complexity
        if "password" in data:
            password = data["password"]
            if not is_password_complex(password):
                raise PasswordNotComplexError()

            update_fields["password"] = password

        if "status" in data:
            status = data["status"].upper()
            update_fields["status"] = status

        # Update user details, only if update_fields is not empty
        if update_fields:
            auth_repository.update_user(user_id, **update_fields)

            return {"message": "User updated successfully", "code": 200}, 200

        else:
            raise NoFieldsToUpdateError()
