from flask import g
from werkzeug.exceptions import BadRequest, NotFound, InternalServerError
from flask_restx import Resource

from .namespace import topic_namespace

from app.service.script import get_trending, get_similar_trending, get_topic_details
from app.controller.auth import login_required
from app.repositories.user_repository import AuthRepository
from app.repositories.script_repository import ScriptRepository
from .utils import create_response

from .schema import (
    trending_list_input_model,
    similar_trending_list_input_model,
    trending_response_model,
    trending_topic_model,
    error_response_model,
)

from app.service.youtube import get_trending_videos_and_topics

auth_repository = AuthRepository()
script_repository = ScriptRepository()


@topic_namespace.route("/trending")
class TrendingTopicsResource(Resource):
    @topic_namespace.doc(security="Bearer Auth")
    @topic_namespace.expect(trending_list_input_model)
    @topic_namespace.response(
        200,
        "Trending Topics fetched successfully.",
        model=trending_response_model,
    )
    @topic_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @topic_namespace.response(
        500,
        "Server error encountered while fetching trending topics.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """Fetch trending topics"""
        try:
            user_id = getattr(g, "user_id")
            args = trending_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]

            data = get_trending(user_id, page, per_page)
            return create_response("Trending Topics fetched successfully", data, 200)
        except BadRequest as e:
            return create_response(str(e), None, 400)
        except NotFound as e:
            return create_response(str(e), None, 404)
        except InternalServerError as e:
            print("Internal Server Error: " + str(e))
            return create_response(
                "An unexpected error occurred while fetching trending topics", None, 500
            )
        except Exception as e:
            topic_namespace.logger.error(
                f"Unexpected error in trending topics: {str(e)}"
            )
            return create_response("An unexpected error occurred", None, 500)


@topic_namespace.route("/trending/<string:topic_id>")
class TrendingTopicResource(Resource):
    @topic_namespace.doc(security="Bearer Auth")
    @topic_namespace.response(
        200,
        "Trending Topic fetched successfully.",
        model=trending_topic_model,
    )
    @topic_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @topic_namespace.response(
        500,
        "Server error encountered while fetching trending topics.",
        model=error_response_model,
    )
    @login_required
    def get(self, topic_id):
        """Fetch trending topics"""
        try:

            data = get_topic_details(topic_id)
            return create_response("Trending Topic fetched successfully", data, 200)
        except BadRequest as e:
            return create_response(str(e), None, 400)
        except NotFound as e:
            return create_response(str(e), None, 404)
        except InternalServerError as e:
            print("Internal Server Error: " + str(e))
            return create_response(
                "An unexpected error occurred while fetching trending topics", None, 500
            )
        except Exception as e:
            topic_namespace.logger.error(
                f"Unexpected error in trending topics: {str(e)}"
            )
            return create_response("An unexpected error occurred", None, 500)


@topic_namespace.route("/similar-trending")
class SimilarTrendingTopicsResource(Resource):
    @topic_namespace.doc(security="Bearer Auth")
    @topic_namespace.expect(similar_trending_list_input_model)
    @topic_namespace.response(
        200,
        "Trending Topics fetched successfully.",
        model=trending_response_model,
    )
    @topic_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @topic_namespace.response(
        500,
        "Server error encountered while fetching trending topics.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """Fetch trending topics"""
        try:
            args = similar_trending_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]
            domain = args["domain"]

            data = get_similar_trending(domain, page, per_page)
            return create_response("Trending Topics fetched successfully", data, 200)
        except BadRequest as e:
            return create_response(str(e), None, 400)
        except NotFound as e:
            return create_response(str(e), None, 404)
        except InternalServerError as e:
            print("Internal Server Error: " + str(e))
            return create_response(
                "An unexpected error occurred while fetching trending topics", None, 500
            )
        except Exception as e:
            topic_namespace.logger.error(
                f"Unexpected error in trending topics: {str(e)}"
            )
            return create_response("An unexpected error occurred", None, 500)


@topic_namespace.route("/trending-on-youtube")
class TrendingOnYoutubeResource(Resource):
    @topic_namespace.doc(security="Bearer Auth")
    @topic_namespace.expect(trending_list_input_model)
    @topic_namespace.response(
        200,
        "Trending Topics fetched successfully.",
        model=trending_response_model,
    )
    @topic_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @topic_namespace.response(
        500,
        "Server error encountered while fetching trending topics.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """Fetch trending topics"""
        try:
            user_id = getattr(g, "user_id")
            args = trending_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]

            user_data = auth_repository.find_user_by_id(user_id)

            if not user_data.get("youtube_credentials"):
                return {
                    "message": "You not authorized for YouTube",
                    "code": 404,
                }, 404

            data = get_trending_videos_and_topics(
                user_data.get("youtube_credentials"), user_data.get("email"), per_page
            )
            return create_response("Trending Topics fetched successfully", data, 200)
        except BadRequest as e:
            return create_response(str(e), None, 400)
        except NotFound as e:
            return create_response(str(e), None, 404)
        except InternalServerError as e:
            print("Internal Server Error: " + str(e))
            return create_response(
                "An unexpected error occurred while fetching trending topics", None, 500
            )
        except Exception as e:
            topic_namespace.logger.error(
                f"Unexpected error in trending topics: {str(e)}"
            )
            return create_response("An unexpected error occurred", None, 500)
