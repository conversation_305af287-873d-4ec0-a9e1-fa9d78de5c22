import json
from flask import g, request, Response
from werkzeug.exceptions import BadRequest, NotFound
from queue import Queue
from threading import Thread
from flask_restx import Resource, reqparse

from .namespace import script_namespace
from app.exceptions import handle_errors
from app.constant.subscription import SubscriptionStatus

from .schema import (
    script_generated_response_model,
    script_generate_using_topic_request_model,
    script_generate_using_video_request_model,
    error_response_model,
    script_update_request_model,
    regenerate_script_request_model,
    script_generate_request_model,
)

from app.exceptions.subscription_exceptions import (
    SubscriptionInactiveError,
    InsufficientCreditsError,
)

from app.service.script import (
    generate_script_using_video_link,
    generate_script_using_topic,
    generate_script_using_script,
    generate_script_using_blog,
    update_script,
    regenerate_script,
    generate_script,
)

from app.controller.auth import login_required
from app.repositories.subscription_repository import SubscriptionRepository
from app.repositories.user_repository import AuthRepository
from app.repositories.script_repository import ScriptRepository

from .utils import create_response
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

auth_repository = AuthRepository()
script_repository = ScriptRepository()
subscription_repository = SubscriptionRepository()


@script_namespace.route("/generate-script")
class ScriptGenerationResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_generate_request_model, validate=True)
    @script_namespace.response(200, "Script generation stream")
    @script_namespace.response(400, "Bad Request", model=error_response_model)
    @script_namespace.response(500, "Server Error", model=error_response_model)
    @login_required
    def post(self):
        """Generate a script with streaming updates"""
        try:
            data = request.get_json()
            user_id = g.user_id

            input_type = data["input_type"]
            topic = data["topic"]
            video_type = data["video_type"]
            link = data.get("link")
            keywords = data.get("keywords", [])
            video_link = data.get("video_link")
            add_brand = data.get("add_brand", False)
            music_media_id = data.get("music_media_id", None)

            if input_type in ["VIDEO", "BLOG"] and not link:
                raise BadRequest("Link is required for video and blog input types")

            user_subscription = subscription_repository.get_subscription_by_user_id(
                user_id
            )

            if (
                not user_subscription
                or user_subscription["subscription_status"]
                != SubscriptionStatus.ACTIVE.value
            ):
                raise SubscriptionInactiveError("User don't have active subscription.")

            if user_subscription.get("remaining_credits", 0) == 0:
                raise InsufficientCreditsError("User don't have active subscription.")

            queue = Queue()
            Thread(
                target=generate_script,
                args=(
                    user_id,
                    topic,
                    video_type,
                    keywords,
                    input_type,
                    add_brand,
                    music_media_id,
                    link,
                    video_link,
                    queue,
                ),
            ).start()

            return Response(
                return_streaming_response(queue),
                mimetype="text/event-stream",
                headers={"Cache-Control": "no-cache", "X-Accel-Buffering": "no"},
            )

        except BadRequest as e:
            logger.warning(f"Bad request: {str(e)}")
            return {"error": str(e)}, 400
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}", exc_info=True)
            return {"error": "An unexpected error occurred"}, 500


def return_streaming_response(queue: Queue):
    while True:
        message = queue.get()
        if message == "[DONE]":
            yield "data: [DONE]\n\n"
            break
        chunk = {
            "data": message,
        }
        yield f"data: {json.dumps(chunk)}\n\n"
        queue.task_done()


@script_namespace.route("/create-script-from-video")
class ScriptFromVideoResource(Resource):

    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_generate_using_video_request_model, validate=True)
    @script_namespace.response(
        201, "Script generated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(
        500,
        "Server error encountered while script generation.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """
        Generate a script using a video
        """
        user_id = getattr(g, "user_id")
        user = auth_repository.find_user_by_id(user_id)
        data = request.get_json()
        link = data.get("link", None)
        title = data.get("topic")
        video_type = data.get("video_type")
        keywords = data.get("keywords")
        video_link = data.get("video_link", None)
        add_brand = data.get("add_brand", False)
        music_media_id = data.get("music_media_id", None)

        script_data = generate_script_using_video_link(
            user["_id"],
            title,
            video_type,
            link,
            video_link,
            keywords,
            add_brand,
            music_media_id,
        )

        if script_data is None:
            raise BadRequest("script is not generated")

        return {
            "message": "Script Generated successfully",
            "data": script_data,
            "code": 200,
        }, 200


@script_namespace.route("/create-script-from-topic")
class ScriptFromTopicResource(Resource):

    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_generate_using_topic_request_model, validate=True)
    @script_namespace.response(
        201, "Script generated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(
        500,
        "Server error encountered while script generation.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """
        Generate a script using a topic
        """

        user_id = getattr(g, "user_id")

        user = auth_repository.find_user_by_id(user_id)

        data = request.get_json()
        topic = data.get("topic")
        video_type = data.get("video_type")
        keywords = data.get("keywords")
        add_brand = data.get("add_brand", False)
        music_media_id = data.get("music_media_id", None)

        script = generate_script_using_topic(
            user["_id"], topic, video_type, keywords, add_brand, music_media_id
        )

        if script is None:
            raise BadRequest("script is not generated")
        return {
            "message": "Script Generated successfully",
            "data": script,
            "code": 200,
        }, 200


@script_namespace.route("/create-script-from-blog")
class ScriptFromBlogResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_generate_using_video_request_model, validate=True)
    @script_namespace.response(
        201, "Script generated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(
        500,
        "Server error encountered while script generation.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """
        Generate a script using a topic
        """

        user_id = getattr(g, "user_id")

        user = auth_repository.find_user_by_id(user_id)

        data = request.get_json()
        topic = data.get("topic")
        video_type = data.get("video_type")
        link = data.get("link")
        keywords = data.get("keywords")
        add_brand = data.get("add_brand", False)
        music_media_id = data.get("music_media_id", None)

        script = generate_script_using_blog(
            user["_id"], topic, video_type, link, keywords, add_brand, music_media_id
        )

        if script is None:
            raise BadRequest("script is not generated")
        return {
            "message": "Script Generated successfully",
            "data": script,
            "code": 200,
        }, 200


@script_namespace.route("/create-script-from-script")
class ScriptFromScriptResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_generate_using_topic_request_model, validate=True)
    @script_namespace.response(
        201, "Script generated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(
        500,
        "Server error encountered while script generation.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """
        Generate a script using a topic
        """

        user_id = getattr(g, "user_id")

        user = auth_repository.find_user_by_id(user_id)

        data = request.get_json()
        topic = data.get("topic")
        video_type = data.get("video_type")
        keywords = data.get("keywords")
        add_brand = data.get("add_brand", False)
        music_media_id = data.get("music_media_id", None)

        script = generate_script_using_script(
            user["_id"], topic, video_type, keywords, add_brand, music_media_id
        )

        if script is None:
            raise BadRequest("script is not generated")

        return {
            "message": "Script Generated successfully",
            "data": script,
            "code": 200,
        }, 200


@script_namespace.route("/regenerate_script/<string:script_id>")
class RegenerateScriptResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(regenerate_script_request_model, validate=True)
    @script_namespace.response(
        201, "Script generated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(
        500,
        "Server error encountered while script generation.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def put(self, script_id):
        """
        Generate a script using a topic
        """

        user_id = getattr(g, "user_id")

        data = request.get_json()
        topic = data.get("topic")
        video_type = data.get("video_type", None)
        link = data.get("link", None)
        keywords = data.get("keywords")
        add_brand = data.get("add_brand", False)
        music_media_id = data.get("music_media_id", False)

        script_data = script_repository.get_script_of_user(user_id, script_id)

        if not script_data:
            raise NotFound("script is not found")

        script = regenerate_script(
            user_id,
            script_data,
            topic,
            video_type,
            link,
            keywords,
        )

        if script is None:
            raise BadRequest("script is not generated")

        return {
            "message": "Script Generated successfully",
            "data": script,
            "code": 200,
        }, 200


@script_namespace.route("/<string:script_id>")
class ScriptResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.response(
        200, "Script fetched successfully.", model=script_generated_response_model
    )
    @script_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @script_namespace.response(404, "Script not found.", model=error_response_model)
    @script_namespace.response(
        500, "An internal server error occurred.", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self, script_id):
        """Fetches and returns a specific script for the authenticated user."""

        user_id = getattr(g, "user_id")
        if not script_id:
            return create_response("Script ID is required.", code=400)

        script_data = script_repository.get_script_of_user(user_id, script_id)

        response = script_repository.to_dict(script_data)

        if not response:
            return create_response("Script not found.", code=404)

        return create_response("Script fetched successfully.", response)

    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(script_update_request_model, validate=True)
    @script_namespace.response(
        200, "Script updated successfully.", model=script_generated_response_model
    )
    @script_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @script_namespace.response(404, "Script not found.", model=error_response_model)
    @script_namespace.response(
        500, "An internal server error occurred.", model=error_response_model
    )
    @login_required
    @handle_errors
    def put(self, script_id):
        """Updates a specific script for the authenticated user."""

        user_id = getattr(g, "user_id")
        update_data = request.json

        if not script_id:
            return create_response("Script ID is required.", code=400)

        updated_script = update_script(user_id, script_id, update_data)

        updated_script = script_repository.to_dict(updated_script)

        if not updated_script:
            return create_response("Script not found or unauthorized.", code=404)

        return create_response("Script updated successfully.", updated_script)

    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.response(200, "Script deleted successfully.")
    @script_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @script_namespace.response(404, "Script not found.", model=error_response_model)
    @script_namespace.response(
        500, "An internal server error occurred.", model=error_response_model
    )
    @login_required
    @handle_errors
    def delete(self, script_id):
        """Deletes a specific script for the authenticated user."""

        user_id = getattr(g, "user_id")

        if not script_id:
            return create_response("Script ID is required.", code=400)

        success = script_repository.delete_script(user_id, script_id)

        if not success:
            return create_response("Script not found or unauthorized.", code=404)

        return create_response("Script deleted successfully.")


# Define the parser for query parameters
parser = reqparse.RequestParser()
parser.add_argument("page", type=int, required=False, default=1, help="Page number")
parser.add_argument(
    "limit", type=int, required=False, default=10, help="Items per page"
)
parser.add_argument("filter", type=str, required=False, help="Filter scripts by name")


@script_namespace.route("/all")
class AllScriptsResource(Resource):
    @script_namespace.doc(security="Bearer Auth")
    @script_namespace.expect(parser)
    @script_namespace.response(
        200, "Scripts fetched successfully.", model=script_generated_response_model
    )
    @script_namespace.response(401, "Unauthorized access.", model=error_response_model)
    @script_namespace.response(
        500, "An internal server error occurred.", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self):
        """Fetches and returns paginated and filtered scripts for the authenticated user."""

        # Try to parse arguments
        try:
            args = parser.parse_args()
        except Exception as parse_error:
            print(f"Error parsing arguments: {str(parse_error)}")
            return create_response(
                f"Error parsing arguments: {str(parse_error)}", code=400
            )

        user_id = getattr(g, "user_id", None)
        if user_id is None:
            return create_response("User ID not found in request context", code=401)

        page = args.get("page", 1)
        limit = args.get("limit", 10)
        filter = args.get("filter")

        scripts, total_count = script_repository.get_all_scripts_by_user(
            user_id, page, limit, filter
        )

        if scripts is None:
            return create_response("Error retrieving scripts.", code=500)

        scripts_dict = script_repository.to_dict(scripts)

        response_data = {
            "scripts": scripts_dict,
            "page": page,
            "limit": limit,
            "total_count": total_count,
            "total_pages": -(-total_count // limit),  # Ceiling division
        }

        return create_response("Scripts fetched successfully.", response_data)
