from flask_restx import fields, reqparse

from app.controller.script.namespace import script_namespace, topic_namespace
from app.constant.script_enums import ScriptType

from werkzeug.exceptions import BadRequest
import re


def validate_youtube_link(link):
    youtube_regex = r"^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$"
    if not re.match(youtube_regex, link):
        raise BadRequest("Invalid YouTube link")
    return link


script_generated_data_model = script_namespace.model(
    "Script",
    {
        "script": fields.String(),
    },
)

script_data_model = script_namespace.model(
    "ScriptData",
    {
        "_id": fields.String(description="Unique identifier of the script"),
        "script": fields.String(description="Script content"),
        "type": fields.String(description="Type of the script"),
        "created_at": fields.String(description="Creation timestamp"),
        "updated_at": fields.String(description="Last update timestamp"),
        "video_type": fields.String(
            required=True,
            description="Type of video: shorts or youtube.",
        ),
        "keywords": fields.Nested(
            script_namespace.model(
                "Keywords",
                {
                    "time": fields.String(
                        description="Time-related information for the video."
                    ),
                    "objective": fields.String(description="Objective of the video."),
                    "audience": fields.String(
                        description="Target audience for the video."
                    ),
                    "gender": fields.String(description="Gender focus, if applicable."),
                    "tone": fields.String(description="Tone of the video content."),
                    "speakers": fields.String(
                        description="Information about speakers in the video."
                    ),
                },
            ),
            required=True,
            description="Additional keywords and metadata for the video.",
        ),
    },
)

script_generated_response_model = script_namespace.model(
    "ScriptResponse",
    {
        "message": fields.String(example="Script generated successfully."),
        "data": fields.Nested(script_data_model),
        "code": fields.Integer(example=200),
    },
)

script_generate_request_model = script_namespace.model(
    "GenerateScriptModel",
    {
        "topic": fields.String(required=True, description="Topic of the video."),
        "video_type": fields.String(
            required=True, description="Type of video: shorts or youtube."
        ),
        "link": fields.String(
            required=False,
            description="YouTube link.",
            validate=validate_youtube_link,
        ),
        "video_link": fields.String(
            required=False,
            description="Video link.",
        ),
        "input_type": fields.String(
            required=True,
            enum=[e.value for e in ScriptType],
            description="Type of input for script generation",
        ),
        "add_brand": fields.Boolean(
            required=False, description="Whether want to add a brand"
        ),
        "music_media_id": fields.String(
            required=False, description="id of the music"
        ),
        "keywords": fields.Nested(
            script_namespace.model(
                "Keywords",
                {
                    "time": fields.String(
                        description="Time-related information for the video."
                    ),
                    "objective": fields.String(description="Objective of the video."),
                    "audience": fields.String(
                        description="Target audience for the video."
                    ),
                    "gender": fields.String(description="Gender focus, if applicable."),
                    "tone": fields.String(description="Tone of the video content."),
                    "speakers": fields.String(
                        description="Information about speakers in the video."
                    ),
                },
            ),
            required=True,
            description="Additional keywords and metadata for the video.",
        ),
    },
    strict=True,
)

script_generate_using_video_request_model = script_namespace.model(
    "generateScriptWithLink",
    {
        "topic": fields.String(required=True, description="Topic of the video."),
        "video_type": fields.String(
            required=True, description="Type of video: shorts or youtube."
        ),
        "link": fields.String(
            required=False,
            description="YouTube link.",
            validate=validate_youtube_link,
        ),
        "add_brand": fields.Boolean(
            required=False, description="Whether want to add a brand"
        ),
        "music_media_id": fields.String(
            required=False, description="id of the music"
        ),
        "video_link": fields.String(
            required=False,
            description="Video link.",
        ),
        "keywords": fields.Nested(
            script_namespace.model(
                "Keywords",
                {
                    "time": fields.String(
                        description="Time-related information for the video."
                    ),
                    "objective": fields.String(description="Objective of the video."),
                    "audience": fields.String(
                        description="Target audience for the video."
                    ),
                    "gender": fields.String(description="Gender focus, if applicable."),
                    "tone": fields.String(description="Tone of the video content."),
                    "speakers": fields.String(
                        description="Information about speakers in the video."
                    ),
                },
            ),
            required=True,
            description="Additional keywords and metadata for the video.",
        ),
    },
    strict=True,
)

regenerate_script_request_model = script_namespace.model(
    "regenerateScript",
    {
        "topic": fields.String(required=True, description="Topic of the video."),
        "video_type": fields.String(
            required=False, description="Type of video: shorts or youtube."
        ),
        "link": fields.String(required=False, description="YouTube link."),
        "keywords": fields.Nested(
            script_namespace.model(
                "Keywords",
                {
                    "time": fields.String(
                        description="Time-related information for the video."
                    ),
                    "objective": fields.String(description="Objective of the video."),
                    "audience": fields.String(
                        description="Target audience for the video."
                    ),
                    "gender": fields.String(description="Gender focus, if applicable."),
                    "tone": fields.String(description="Tone of the video content."),
                    "speakers": fields.String(
                        description="Information about speakers in the video."
                    ),
                },
            ),
            required=True,
            description="Additional keywords and metadata for the video.",
        ),
    },
    strict=True,
)

script_generate_using_topic_request_model = script_namespace.model(
    "generateScriptWithTopic",
    {
        "topic": fields.String(required=True, description="topic of the video."),
        "video_type": fields.String(
            required=True, description="type of video shorts and youtube."
        ),
        "add_brand": fields.Boolean(
            required=False, description="Whether want to add a brand"
        ),
        "music_media_id": fields.String(
            required=False, description="id of the music"
        ),
        "keywords": fields.Nested(
            script_namespace.model(
                "Keywords",
                {
                    "time": fields.String(
                        description="Time-related information for the video."
                    ),
                    "objective": fields.String(description="Objective of the video."),
                    "audience": fields.String(
                        description="Target audience for the video."
                    ),
                    "gender": fields.String(description="Gender focus, if applicable."),
                    "tone": fields.String(description="Tone of the video content."),
                    "speakers": fields.String(
                        description="Information about speakers in the video."
                    ),
                },
            ),
            required=True,
            description="Additional keywords and metadata for the video.",
        ),
    },
    strict=True,
)


script_request_model = script_namespace.model(
    "UserLogin",
    {
        "script_id": fields.String(required=True, description="The user's id."),
    },
    strict=True,
)


error_response_model = script_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)

script_update_request_model = script_namespace.model(
    "generateScript",
    {
        "script": fields.String(required=True, description="new updated script."),
    },
    strict=True,
)


trending_topic_model = topic_namespace.model(
    "Trending",
    {
        "_id": fields.String(description="Unique identifier for the trending topic"),
        "title": fields.String(description="Title of the trending topic"),
        "link": fields.String(description="Link to the trending topic"),
        "source": fields.String(description="Source of the trending topic"),
        "snippet": fields.String(
            description="Brief snippet or description of the topic"
        ),
        "date": fields.String(description="Date associated with the topic"),
        "image_url": fields.String(description="URL of the main image, if any"),
        "video_url": fields.String(description="URL of the main video, if any"),
        "field": fields.String(description="Field or category of the topic"),
        "summary": fields.String(description="Summary of the topic content"),
        "images": fields.List(
            fields.String, description="List of additional image URLs"
        ),
        "videos": fields.List(
            fields.String, description="List of additional video URLs"
        ),
        "created_at": fields.DateTime(
            description="Timestamp of when the topic was created"
        ),
        "updated_at": fields.DateTime(
            description="Timestamp of when the topic was last updated"
        ),
    },
)

# Pagination model
pagination_model = topic_namespace.model(
    "Pagination",
    {
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of items per page"),
        "total_count": fields.Integer(description="Total number of items"),
        "total_pages": fields.Integer(description="Total number of pages"),
    },
)

# Response model for voice list
trending_response_model = topic_namespace.model(
    "TrendingListResponse",
    {
        "message": fields.String(description="Response message"),
        "data": fields.List(
            fields.Nested(trending_topic_model), description="List of voices"
        ),
        "pagination": fields.Nested(
            pagination_model, description="Pagination information"
        ),
        "code": fields.Integer(description="Response code"),
    },
)


# Input model for trending list query parameters
trending_list_input_model = reqparse.RequestParser()
trending_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
trending_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)

# Input model for trending list query parameters
similar_trending_list_input_model = reqparse.RequestParser()
similar_trending_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
similar_trending_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
similar_trending_list_input_model.add_argument("domain", type=str, required=True)
