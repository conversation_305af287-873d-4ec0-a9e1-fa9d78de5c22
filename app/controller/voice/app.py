from flask import Blueprint
from flask_restx import Api

from .endpoint import voice_namespace


from app.exceptions.exception_handler import handle_error

from app.exceptions.common import (
    BaseExceptionClass,
    ForbiddenAccessError,
    InternalError,
    InvalidAuthError,
    InvalidAuthTokenFormatError,
    InvalidTokenError,
    InvalidValueError,
)

voice_blueprint = Blueprint("voice_blueprint", __name__)


# Define the authorization method to be used in the Swagger UI documentation
# This specifies that the API uses Bearer Token authentication for securing endpoints
authorizations: dict[str, dict[str, str]] = {
    "Bearer Auth": {
        "type": "apiKey",  # Specifies the type of security scheme
        "in": "header",  # Specifies where the API key is passed (in this case, the HTTP header)
        "name": "Authorization",  # Name of the header field to be used
        "devoiceion": "Type in the *'Value'* input box below: **'Bearer &lt;JWT_TOKEN&gt;'**, where JWT_TOKEN is your authentication token.",
    },
}

api = Api(
    app=voice_blueprint,
    title="Voice API",
    description="API endpoints for voice.",
    default="Voice API",
    authorizations=authorizations,  # Apply the defined authorization method
    default_label="Operations related to voice",
)

api.add_namespace(voice_namespace)

# Register the error handler for multiple exceptions
api.errorhandler(BaseExceptionClass)(handle_error)
api.errorhandler(InvalidValueError)(handle_error)
api.errorhandler(InvalidAuthTokenFormatError)(handle_error)
api.errorhandler(InvalidAuthError)(handle_error)
api.errorhandler(ForbiddenAccessError)(handle_error)
api.errorhandler(InvalidTokenError)(handle_error)
api.errorhandler(InternalError)(handle_error)
api.errorhandler(handle_error)
