from flask_restx import fields, reqparse, inputs

from .namespace import voice_namespace

# Define the input model
voice_clone_model = voice_namespace.model(
    "VoiceClone",
    {
        "sample_file_url": fields.String(
            required=True, description="URL of the audio file to clone"
        ),
        "voice_name": fields.String(
            required=True, description="Name for the new cloned voice"
        ),
        "language": fields.String(
            required=False, description="Language for the new cloned voice"
        ),
        "gender": fields.String(
            required=True, description="gender for the new cloned voice"
        ),
        "voice_person": fields.String(
            required=False, description="person for the new cloned voice"
        ),
    },
)

audio_generate_using_script_request_model = voice_namespace.model(
    "AudioGenerateUsingScriptRequest",
    {"script_id": fields.String(required=True, description="script id")},
)

error_response_model = voice_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)

# Voice model
voice_model = voice_namespace.model(
    "Voice",
    {
        "id": fields.String(description="Voice ID"),
        "user_id": fields.String(description="User ID"),
        "voice_id": fields.String(description="PlayHT Voice ID"),
        "name": fields.String(description="Voice Name"),
        "status": fields.String(description="Voice Status"),
        "link": fields.String(description="Sample audio link"),
        "accent": fields.String(description="Voice accent"),
        "age": fields.String(description="Voice age"),
        "gender": fields.String(description="Voice gender"),
        "language": fields.String(description="Voice language"),
        "language_code": fields.String(description="Voice language code"),
        "is_cloned": fields.Boolean(description="Whether the voice is cloned or not"),
        "create_at": fields.Date(description=" creation time"),
        "updated_at": fields.Date(description=" updated time"),
    },
)

# Pagination model
pagination_model = voice_namespace.model(
    "Pagination",
    {
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of items per page"),
        "total_count": fields.Integer(description="Total number of items"),
        "total_pages": fields.Integer(description="Total number of pages"),
    },
)

# Response model for voice list
voice_list_response_model = voice_namespace.model(
    "VoiceListResponse",
    {
        "message": fields.String(description="Response message"),
        "data": fields.List(fields.Nested(voice_model), description="List of voices"),
        "pagination": fields.Nested(
            pagination_model, description="Pagination information"
        ),
        "code": fields.Integer(description="Response code"),
    },
)

# Input model for voice list query parameters
voice_list_input_model = reqparse.RequestParser()
voice_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
voice_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
voice_list_input_model.add_argument(
    "gender",
    type=str,
    required=False,
    choices=["male", "female", "other"],
    help="Filter by gender",
)
voice_list_input_model.add_argument(
    "is_cloned", type=inputs.boolean, required=False, help="Filter by cloned status"
)
