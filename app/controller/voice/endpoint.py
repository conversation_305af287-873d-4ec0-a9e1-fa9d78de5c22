from .namespace import voice_namespace
from flask_restx import Resource
from flask import g, request
from werkzeug.exceptions import Unauthorized
from app.controller.auth import login_required

from app.service.voice import VoiceService
from app.service.play_ht import PlayHtService

from .schema import (
    voice_clone_model,
    audio_generate_using_script_request_model,
    error_response_model,
    voice_model,
    voice_list_response_model,
    voice_list_input_model,
)

from app.repositories.voice_repository import VoiceRepository

voice_repository = VoiceRepository()
voice_service = VoiceService()
play_ht_service = PlayHtService()


@voice_namespace.route("/voice_list")
class VoiceList(Resource):
    @voice_namespace.doc(security="Bearer Auth")
    @voice_namespace.expect(voice_list_input_model)
    @voice_namespace.response(
        200, "Successfully retrieved list of PlayHT voices", voice_list_response_model
    )
    @voice_namespace.response(401, "Unauthorized", error_response_model)
    @voice_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def get(self):
        """Get the list of PlayHT Voices with pagination and optional filters"""
        try:
            args = voice_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]
            gender = args["gender"]
            is_cloned = args["is_cloned"]

            user_id = getattr(g, "user_id")

            result = voice_service.get_default_voices(
                user_id, page, per_page, gender, is_cloned
            )

            return {
                "message": "Voices successfully fetched",
                "data": result["voices"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200
        except Exception as e:
            voice_namespace.abort(500, f"An error occurred: {str(e)}")


@voice_namespace.route("/voice_clone")
class VoiceClone(Resource):
    @voice_namespace.doc(security="Bearer Auth")
    @voice_namespace.expect(voice_clone_model)
    @voice_namespace.response(201, "Voice clone created successfully", voice_model)
    @voice_namespace.response(400, "Invalid parameters", error_response_model)
    @voice_namespace.response(401, "Unauthorized", error_response_model)
    @voice_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def post(self):
        """Create an instant voice clone"""
        try:
            data = request.json
            user_id = getattr(g, "user_id")

            voice_data = voice_service.clone_voice(
                user_id,
                data["sample_file_url"],
                data["voice_name"],
                data.get("language", "english"),
                data["gender"],
                data.get("voice_person", None),
            )
            return {
                "message": "voice successfully clone",
                "data": voice_data,
                "code": 201,
            }
        except ValueError as ve:
            return {"message": str(ve), "code": 400}
        except Exception as e:
            voice_namespace.abort(500, f"An error occurred: {str(e)}")

    @voice_namespace.doc(security="Bearer Auth")
    @voice_namespace.expect(voice_list_input_model)
    @voice_namespace.response(
        200, "Successfully retrieved list of cloned voices", voice_list_response_model
    )
    @voice_namespace.response(401, "Unauthorized", error_response_model)
    @voice_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def get(self):
        """Get the list of all cloned voices created by the user with pagination"""
        try:
            args = voice_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]

            user_id = getattr(g, "user_id")

            result = voice_service.get_voices(user_id, page, per_page, is_cloned=True)

            return {
                "message": "List of user's cloned voices",
                "data": result["voices"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200
        except Exception as e:
            voice_namespace.abort(500, f"An error occurred: {str(e)}")


@voice_namespace.route("/voice_clone/<string:voice_id>")
class VoiceCloneManagement(Resource):
    @voice_namespace.doc(security="Bearer Auth")
    @voice_namespace.response(200, "Voice deleted successfully")
    @voice_namespace.response(401, "Unauthorized", error_response_model)
    @voice_namespace.response(404, "Voice not found", error_response_model)
    @voice_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    def delete(self, voice_id):
        """Delete a cloned voice"""
        try:
            user_id = getattr(g, "user_id")
            result = voice_service.delete_voice(user_id, voice_id)
            if result:
                return {"message": "Voice deleted successfully", "code": 200}
            else:
                return {"message": "Voice not found", "code": 404}, 404
        except Exception as e:
            voice_namespace.abort(500, f"An error occurred: {str(e)}")


@voice_namespace.route("/generate-audio-of-script")
class ScriptFromVideoResource(Resource):

    @voice_namespace.doc(security="Bearer Auth")
    @voice_namespace.expect(audio_generate_using_script_request_model, validate=True)
    # @voice_namespace.response(
    #     201, "Script generated successfully.", model=voice_generated_response_model
    # )
    @voice_namespace.response(
        500,
        "Server error encountered while voice generation.",
        model=error_response_model,
    )
    @login_required
    def post(self):
        """
        Generate a voice using a video
        """

        user_id = getattr(g, "user_id")

        user = auth_repository.find_user_by_id(user_id)

        data = request.get_json()

        script_id = data.get("script_id")

        try:

            voice_data = generate_voice_using_video_link(
                user["_id"], title, video_type, link, keywords
            )

            return {
                "message": "Audio Generated successfully",
                "data": json.dumps(voice_data, cls=JSONEncoder),
                "code": 201,
            }, 201

        except Exception as e:
            return {
                "message": str(e),
                "code": 500,
            }, 500


@voice_namespace.route("/playht")
class PlayHTWebhook(Resource):
    @voice_namespace.doc("handle_playht_webhook")
    def post(self):
        """Handle PlayHT webhook"""
        try:
            bearer_token = request.headers.get("Authorization")
            play_ht_service.validate_secret_key(bearer_token)
            payload = request.json
            return voice_service.process_playht_hook(payload)
        except Unauthorized:
            voice_namespace.abort(401, "Unauthorized")
