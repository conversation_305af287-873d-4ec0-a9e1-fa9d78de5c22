from flask_restx import Resource
from flask import g, current_app
from werkzeug.exceptions import NotFound, BadRequest, Unauthorized, InternalServerError

from app.repositories.uploaded_video_repository import UploadedVideoRepository

from .namespace import upload_namespace
from .schema import (
    error_response_model,
    pagination_parser,
    uploaded_video_list_input_model,
    fetch_videos_response_model,
)

from ..auth import login_required

repo = UploadedVideoRepository()


def create_error_response(message, code, errors=None):
    response = {"message": message, "code": code}
    if errors:
        response["errors"] = errors
    return response, code


@upload_namespace.errorhandler(NotFound)
def handle_not_found_error(error):
    return create_error_response("Resource not found", 404)


@upload_namespace.errorhandler(BadRequest)
def handle_bad_request_error(error):
    return create_error_response("Bad request", 400, str(error))


@upload_namespace.errorhandler(Unauthorized)
def handle_unauthorized_error(error):
    return create_error_response("Unauthorized access", 401)


@upload_namespace.errorhandler(InternalServerError)
def handle_internal_server_error(error):
    current_app.logger.error(f"Internal Server Error: {str(error)}")
    return create_error_response("An internal server error occurred", 500)


@upload_namespace.route("/list_uploaded_videos")
class uploaded(Resource):
    @upload_namespace.doc(security="Bearer Auth")
    @upload_namespace.expect(uploaded_video_list_input_model)
    @upload_namespace.response(200, "Success", model=fetch_videos_response_model)
    @upload_namespace.response(401, "Unauthorized", model=error_response_model)
    @upload_namespace.response(500, "Internal Server Error", model=error_response_model)
    @login_required
    def get(self):
        try:
            user_id = getattr(g, "user_id")

            args = uploaded_video_list_input_model.parse_args()
            video_id = args["video_id"]
            page = args["page"]
            per_page = args["per_page"]
            args = pagination_parser.parse_args()

            result = repo.get_uploaded_videos_paginated(
                user_id, video_id, page, per_page
            )

            return {
                "message": "Uploaded videos successfully fetched",
                "data": result["videos"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200

        except Exception as e:
            current_app.logger.error(f"Error in uploaded: {str(e)}")
            raise InternalServerError(str(e))
