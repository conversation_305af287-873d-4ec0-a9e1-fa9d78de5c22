from flask import Blueprint
from flask_restx import Api

from .endpoint import video_namespace
from .youtube_endpoint import youtube_namespace
from .upload_endpoint import upload_namespace

from app.exceptions.exception_handler import handle_error

from app.exceptions.video_exceptions import (
    VideoNotFoundError,
    VideoInitializationError,
    VideoForbidden,
    VideoListFetchError,
    VideoInvalidStepOrder,
)

video_blueprint = Blueprint("video", __name__)


# Define the authorization method to be used in the Swagger UI documentation
# This specifies that the API uses Bearer Token authentication for securing endpoints
authorizations: dict[str, dict[str, str]] = {
    "Bearer Auth": {
        "type": "apiKey",  # Specifies the type of security scheme
        "in": "header",  # Specifies where the API key is passed (in this case, the HTTP header)
        "name": "Authorization",  # Name of the header field to be used
        "description": "Type in the *'Value'* input box below: **'Bearer &lt;JWT_TOKEN&gt;'**, where JWT_TOKEN is your authentication token.",
    },
}
api = Api(
    app=video_blueprint,
    title="Video API",
    description="API endpoints for Video.",
    authorizations=authorizations,  # Apply the defined authorization method
    security="Bearer Auth",  # Set the global security scheme to require Bearer Auth for all endpoints
    default="Video API",
    default_label="Operations related to Video",
)

api.add_namespace(video_namespace)
api.add_namespace(youtube_namespace)
api.add_namespace(upload_namespace)

api.errorhandler(VideoForbidden)(handle_error)
api.errorhandler(VideoNotFoundError)(handle_error)
api.errorhandler(VideoListFetchError)(handle_error)
api.errorhandler(VideoInvalidStepOrder)(handle_error)
api.errorhandler(VideoInitializationError)(handle_error)
