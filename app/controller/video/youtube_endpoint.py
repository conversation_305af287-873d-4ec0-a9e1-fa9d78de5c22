from flask_restx import Resource
from flask import g, request, current_app
from werkzeug.exceptions import NotFound, BadRequest, Unauthorized, InternalServerError

from app.repositories.schedule_video_repository import ScheduleVideoRepository
from app.repositories.video_repository import VideoRepository
from app.repositories.user_repository import AuthRepository
from app.service.video.video import VideoService
from app.service.video.upload_video import UploadVideoService
from .namespace import youtube_namespace
from .schema import (
    error_response_model,
    upload_model,
    schedule_video_model,
    pagination_parser,
    schedule_video_list_input_model,
    fetch_videos_response_model,
)

from app.constant.video_enums import UploadDomain
from ..auth import login_required

video_service = VideoService()
video_repository = VideoRepository()
upload_video_service = UploadVideoService()
auth_repository = AuthRepository()
repo = ScheduleVideoRepository()


def create_error_response(message, code, errors=None):
    response = {"message": message, "code": code}
    if errors:
        response["errors"] = errors
    return response, code


@youtube_namespace.errorhandler(NotFound)
def handle_not_found_error(error):
    return create_error_response("Resource not found", 404)


@youtube_namespace.errorhandler(BadRequest)
def handle_bad_request_error(error):
    return create_error_response("Bad request", 400, str(error))


@youtube_namespace.errorhandler(Unauthorized)
def handle_unauthorized_error(error):
    return create_error_response("Unauthorized access", 401)


@youtube_namespace.errorhandler(InternalServerError)
def handle_internal_server_error(error):
    current_app.logger.error(f"Internal Server Error: {str(error)}")
    return create_error_response("An internal server error occurred", 500)


@youtube_namespace.route("/schedule-video")
class ScheduleUpload(Resource):
    @youtube_namespace.doc(security="Bearer Auth")
    @youtube_namespace.expect(upload_model)
    @youtube_namespace.response(201, "Upload scheduled successfully")
    @youtube_namespace.response(400, "Bad Request", model=error_response_model)
    @youtube_namespace.response(401, "Unauthorized", model=error_response_model)
    @youtube_namespace.response(404, "Not Found", model=error_response_model)
    @youtube_namespace.response(
        500, "Internal Server Error", model=error_response_model
    )
    @login_required
    def post(self):
        try:
            user_id = getattr(g, "user_id")
            data = request.json

            upload_domain = data.get("upload_domain", UploadDomain.YOUTUBE.value)

            user_data = auth_repository.find_user_by_id(user_id)
            user_data = auth_repository.to_dict(user_data)

            if upload_domain == UploadDomain.YOUTUBE.value:
                credential = user_data.get("youtube_credentials")
                if not credential:
                    return {
                        "message": "You not authorized for YouTube",
                        "code": 404,
                    }, 404

            if upload_domain == UploadDomain.LINKEDIN.value:
                credential = user_data.get("linkedin_credentials")
                if not credential:
                    return {
                        "message": "You not authorized for Linkedin",
                        "code": 404,
                    }, 404

            video_id = data["video_id"]
            video_data = video_repository.get_video_by_user_id(user_id, video_id)
            video_data = video_repository.to_dict(video_data)

            if not video_data:
                raise NotFound("Video is not found.")

            if not (
                video_data.get("metadata")
                and video_data["metadata"].get("title")
                and video_data["metadata"].get("description")
            ):
                raise NotFound("Video metadata not found found.")

            response = upload_video_service.schedule_to_upload(
                user_data, video_data, upload_domain, data["scheduled_time"]
            )

            return {
                "message": "Upload scheduled successfully",
                "data": response,
                "status_code": 201,
            }, 201

        except Exception as e:
            current_app.logger.error(f"Error in ScheduleUpload: {str(e)}")
            raise InternalServerError(str(e))


@youtube_namespace.route("/list_schedule_videos")
class ScheduleVideoList(Resource):
    @youtube_namespace.doc(security="Bearer Auth")
    @youtube_namespace.expect(schedule_video_list_input_model)
    @youtube_namespace.response(200, "Success", model=fetch_videos_response_model)
    @youtube_namespace.response(401, "Unauthorized", model=error_response_model)
    @youtube_namespace.response(
        500, "Internal Server Error", model=error_response_model
    )
    @login_required
    def get(self):
        try:
            user_id = getattr(g, "user_id")

            args = schedule_video_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]
            status = args["status"]
            args = pagination_parser.parse_args()
            result = repo.get_schedule_videos_paginated(user_id, page, per_page, status)
            return {
                "message": "Schedule videos successfully fetched",
                "data": result["videos"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200
        except Exception as e:
            current_app.logger.error(f"Error in ScheduleVideoList: {str(e)}")
            raise InternalServerError(str(e))


@youtube_namespace.route("/schedule-video/<string:id>")
@youtube_namespace.response(404, "ScheduleVideo not found")
@youtube_namespace.param("id", "The schedule video identifier")
class ScheduleVideo(Resource):
    @youtube_namespace.doc(security="Bearer Auth")
    @youtube_namespace.response(200, "Success", model=schedule_video_model)
    @youtube_namespace.response(401, "Unauthorized", model=error_response_model)
    @youtube_namespace.response(404, "Not Found", model=error_response_model)
    @youtube_namespace.response(
        500, "Internal Server Error", model=error_response_model
    )
    @login_required
    def get(self, id):
        try:
            user_id = getattr(g, "user_id")
            schedule_video = repo.get_schedule_video_by_id_and_user(id, user_id)
            if not schedule_video:
                raise NotFound(f"ScheduleVideo {id} doesn't exist")

            return {
                "message": "Fetch successfully",
                "data": repo.to_dict(schedule_video),
                "status_code": 200,
            }, 200

        except Exception as e:
            current_app.logger.error(f"Error in ScheduleVideo GET: {str(e)}")
            raise InternalServerError(str(e))

    @youtube_namespace.doc(security="Bearer Auth")
    @youtube_namespace.expect(schedule_video_model)
    @youtube_namespace.response(200, "Success", model=schedule_video_model)
    @youtube_namespace.response(400, "Bad Request", model=error_response_model)
    @youtube_namespace.response(401, "Unauthorized", model=error_response_model)
    @youtube_namespace.response(404, "Not Found", model=error_response_model)
    @youtube_namespace.response(
        500, "Internal Server Error", model=error_response_model
    )
    @login_required
    def put(self, id):
        try:
            user_id = getattr(g, "user_id")
            update_data = youtube_namespace.payload
            updated_schedule_video = upload_video_service.update_schedule_video(
                user_id, id, update_data
            )
            if not updated_schedule_video:
                raise NotFound(f"ScheduleVideo {id} doesn't exist")
            return {
                "message": "update successfully",
                "data": repo.to_dict(updated_schedule_video),
                "status_code": 200,
            }, 200
        except Exception as e:
            current_app.logger.error(f"Error in ScheduleVideo PUT: {str(e)}")
            raise InternalServerError(str(e))

    @youtube_namespace.doc(security="Bearer Auth")
    @youtube_namespace.response(204, "ScheduleVideo deleted")
    @youtube_namespace.response(401, "Unauthorized", model=error_response_model)
    @youtube_namespace.response(404, "Not Found", model=error_response_model)
    @youtube_namespace.response(
        500, "Internal Server Error", model=error_response_model
    )
    @login_required
    def delete(self, id):
        try:
            user_id = getattr(g, "user_id")
            if not upload_video_service.delete_schedule_video(user_id, id):
                raise NotFound(f"ScheduleVideo {id} doesn't exist")
            return {
                "message": "deleted successfully",
                "data": "",
                "status_code": 204,
            }, 204
        except Exception as e:
            current_app.logger.error(f"Error in ScheduleVideo DELETE: {str(e)}")
            raise InternalServerError(str(e))
