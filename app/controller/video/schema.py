from flask_restx import fields, reqparse
from werkzeug.datastructures import FileStorage

from app.controller.video.namespace import video_namespace, youtube_namespace
from app.constant.video_enums import VideoStatus, VideoViewType, ScheduleVideoStatus
from app.helper.validation import url_validator


video_initialized_data_model = video_namespace.model(
    "VideoId",
    {
        "video_id": fields.String(),
        "task_id": fields.String(),
    },
)

video_added_response_model = video_namespace.model(
    "VideoAddedResponse",
    {
        "message": fields.String(example="Video initialized successfully."),
        "data": fields.Nested(video_initialized_data_model),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)

caption_model = video_namespace.model(
    "Caption",
    {
        "font_path": fields.String(required=True, description="subtitle font path"),
        "color_code": fields.String(required=True, description="subtitle color code"),
    },
)


generate_video_request_model = video_namespace.model(
    "GenerateVideo",
    {
        "script_id": fields.String(
            required=True, description="The script id for the video"
        ),
        "avatar_id": fields.String(
            required=False, description="The avatar id for the video (optional)"
        ),
        "voice_id": fields.String(
            required=False, description="The voice id for the video"
        ),
        "avatar_video_url": fields.String(
            required=False,
            description="The avatar video url(optional)",
            validate=url_validator,
        ),
        "template_id": fields.String(
            required=False,
            description="template_id for video",
        ),
        "caption": fields.Nested(caption_model, required=False),
    },
)

generate_video_by_id_request_model = video_namespace.model(
    "GenerateVideoById",
    {
        "video_id": fields.String(
            required=True, description="The script id for the video"
        ),
    },
)

upload_video_by_id = video_namespace.model(
    "GenerateVideoById",
    {
        "video_id": fields.String(
            required=True, description="The script id for the video"
        ),
    },
)

upload_video_model = video_namespace.model(
    "UpdateVideoModel",
    {
        "video_id": fields.String(
            required=True, description="The script id for the video"
        ),
        "upload_domain": fields.String(
            required=True,
            enum=["youtube", "linkedin", "instagram"],
            description="upload domain",
        ),
    },
)


fetch_video_request_model: dict[str, str] = {
    "video_id": "Mongo Id of the videos.",
}


video_response_model = video_namespace.model(
    "VideoResponseModel",
    {
        "_id": fields.String(description="Unique identifier for the video"),
        "user_id": fields.String(
            description="ID of the user associated with the video"
        ),
        "voice_id": fields.String(description="ID of the voice used in the video"),
        "avatar_id": fields.String(description="ID of the avatar used in the video"),
        "script_id": fields.String(description="ID of the script used for the video"),
        "status": fields.String(description="Current status of the video"),
        "link": fields.String(description="Link to the video"),
        "title": fields.String(description="Title of the video"),
        "avatar_video_ids": fields.List(
            fields.String, description="List of avatar video IDs"
        ),
        "avatar_video_urls": fields.List(
            fields.String, description="List of avatar video URLs"
        ),
        "created_at": fields.DateTime(description="Timestamp of video creation"),
        "updated_at": fields.DateTime(description="Timestamp of last update"),
        "template_id": fields.String(
            description="ID of the template used for the video"
        ),
        "status_message": fields.String(description="Additional status information"),
        "view_type": fields.String(description="Type of view for the video"),
        "metadata": fields.Raw(description="Additional metadata for the video"),
        "link_1080p": fields.String(
            description="Link to the 1080p version of the video"
        ),
        "thumbnail_link": fields.String(description="Link to the video thumbnail"),
    },
)


fetch_video_response_model = video_namespace.model(
    "VideoDetailsFetchResponse",
    {
        "message": fields.String(example="Video fetched successfully."),
        "data": fields.Nested(video_response_model),
        "code": fields.Integer(example="<error status code:integer>"),
    },
    strict=True,
)

video_status_data_model = video_namespace.model(
    "VideoStatusDataModel",
    {"status": fields.Integer(description="Status of the video")},
)

fetch_video_status_response_model = video_namespace.model(
    "VideoStatusFetchResponse",
    {
        "message": fields.String(example="Video fetched successfully."),
        "data": fields.Nested(video_status_data_model),
    },
    strict=True,
)

fetch_videos_response_model = video_namespace.model(
    "VideosDetailFetchResponse",
    {
        "message": fields.String(example="Videos fetched successfully."),
        "data": fields.List(fields.Nested(video_response_model)),
    },
    strict=True,
)

video_list_query_model_dict: dict[str, str] = {
    "page": "Specifies the current page number for pagination.",
    "limit": "Defines the number of items to return per page.",
    "sort_field": "Indicates the field by which the results should be sorted.",
    "sort_direction": "Determines the sort order, with 1 for ascending and -1 for descending.",
}

error_response_model = video_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)


def validate_view_type(value):
    if value not in [item.value for item in VideoViewType]:
        raise ValueError(
            f"Invalid view_type. Must be one of {[item.value for item in VideoViewType]}"
        )
    return value


def validate_status_type(value):
    if value not in [item.value for item in ScheduleVideoStatus]:
        raise ValueError(
            f"Invalid schedule_video_status. Must be one of {[item.value for item in ScheduleVideoStatus]}"
        )
    return value


def validate_status(value):
    if value not in [item.value for item in VideoStatus]:
        raise ValueError(
            f"Invalid status. Must be one of {[item.value for item in VideoStatus]}"
        )
    return value


video_list_input_model = reqparse.RequestParser()
video_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
video_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
video_list_input_model.add_argument(
    "status",
    type=validate_status,
    required=False,
    help="Video status. Must be one of: PENDING, PROCESSING, FAILED, COMPLETED, TIMED_OUT",
)
video_list_input_model.add_argument(
    "view_type",
    type=validate_view_type,
    required=False,
    help="Video view type. Must be either LANDSCAPE or PORTRAIT",
)
video_list_input_model.add_argument(
    "title",
    type=str,
    required=False,
    help="title of the video",
)


# Models
upload_model = video_namespace.model(
    "UploadDetails",
    {
        "title": fields.String(required=True, description="Video title"),
        "description": fields.String(required=True, description="Video description"),
        "tags": fields.List(fields.String, description="Video tags"),
        "category_id": fields.String(required=True, description="Video category ID"),
        "privacy_status": fields.String(
            required=True,
            enum=["public", "private", "unlisted"],
            description="Privacy status",
        ),
    },
)

# Parser for file upload
upload_parser = video_namespace.parser()
upload_parser.add_argument("file", location="files", type=FileStorage, required=True)


# Model for video upload request
upload_model = youtube_namespace.model(
    "UploadModel",
    {
        "video_id": fields.String(required=True, description="Video id"),
        "upload_domain": fields.String(
            required=False,
            enum=["youtube", "linkedin", "instagram"],
            description="upload domain",
            default="youtube",
        ),
        "scheduled_time": fields.DateTime(
            required=True, description="Scheduled upload time (ISO 8601 format)"
        ),
    },
)

# Define the ScheduleVideo model
schedule_video_model = youtube_namespace.model(
    "ScheduleVideo",
    {
        "_id": fields.String(
            readonly=True, description="The unique identifier of a ScheduleVideo"
        ),
        "user_id": fields.String(
            required=True, description="The user ID associated with the ScheduleVideo"
        ),
        "video_id": fields.String(
            required=True, description="The video ID associated with the ScheduleVideo"
        ),
        "metadata": fields.String(
            description="Additional metadata for the ScheduleVideo"
        ),
        "video_link": fields.String(required=True, description="The link to the video"),
        "scheduled_time": fields.DateTime(
            required=True, description="The scheduled time for the video"
        ),
        "status": fields.String(description="The status of the ScheduleVideo"),
        "created_at": fields.DateTime(
            readonly=True, description="The creation time of the ScheduleVideo"
        ),
        "updated_at": fields.DateTime(
            readonly=True, description="The last update time of the ScheduleVideo"
        ),
    },
)

# Parser for query parameters
pagination_parser = reqparse.RequestParser()
pagination_parser.add_argument("page", type=int, default=1, help="Page number")
pagination_parser.add_argument("per_page", type=int, default=10, help="Items per page")


schedule_video_list_input_model = reqparse.RequestParser()
schedule_video_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
schedule_video_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
schedule_video_list_input_model.add_argument(
    "status",
    type=validate_status_type,
    required=False,
    help="Video status. Must be one of: SCHEDULED, UPLOADED, FAILED,",
)


uploaded_video_list_input_model = reqparse.RequestParser()
uploaded_video_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
uploaded_video_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
uploaded_video_list_input_model.add_argument(
    "video_id", type=str, required=True, help="video id of the video"
)


# Model for video metadata with optional fields
metadata_model = video_namespace.model(
    "Metadata",
    {
        "title": fields.String(required=False, description="Video title"),
        "description": fields.String(required=False, description="Video description"),
        "keywords": fields.List(
            fields.String, required=False, description="Video keywords"
        ),
        "caption": fields.String(required=False, description="Video caption"),
    },
)

# Request model for updating metadata
update_metadata_model = video_namespace.model(
    "UpdateMetadata",
    {
        "metadata": fields.Nested(
            metadata_model, required=True, description="Video metadata"
        ),
    },
)

# Request model for updating metadata
update_video_model = video_namespace.model(
    "UpdateVideoMetadata",
    {
        "is_community_video": fields.Boolean(
            required=False, description="Community video"
        ),
        "thumbnail_link": fields.String(
            required=False, description="Video thumbnail link"
        ),
    },
)

# Response models
success_response_model = video_namespace.model(
    "SuccessResponse",
    {
        "message": fields.String(description="Success message"),
        "data": fields.Raw(description="Updated video data"),
    },
)


video_quality_model = video_namespace.model(
    "VideoQuality",
    {
        "quality": fields.String(
            required=True, description="Video quality (720p or 1080p)"
        ),
    },
)
