from flask_restx import Resource
from flask import g, request, Response
from bson import ObjectId
from werkzeug.exceptions import NotFound, BadRequest
from app.exceptions import handle_errors
from app.constant.subscription import SubscriptionStatus
import time
import json
from .namespace import video_namespace
from .schema import (
    video_added_response_model,
    fetch_video_request_model,
    fetch_video_response_model,
    fetch_videos_response_model,
    video_list_input_model,
    fetch_video_status_response_model,
    error_response_model,
    generate_video_request_model,
    generate_video_by_id_request_model,
    upload_video_by_id,
    update_metadata_model,
    success_response_model,
    update_video_model,
    upload_video_model,
)
from ..auth import login_required

from app.exceptions.subscription_exceptions import (
    SubscriptionInactiveError,
    InsufficientCreditsError,
)
from app.repositories.video_repository import VideoRepository
from app.repositories.user_repository import AuthRepository
from app.repositories.script_repository import ScriptRepository
from app.repositories.subscription_repository import SubscriptionRepository
from app.service.uploader.linkedin_upload import LinkedInUploader
from app.service.video.video import VideoService
from app.service.video.upload_video import UploadVideoService

from app.service.video.video_tasks import generate_video_task, generate_video

from app.constant.video_enums import VideoStatus, VideoViewType, VideoType, UploadDomain

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

video_service = VideoService()
video_repository = VideoRepository()
upload_video_service = UploadVideoService()
auth_repository = AuthRepository()
script_repository = ScriptRepository()
subscription_repository = SubscriptionRepository()


@video_namespace.route("/generate-video")
class GenerateVideo(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(generate_video_request_model, validate=True)
    @video_namespace.response(
        201,
        "Video initialized successfully.",
        model=video_added_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """
        Initialize a new video

        """
        user_id = getattr(g, "user_id")

        data = request.json
        script_id = data["script_id"]
        voice_id = data.get("voice_id", None)
        avatar_id = data.get("avatar_id", None)
        avatar_video_url = data.get("avatar_video_url", None)
        template_id = data.get("template_id", None)
        caption = data.get("caption", None)

        script_data = script_repository.get_script(script_id)

        if not script_data:
            raise NotFound("Script is not found")

        user_subscription = subscription_repository.get_subscription_by_user_id(user_id)

        if (
            not user_subscription
            or user_subscription["subscription_status"]
            != SubscriptionStatus.ACTIVE.value
        ):
            raise SubscriptionInactiveError("User don't have active subscription.")

        if user_subscription.get("remaining_credits", 0) <= 0:
            raise InsufficientCreditsError("User don't have active subscription.")

        # Create a new video entry with pending status
        video_data = video_repository.create_video(
            {
                "user_id": ObjectId(user_id),
                "voice_id": (ObjectId(voice_id) if voice_id is not None else None),
                "avatar_id": (ObjectId(avatar_id) if avatar_id is not None else None),
                "script_id": ObjectId(script_id),
                "avatar_video_urls": (
                    [avatar_video_url] if avatar_video_url is not None else None
                ),
                "status": VideoStatus.PENDING,
                "template_id": template_id,
                "view_type": (
                    VideoViewType.LANDSCAPE
                    if script_data.get("video_type") == VideoType.LONG.value
                    else VideoViewType.PORTRAIT
                ),
                "caption": caption,
            }
        )

        video_data = video_repository.to_dict(video_data)

        task = generate_video_task.delay(video_data)

        subscription_repository.update_subscription(
            str(user_subscription["_id"]),
            {"remaining_credits": user_subscription.get("remaining_credits") - 1},
        )

        return {
            "message": "Video generation task created successfully",
            "data": {
                "video_id": str(video_data["_id"]),
                "task_id": task.id,
                "view_type": video_data.get("view_type"),
            },
            "code": 201,
        }, 201


@video_namespace.route("/video-generation-status/<video_id>")
class VideoGenerationStatus(Resource):
    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.response(
        500, "Server error encountered while adding video.", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self, video_id):
        video_data = video_repository.get_video_by_id(video_id)

        if not video_data:
            return {"message": "Video not found"}, 404

        task_id = video_data.get("task_id")

        if not task_id:
            return {"message": "No task associated with this video"}, 400

        def generate():
            task = generate_video_task.AsyncResult(task_id)

            while not task.ready():
                if task.info:
                    yield f"data: {json.dumps(task.info)}\n\n"
                time.sleep(2)

            # Send the final result
            yield f"data: {json.dumps(task.result)}\n\n"
            yield "event: CLOSE\ndata: Stream closed\n\n"

        return Response(generate(), mimetype="text/event-stream")


@video_namespace.route("/generate-video-by-id")
class GenerateVideoById(Resource):
    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(generate_video_by_id_request_model, validate=True)
    @video_namespace.response(
        201, "Video initialized successfully.", model=video_added_response_model
    )
    @video_namespace.response(404, "Video not found.", model=error_response_model)
    @video_namespace.response(
        400, "Bad request or video already generated.", model=error_response_model
    )
    @video_namespace.response(
        500, "Server error encountered while adding video.", model=error_response_model
    )
    @login_required
    @handle_errors
    def post(self):
        user_id = getattr(g, "user_id")
        data = request.json
        video_id = data["video_id"]

        video_data = video_repository.get_video_by_user_id(user_id, video_id)
        if not video_data:
            raise NotFound("Video not found")

        if video_data.get("status") == VideoStatus.COMPLETED:
            raise BadRequest("Video has already been generated")

        if video_data.get("status") == VideoStatus.PROCESSING:
            raise BadRequest("Video is already in progress")

        user_subscription = subscription_repository.get_subscription_by_user_id(user_id)

        if (
            not user_subscription
            or user_subscription["subscription_status"]
            != SubscriptionStatus.ACTIVE.value
        ):
            raise SubscriptionInactiveError("User don't have active subscription.")

        if user_subscription.get("remaining_credits", 0) == 0:
            raise InsufficientCreditsError("User don't have active subscription.")

        video_data = video_repository.to_dict(video_data)

        try:
            task = generate_video_task.delay(video_data)
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            raise BadRequest("Error initializing video generation task")

        return {
            "message": "Video generation task created successfully",
            "data": {
                "video_id": str(video_data["_id"]),
                "task_id": task.id,
                "view_type": video_data.get("view_type"),
            },
            "code": 201,
        }, 201


@video_namespace.route("/generate-single-video-by-id")
class GenerateSingleVideoById(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(generate_video_by_id_request_model, validate=True)
    @video_namespace.response(
        201,
        "Video initialized successfully.",
        model=video_added_response_model,
    )
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        400,
        "Bad request or video already generated.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    def post(self):
        """
        Initialize a new video generation task
        """
        user_id = getattr(g, "user_id")
        data = request.json
        video_id = data["video_id"]

        try:
            video_data = video_repository.get_video_by_user_id(user_id, video_id)

            if not video_data:
                raise NotFound("Video not found")

            if video_data.get("status") == VideoStatus.COMPLETED:
                raise BadRequest("Video has already been generated")

            video_data = video_repository.to_dict(video_data)

            video_data = generate_video(video_data)

            video_data = video_repository.to_dict(video_data)

            return {
                "message": "Video generated successfully",
                "data": video_data,
                "code": 201,
            }, 201

        except NotFound as e:
            video_namespace.abort(404, e.description, code=404)
        except BadRequest as e:
            video_namespace.abort(400, e.description, code=400)
        except Exception as e:
            print("Error", str(e))
            video_namespace.abort(500, f"An error occurred: {str(e)}", code=500)


@video_namespace.route("/generate-single-video")
class GenerateSingleVideo(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(generate_video_request_model, validate=True)
    @video_namespace.response(
        201,
        "Video initialized successfully.",
        model=video_added_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    def post(self):
        """
        Initialize a new video

        """
        user_id = getattr(g, "user_id")

        data = request.json

        script_id = data["script_id"]
        voice_id = data["voice_id"]
        avatar_id = data.get("avatar_id", None)
        avatar_video_url = data.get("avatar_video_url", None)
        template_id = data.get("template_id", None)

        # Create a new video entry with pending status
        video_data = video_repository.create_video(
            {
                "user_id": ObjectId(user_id),
                "voice_id": ObjectId(voice_id),
                "avatar_id": (ObjectId(avatar_id) if avatar_id is not None else None),
                "script_id": ObjectId(script_id),
                "avatar_video_urls": (
                    [avatar_video_url] if avatar_video_url is not None else None
                ),
                "status": VideoStatus.PENDING,
                "template_id": template_id,
            }
        )

        video_data = video_repository.to_dict(video_data)

        response = generate_video(video_data)

        return {
            "message": "Video generation successfully",
            "data": response,
            "code": 201,
        }


@video_namespace.route("/get-video/<video_id>")
class getVideo(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.doc(params=fetch_video_request_model)
    @video_namespace.response(
        200,
        "Video Fetched successfully.",
        model=fetch_video_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self, video_id):

        user_id = getattr(g, "user_id")

        video = video_repository.get_video_by_user_id(user_id, video_id)

        if not video:
            raise NotFound("Video is not found.")

        video = video_repository.to_public_dict(video)

        return {
            "message": "Video Fetched successfully",
            "data": video,
            "code": 200,
        }


@video_namespace.route("/get-community-video/<video_id>")
class getCommunityVideo(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.doc(params=fetch_video_request_model)
    @video_namespace.response(
        200,
        "Video Fetched successfully.",
        model=fetch_video_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self, video_id):

        video = video_repository.get_video_details(video_id)

        if not video:
            raise NotFound("Video is not found.")

        return {
            "message": "Video Fetched successfully",
            "data": video,
            "code": 200,
        }


@video_namespace.route("/update-video/<video_id>")
class UpdateVideo(Resource):
    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(update_video_model, validate=True)
    @video_namespace.response(
        200,
        "Video updated successfully.",
        model=fetch_video_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def put(self, video_id):

        user_id = getattr(g, "user_id")

        data = request.json

        video = video_repository.get_video_by_user_id(user_id, video_id)

        if not video:
            raise NotFound("Video is not found.")

        video = video_repository.update_video(video_id, data)

        video = video_repository.to_public_dict(video)

        return {
            "message": "Video Updated successfully",
            "data": video,
            "code": 200,
        }


@video_namespace.route("/get-video-status/<video_id>")
class getVideoStatus(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.doc(params=fetch_video_request_model)
    @video_namespace.response(
        200,
        "Video Status Fetched successfully.",
        model=fetch_video_status_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self, video_id):

        video = video_repository.get_video_by_id(video_id)

        if not video:
            raise NotFound("Video is not found.")

        return {
            "message": "Video status fetched successfully",
            "data": {"status": video["status"]},
            "code": 200,
        }


@video_namespace.route("/get-videos")
class getVideos(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(video_list_input_model)
    @video_namespace.response(
        200,
        "Videos Fetched successfully.",
        model=fetch_videos_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self):

        user_id = getattr(g, "user_id")

        args = video_list_input_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        status = args.get("status")
        view_type = args.get("view_type")
        title = args.get("title")

        result = video_repository.find_videos_by_user_id(
            user_id, page, per_page, status, view_type, title
        )

        return {
            "message": "Videos successfully fetched",
            "data": result["videos"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200


@video_namespace.route("/delete-video/<video_id>")
class DeleteVideo(Resource):
    """
    Represent the deletion of a video
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.doc(params={"video_id": "The ID of the video to delete"})
    @video_namespace.response(
        200,
        "Video deleted successfully.",
        model=error_response_model,
    )
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        403,
        "Not authorized to delete this video.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while deleting video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def delete(self, video_id):
        """
        Delete a video by its ID
        """
        user_id = getattr(g, "user_id")

        video = video_repository.get_video_by_user_id(user_id, video_id)

        if not video:
            return {
                "message": "Video not found",
                "code": 404,
            }, 404

        video_repository.soft_delete_video(video_id)

        return {
            "message": "Video deleted successfully",
            "code": 200,
        }, 200


@video_namespace.route("/generate-metadata")
class GenerateMetadata(Resource):

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(upload_video_by_id, validate=True)
    @video_namespace.response(
        201,
        "Metadata Generated Successfully.",
        model=error_response_model,
    )
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        403,
        "Not authorized to delete this video.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while deleting video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):

        user_id = getattr(g, "user_id")
        data = request.json

        video_id = data["video_id"]

        user_details = auth_repository.get_user_details(user_id)

        if (
            not user_details.get("subscription")
            or user_details.get("subscription")["status"] != "active"
        ):
            raise SubscriptionInactiveError("User don't have active subscription.")

        video_data = video_repository.get_video_by_user_id(user_id, video_id)

        video_data = video_repository.to_dict(video_data)

        if not video_data:
            return {
                "message": "Video not found",
                "code": 404,
            }, 404

        metadata = video_data.get("metadata")

        if not (
            metadata
            or (
                user_details.get("user_setting")
                and user_details["user_setting"].get("auto_youtube_metadata")
            )
        ):
            return {
                "message": "Auto metadata generation disabled.",
                "code": 404,
            }, 404

        response = upload_video_service.generate_youtube_metadata(video_data)

        return {
            "message": "Metadata Generated Successfully",
            "data": response,
        }, 201


@video_namespace.route("/update-metadata/<string:video_id>")
class UpdateMetadata(Resource):
    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(update_metadata_model, validate=True)
    @video_namespace.response(
        200, "Metadata updated successfully.", model=success_response_model
    )
    @video_namespace.response(404, "Video not found.", model=error_response_model)
    @video_namespace.response(
        403, "Not authorized to update this video.", model=error_response_model
    )
    @video_namespace.response(
        500,
        "Server error encountered while updating metadata.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def put(self, video_id):
        user_id = getattr(g, "user_id")
        data = request.json

        new_metadata = data["metadata"]

        video_data = video_repository.get_video_by_user_id(user_id, video_id)

        if not video_data:
            return {"message": "Video not found", "code": 404}, 404

        try:
            # Get the existing metadata
            existing_metadata = video_data.get("metadata", {})

            # Update only the fields that are present in the request
            updated_metadata = {**existing_metadata, **new_metadata}

            updated_video = video_repository.update_video(
                video_id, {"metadata": updated_metadata}
            )

            return {
                "message": "Metadata updated successfully",
                "data": video_repository.to_dict(updated_video),
            }, 200

        except Exception as e:
            return {
                "message": f"Server error encountered while updating metadata: {str(e)}",
                "code": 500,
            }, 500


@video_namespace.route("/upload-video")
class VideoUpload(Resource):

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(upload_video_model, validate=True)
    @video_namespace.response(
        201,
        "Video uploaded successfully.",
        model=error_response_model,
    )
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        403,
        "Not authorized to delete this video.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while deleting video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """Upload a video to YouTube"""

        user_id = getattr(g, "user_id")
        data = request.json

        video_id = data["video_id"]
        upload_domain = data.get("upload_domain")

        video_data = video_repository.get_video_by_user_id(user_id, video_id)

        if not video_data:
            return {
                "message": "Video not found",
                "code": 404,
            }, 404

        video_data = video_repository.to_dict(video_data)

        user_data = auth_repository.find_user_by_id(user_id)

        if upload_domain == UploadDomain.YOUTUBE.value:
            credential = user_data.get("youtube_credentials")
            if not credential:
                return {
                    "message": "You not authorized for YouTube",
                    "code": 404,
                }, 404

        if upload_domain == UploadDomain.LINKEDIN.value:
            credential = user_data.get("linkedin_credentials")
            if not credential:
                return {
                    "message": "You not authorized for Linkedin",
                    "code": 404,
                }, 404

        user_subscription = subscription_repository.get_subscription_by_user_id(user_id)

        if not user_subscription or user_subscription["status"] != "active":
            raise SubscriptionInactiveError("User don't have active subscription.")

        if not (
            video_data.get("metadata")
            and video_data["metadata"].get("title")
            and video_data["metadata"].get("description")
        ):
            raise NotFound("Video metadata not found found.")

        try:

            if upload_domain == UploadDomain.YOUTUBE.value:
                response = upload_video_service.upload_on_youtube(user_data, video_data)

            if upload_domain == UploadDomain.LINKEDIN.value:
                linkedin_uploader = LinkedInUploader(
                    credential.get("access_token"), credential.get("sub")
                )
                response = linkedin_uploader.upload_video(user_data, video_data)

            return {
                "message": "Video uploaded successfully",
                "video_id": response["id"],
            }, 201

        except Exception as e:
            return {"message": str(e)}, 500


@video_namespace.route("/upload-video-on-instagram")
class UploadVideo(Resource):

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(upload_video_by_id, validate=True)
    @video_namespace.response(
        201,
        "Video uploaded successfully.",
        model=error_response_model,
    )
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        403,
        "Not authorized to delete this video.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while deleting video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def post(self):
        """Upload a video to Instagram"""

        user_id = getattr(g, "user_id")
        data = request.json

        user_data = auth_repository.find_user_by_id(user_id)

        user_data = auth_repository.to_dict(user_data)

        if not user_data.get("youtube_credentials"):
            return {
                "message": "You not authorized for YouTube",
                "code": 404,
            }, 404

        video_id = data["video_id"]

        video_data = video_repository.get_video_by_user_id(user_id, video_id)

        video_data = video_repository.to_dict(video_data)

        try:
            response = upload_video_service.upload_on_instagram(user_data, video_data)

            return {
                "message": "Video uploaded successfully",
                "video_id": response["id"],
            }, 201

        except Exception as e:
            return {"message": str(e)}, 500


@video_namespace.route("/download-video/<string:video_id>")
@video_namespace.response(404, "Video not found")
@video_namespace.param("video_id", "The video identifier")
@video_namespace.param(
    "quality", "Video quality (720p or 1080p)", enum=["720p", "1080p"]
)
class Video(Resource):
    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.doc("get_video")
    @video_namespace.response(200, "Success")
    @video_namespace.response(
        404,
        "Video not found.",
        model=error_response_model,
    )
    @video_namespace.response(
        403,
        "Not authorized to delete this video.",
        model=error_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while deleting video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self, video_id):
        """
        Download a video
        """
        user_id = getattr(g, "user_id")

        quality = request.args.get("quality", "720p")

        if quality not in ["720p", "1080p"]:
            video_namespace.abort(
                400, "Invalid quality. Choose either '720p' or '1080p'"
            )

        video_data = video_repository.get_video_by_user_id(user_id, video_id)

        video_data = video_repository.to_dict(video_data)

        if not video_data:
            return {
                "message": "Video not found",
                "code": 404,
            }, 404

        try:
            response = video_service.download_video_with_quality(
                user_id, video_data, quality
            )

            return {
                "message": "Video download fetch successfully",
                "video_id": response,
                "code": 200,
            }, 200

        except Exception as e:
            return {"message": str(e)}, 500


@video_namespace.route("/get-shared-video/<video_id>")
class getSharedVideo(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(params=fetch_video_request_model)
    @video_namespace.response(
        200,
        "Video Fetched successfully.",
        model=fetch_video_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @handle_errors
    def get(self, video_id):

        video = video_repository.get_video_by_id(video_id)

        if video:

            video = {
                "_id": str(video.get("_id", "")),
                "user_id": str(video.get("user_id", "")),
                "status": video.get("status", ""),
                "link": video.get("link", ""),
                "title": video.get("title", None),
                "created_at": video.get("created_at", None),
                "updated_at": video.get("updated_at", None),
                "status_message": video.get("status_message", None),
                "view_type": video.get("view_type", None),
                "metadata": video.get("metadata", None),
                "link_1080p": video.get("link_1080p", None),
                "thumbnail_link": video.get("thumbnail_link", None),
            }

        else:
            raise NotFound("Video is not found.")

        return {
            "message": "Video Fetched successfully",
            "data": video,
            "code": 200,
        }


@video_namespace.route("/get-community-videos")
class GetVideos(Resource):
    """
    Represent the collection of videos
    """

    @video_namespace.doc(security="Bearer Auth")
    @video_namespace.expect(video_list_input_model)
    @video_namespace.response(
        200,
        "Videos Fetched successfully.",
        model=fetch_videos_response_model,
    )
    @video_namespace.response(
        500,
        "Server error encountered while adding video.",
        model=error_response_model,
    )
    @login_required
    @handle_errors
    def get(self):

        args = video_list_input_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        view_type = args["view_type"]

        result = video_repository.find_community_videos(page, per_page, view_type)

        return {
            "message": "Videos successfully fetched",
            "data": result["videos"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200
