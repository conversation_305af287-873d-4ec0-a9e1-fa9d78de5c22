from .namespace import media_namespace
from flask_restx import Resource
from flask import g, request
from app.controller.auth import login_required

from bson import ObjectId
from app.service.media import MediaService

from app.exceptions import handle_errors
from .schema import (
    error_response_model,
    media_create_model,
    media_response_model,
    media_update_model,
    media_list_model,
    music_media_list_model,
)

from app.repositories.media_repository import MediaRepository
from app.repositories.subscription_repository import SubscriptionRepository


media_repository = MediaRepository()

media_service = MediaService()
subscription_repository = SubscriptionRepository()


@media_namespace.route("/add-media")
class MediaManagement(Resource):
    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.expect(media_create_model)
    @media_namespace.response(201, "Media created successfully", media_response_model)
    @media_namespace.response(400, "Bad request", error_response_model)
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def post(self):
        """Create a new media"""

        user_id = getattr(g, "user_id")

        # user_subscription = subscription_repository.get_subscription_by_user_id(user_id)

        # if (
        #     not user_subscription
        #     or user_subscription["status"] != "active"
        #     # or user_subscription["amount_paid"] == 0
        # ):
        #     raise ForbiddenAccessError("User don't have active subscription.")

        data = request.json

        data["user_id"] = ObjectId(user_id)

        result = media_service.create_media(data)
        return {
            "message": "Media created successfully",
            "code": 201,
            "data": result,
        }, 201


@media_namespace.route("/medias")
class MediasManagement(Resource):
    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.expect(media_list_model)
    @media_namespace.response(201, "Media created successfully", media_response_model)
    @media_namespace.response(400, "Bad request", error_response_model)
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def get(self):
        """Create a new media"""

        user_id = getattr(g, "user_id")

        args = media_list_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        media_type = args["type"]
        category = args["category"]
        folder = args["folder"]

        result = media_service.get_medias(
            user_id, page, per_page, media_type, category, folder
        )

        return {
            "message": "media successfully fetched",
            "data": result["medias"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200


@media_namespace.route("/media/<string:media_id>")
class MediaUpdateManagement(Resource):
    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.expect(media_update_model)
    @media_namespace.response(200, "Media updated successfully", media_response_model)
    @media_namespace.response(400, "Bad request", error_response_model)
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(404, "Media not found", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def put(self, media_id):
        """Update an existing media"""
        user_id = getattr(g, "user_id")
        data = request.json
        result = media_service.update_media(user_id, media_id, data)
        if result:
            return {
                "message": "Media updated successfully",
                "code": 200,
                "data": result,
            }, 200
        else:
            return {"message": "Media not found", "code": 404, "data": None}, 404

    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.response(200, "Media retrieved successfully", media_response_model)
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(404, "Media not found", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def get(self, media_id):
        """Get a specific media"""

        user_id = getattr(g, "user_id")
        result = media_service.get_media(user_id, media_id)
        if result:
            return {
                "message": "Media retrieved successfully",
                "code": 200,
                "data": result,
            }, 200
        else:
            return {"message": "Media not found", "code": 404, "data": None}, 404


@media_namespace.route("/remove-media/<string:media_id>")
class MediaCloneManagement(Resource):
    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.response(200, "Media deleted successfully")
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(404, "Media not found", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def delete(self, media_id):
        """Delete a cloned media"""

        user_id = getattr(g, "user_id")
        result = media_service.delete_media(user_id, media_id)
        if result:
            return {"message": "Media deleted successfully", "code": 200}
        else:
            return {"message": "Media not found", "code": 404}, 404


@media_namespace.route("/media/musics")
class MusicMediasManagement(Resource):
    @media_namespace.doc(security="Bearer Auth")
    @media_namespace.expect(music_media_list_model)
    @media_namespace.response(201, "Media created successfully", media_response_model)
    @media_namespace.response(400, "Bad request", error_response_model)
    @media_namespace.response(401, "Unauthorized", error_response_model)
    @media_namespace.response(500, "Internal server error", error_response_model)
    @login_required
    @handle_errors
    def get(self):
        """Create a new media"""

        user_id = getattr(g, "user_id")

        args = music_media_list_model.parse_args()
        page = args["page"]
        per_page = args["per_page"]
        media_type = "music"
        category = args["category"]
        folder = args["folder"]
        is_public = args["is_public"]

        if is_public:
            user_id = None

        result = media_service.get_medias(
            user_id, page, per_page, media_type, category, folder
        )

        return {
            "message": "media successfully fetched",
            "data": result["medias"],
            "pagination": {
                "page": result["page"],
                "per_page": result["per_page"],
                "total_count": result["total_count"],
                "total_pages": result["total_pages"],
            },
            "code": 200,
        }, 200
