from flask_restx import fields, reqparse
from .namespace import media_namespace


# Enum for social media platforms
social_platforms = [
    "web",
    "linkedin",
    "facebook",
    "youtube",
    "twitter",
    "x",
    "instagram",
]

# Social Link model
social_link_model = media_namespace.model(
    "SocialLink",
    {
        "platform": fields.String(
            required=True,
            description="Social media platform name",
            enum=social_platforms,
        ),
        "url": fields.String(
            required=True, description="URL of the social media profile"
        ),
    },
)

# Request model for creating/updating a media
media_create_model = media_namespace.model(
    "MediaRequest",
    {
        "title": fields.String(required=True, description="Name of the media"),
        "description": fields.String(
            required=False, description="description of the media"
        ),
        "link": fields.String(required=True, description="URL of the media"),
        "type": fields.String(required=True, description="type of the media"),
        "mime_type": fields.String(required=True, description="mime type of the media"),
        "category": fields.String(required=False, description="category of the media"),
        "folder": fields.String(required=False, description="folder of the media"),
    },
)

# Response model for media operations
media_model = media_namespace.model(
    "MediaResponse",
    {
        "_id": fields.String(description="Unique identifier for the media"),
        "user_id": fields.String(description="ID of the user who owns the media"),
        "name": fields.String(description="Name of the media"),
        "logo": fields.String(description="URL of the media logo"),
        "intro_landscape": fields.String(
            description="Intro video URL for landscape orientation"
        ),
        "outro_landscape": fields.String(
            description="Outro video URL for landscape orientation"
        ),
        "intro_portrait": fields.String(
            description="Intro video URL for portrait orientation"
        ),
        "outro_portrait": fields.String(
            description="Outro video URL for portrait orientation"
        ),
        "created_at": fields.DateTime(description="Timestamp of media creation"),
        "updated_at": fields.DateTime(description="Timestamp of last media update"),
    },
)

# Response model for single media operations
media_response_model = media_namespace.model(
    "MediaResponse",
    {
        "message": fields.String(required=True, description="Response message"),
        "code": fields.Integer(required=True, description="Response code"),
        "data": fields.Nested(media_model, description="Media data"),
    },
)

# Response model for listing medias
media_list_response_model = media_namespace.model(
    "MediaListResponse",
    {
        "medias": fields.List(fields.Nested(media_response_model)),
        "total": fields.Integer(description="Total number of medias"),
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of medias per page"),
    },
)

# Request model for updating a media (all fields optional)
media_update_model = media_namespace.model(
    "MediaUpdateRequest",
    {
        "title": fields.String(required=False, description="Name of the media"),
        "description": fields.String(
            required=False, description="description of the media"
        ),
        "link": fields.String(required=False, description="URL of the media"),
        "type": fields.String(required=False, description="type of the media"),
        "mime_type": fields.String(
            required=False, description="mime type of the media"
        ),
    },
)

# Error response model
error_response_model = media_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(required=True, description="Error message"),
        "code": fields.Integer(required=True, description="Error code"),
    },
)

# Input model for avatar list query parameters
media_list_model = reqparse.RequestParser()
media_list_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
media_list_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
media_list_model.add_argument("type", type=str, required=False, help="type of media")
media_list_model.add_argument(
    "folder", type=str, required=False, help="folder of media"
)
media_list_model.add_argument(
    "category", type=str, required=False, help="category of media"
)


# Input model for avatar list query parameters
music_media_list_model = reqparse.RequestParser()
music_media_list_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
music_media_list_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
music_media_list_model.add_argument(
    "category", type=str, required=False, help="category of media"
)
music_media_list_model.add_argument(
    "folder", type=str, required=False, help="folder of media"
)
music_media_list_model.add_argument(
    "is_public", type=bool, required=False, default=True, help="is public of media"
)
