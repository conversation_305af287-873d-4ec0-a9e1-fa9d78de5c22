from flask_restx import fields, reqparse, inputs
from .namespace import avatar_namespace

# Define models
tavus_avatar_model = avatar_namespace.model(
    "TavusAvatar",
    {
        "replica_id": fields.String(
            required=True, description="The replica identifier"
        ),
        "replica_name": fields.String(required=True, description="The replica name"),
        "thumbnail_video_url": fields.String(description="URL of the thumbnail video"),
        "training_progress": fields.String(description="Training progress status"),
        "status": fields.String(description="Current status of the replica"),
        "created_at": fields.String(description="Creation timestamp"),
    },
)

tavus_avatar_list_model = avatar_namespace.model(
    "TavusAvatarList",
    {
        "avatars": fields.List(fields.Nested(tavus_avatar_model)),
        "message": fields.String(description="Additional information"),
    },
)

# Avatar model
avatar_model = avatar_namespace.model(
    "Avatar",
    {
        "id": fields.String(description="Avatar ID"),
        "user_id": fields.String(description="User ID"),
        "avatar_id": fields.String(description="PlayHT Avatar ID"),
        "name": fields.String(description="Avatar Name"),
        "status": fields.String(description="Avatar Status"),
        "link": fields.String(description="Sample audio link"),
        "is_cloned": fields.Boolean(description="Whether the avatar is cloned or not"),
    },
)

# Pagination model
pagination_model = avatar_namespace.model(
    "Pagination",
    {
        "page": fields.Integer(description="Current page number"),
        "per_page": fields.Integer(description="Number of items per page"),
        "total_count": fields.Integer(description="Total number of items"),
        "total_pages": fields.Integer(description="Total number of pages"),
    },
)

# Response model for avatar list
avatar_list_response_model = avatar_namespace.model(
    "AvatarListResponse",
    {
        "message": fields.String(description="Response message"),
        "data": fields.List(fields.Nested(avatar_model), description="List of avatars"),
        "pagination": fields.Nested(
            pagination_model, description="Pagination information"
        ),
        "code": fields.Integer(description="Response code"),
    },
)

avatar_list_model = avatar_namespace.model(
    "AvatarList",
    {
        "avatars": fields.List(fields.Nested(avatar_model)),
        "message": fields.String(description="Additional information"),
    },
)

error_model = avatar_namespace.model(
    "ErrorResponse",
    {
        "message": fields.String(required=True, description="Error message"),
        "error_code": fields.Integer(description="Error code"),
    },
)

# Request model
create_replica_model = avatar_namespace.model(
    "CreateReplica",
    {
        "consent_video_url": fields.String(description="URL of the consent video"),
        "train_video_url": fields.String(
            required=True, description="URL of the training video"
        ),
        "replica_name": fields.String(description="Name of the replica"),
        "gender": fields.String(description="Gender of the replica"),
    },
)

# Response model
replica_response_model = avatar_namespace.model(
    "ReplicaResponse",
    {
        "replica_id": fields.String(description="Unique identifier for the replica"),
        "status": fields.String(description="Status of the replica"),
    },
)

# Input model for avatar list query parameters
avatar_list_input_model = reqparse.RequestParser()
avatar_list_input_model.add_argument(
    "page", type=int, required=False, default=1, help="Page number"
)
avatar_list_input_model.add_argument(
    "per_page", type=int, required=False, default=10, help="Items per page"
)
avatar_list_input_model.add_argument(
    "is_cloned", type=inputs.boolean, required=False, help="Filter by cloned status"
)
