from .namespace import avatar_namespace
from flask_restx import Resource
from flask import g, request

import requests

from app.controller.auth import login_required

from app.service.tavus import TavusService


from app.repositories.avatar_repository import AvatarRepository
from app.service.avatar import AvatarService


from .schema import (
    error_model,
    avatar_list_model,
    replica_response_model,
    create_replica_model,
    avatar_model,
    avatar_list_input_model,
    avatar_list_response_model,
)

avatar_repository = AvatarRepository()
tavus_service = TavusService()
avatar_service = AvatarService()


@avatar_namespace.route("/avatar_list")
class AvatarsList(Resource):

    @avatar_namespace.doc(security="Bearer Auth")
    @avatar_namespace.expect(avatar_list_input_model)
    @avatar_namespace.response(200, "Success", avatar_list_response_model)
    @avatar_namespace.response(401, "Unauthorized", error_model)
    @avatar_namespace.response(500, "Internal Server Error", error_model)
    @login_required
    def get(self):
        """Fetch all avatars from Tavus"""
        try:
            args = avatar_list_input_model.parse_args()
            page = args["page"]
            per_page = args["per_page"]
            is_cloned = args["is_cloned"]

            user_id = getattr(g, "user_id")

            result = avatar_service.get_default_avatars(
                user_id, page, per_page, is_cloned
            )

            return {
                "message": "Avatar successfully fetched",
                "data": result["avatars"],
                "pagination": {
                    "page": result["page"],
                    "per_page": result["per_page"],
                    "total_count": result["total_count"],
                    "total_pages": result["total_pages"],
                },
                "code": 200,
            }, 200

        except requests.exceptions.HTTPError as e:
            error_message = "Error fetching data from Tavus API"
            error_code = 500

            if e.response.status_code == 401:
                error_message = "Unauthorized. Please check your API key."
                error_code = 401

            avatar_namespace.abort(error_code, error_message, error_code=error_code)

        except requests.exceptions.RequestException as e:
            avatar_namespace.abort(
                500, f"Error connecting to Tavus API: {str(e)}", error_code=500
            )

        except Exception as e:
            avatar_namespace.abort(500, f"Unexpected error: {str(e)}", error_code=500)


@avatar_namespace.route("/create_avatar")
class CreateAvatar(Resource):

    @avatar_namespace.doc(security="Bearer Auth")
    @avatar_namespace.expect(create_replica_model)
    @avatar_namespace.response(200, "Success", replica_response_model)
    @avatar_namespace.response(400, "Bad Request")
    @avatar_namespace.response(401, "Unauthorized")
    @avatar_namespace.response(500, "Internal Server Error")
    @login_required
    def post(self):
        """Create a new Replica"""
        try:

            user_id = getattr(g, "user_id")

            data = request.json

            callback_url = ""

            data["callback_url"] = callback_url

            print(data)

            data = tavus_service.create_replica(data)

            avatar_id = data["replica_id"]

            avatar_data = avatar_repository.create_avatar(
                {
                    "avatar_id": avatar_id,
                    "user_id": user_id,
                    "status": data["status"],
                    "link": data["train_video_url"],
                    "name": data["replica_name"],
                    "is_cloned": True,
                    "gender": data["gender"],
                }
            )

            avatar_data = avatar_repository.to_dict(avatar_data)

            return {
                "message": "Avatar clone response submitted successfully",
                "data": avatar_data,
                "code": 200,
            }, 200

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                avatar_namespace.abort(401, "Unauthorized. Please check your API key.")
            elif e.response.status_code == 400:
                avatar_namespace.abort(400, f"Bad request: {e.response.text}")
            else:
                avatar_namespace.abort(500, f"Error calling Tavus API: {str(e)}")

        except requests.exceptions.RequestException as e:
            avatar_namespace.abort(500, f"Error connecting to Tavus API: {str(e)}")

        except Exception as e:
            avatar_namespace.abort(500, f"Unexpected error: {str(e)}")


@avatar_namespace.route("/clone_avatars")
class AvatarList(Resource):
    @avatar_namespace.doc(security="Bearer Auth")
    @avatar_namespace.response(200, "Success", avatar_list_model)
    @avatar_namespace.response(401, "Unauthorized", error_model)
    @avatar_namespace.response(500, "Internal Server Error", error_model)
    @login_required
    def get(self):
        """Get all avatars for the authenticated user"""
        try:
            user_id = getattr(g, "user_id")

            avatars = avatar_repository.get_avatars_by_user(user_id)

            avatar_list = [avatar_repository.to_dict(avatar) for avatar in avatars]

            return {
                "message": "Avatars retrieved successfully",
                "data": {"avatars": avatar_list},
                "code": 200,
            }, 200

        except Exception as e:
            avatar_namespace.abort(500, f"Unexpected error: {str(e)}")


@avatar_namespace.route("/<string:avatar_id>")
class AvatarResource(Resource):

    @avatar_namespace.doc(security="Bearer Auth")
    @avatar_namespace.response(200, "Success", avatar_model)
    @avatar_namespace.response(401, "Unauthorized", error_model)
    @avatar_namespace.response(404, "Avatar not found", error_model)
    @avatar_namespace.response(500, "Internal Server Error", error_model)
    @login_required
    def get(self, avatar_id):
        """Get a specific avatar"""
        try:
            user_id = getattr(g, "user_id")

            avatar_data = avatar_repository.get_avatar_of_user_by_id(avatar_id, user_id)

            if not avatar_data:
                avatar_namespace.abort(404, "Avatar not found")

            avatar_data = avatar_repository.to_dict(avatar_data)

            return {
                "message": "Avatar retrieved successfully",
                "data": avatar_data,
                "code": 200,
            }, 200

        except Exception as e:
            avatar_namespace.abort(500, f"Unexpected error: {str(e)}")

    @avatar_namespace.doc(security="Bearer Auth")
    @avatar_namespace.response(200, "Success")
    @avatar_namespace.response(401, "Unauthorized", error_model)
    @avatar_namespace.response(404, "Avatar not found", error_model)
    @avatar_namespace.response(500, "Internal Server Error", error_model)
    @login_required
    def delete(self, avatar_id):
        """Delete a specific avatar"""
        try:
            user_id = getattr(g, "user_id")

            # First, check if the avatar exists and belongs to the user
            avatar_data = avatar_repository.get_avatar(avatar_id, user_id)

            if not avatar_data:
                avatar_namespace.abort(404, "Avatar not found")

            # Delete from Tavus
            tavus_service.delete_replica(avatar_id)

            # Delete from our database
            avatar_repository.delete_avatar(user_id, avatar_id)

            return {"message": "Avatar deleted successfully", "code": 200}, 200

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                avatar_namespace.abort(401, "Unauthorized. Please check your API key.")
            elif e.response.status_code == 404:
                avatar_namespace.abort(404, "Avatar not found on Tavus")
            else:
                avatar_namespace.abort(500, f"Error calling Tavus API: {str(e)}")

        except requests.exceptions.RequestException as e:
            avatar_namespace.abort(500, f"Error connecting to Tavus API: {str(e)}")

        except Exception as e:
            avatar_namespace.abort(500, f"Unexpected error: {str(e)}")


@avatar_namespace.route("/video-generated")
class VideoGenerationCallback(Resource):
    @avatar_namespace.response(200, "Successfully processed video callback")
    @avatar_namespace.response(400, "Bad Request")
    @avatar_namespace.response(500, "Internal Server Error")
    def post(self):
        """
        Receive callback when a video has been generated
        """

        try:
            data = avatar_namespace.payload

            # Process the callback data
            video_id = data["video_id"]
            status = data["status"]
            download_url = data.get("download_url")
            status_details = data.get("status_details")

            if status == "ready":
                video_url = download_url
                print(
                    f"Video {video_id} has been successfully generated. URL: {video_url}"
                )
                # Add your logic here to handle successful video generation
            elif status == "error":
                error_message = status_details
                print(f"Error generating video {video_id}: {error_message}")
                # Add your logic here to handle video generation errors

            return {"message": "Callback received successfully"}, 200
        except ValueError as ve:
            return {"error": str(ve)}, 400
        except Exception as e:
            print(f"Unexpected error in video callback: {str(e)}")
            return {"error": "Internal Server Error"}, 500
