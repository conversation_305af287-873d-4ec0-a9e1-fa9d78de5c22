from flask import g, request
from flask_restx import Resource, fields
from werkzeug.exceptions import BadRequest

from .namespace import usage_namespace
from .schema import error_response_model

from app.controller.auth import login_required
from app.service.usage import UsageService
from app.repositories.user_repository import AuthRepository
from app.repositories.usage_repository import UsageRepository

usage_service = UsageService()


class UsageModels:
    """Centralized model definitions for usage-related resources"""

    usage_graph_model = usage_namespace.model(
        "UsageGraph",
        {
            "date": fields.String(required=True, description="Date"),
            "daily_usage": fields.Float(required=True, description="Daily Usage"),
            "total_video_minutes": fields.Integer(
                required=True, description="Total Video Minutes Used"
            ),
            "credits_used": fields.Integer(required=True, description="Credits Used"),
            "seconds_used": fields.Integer(required=True, description="Minutes Used"),
        },
    )

    usage_model = usage_namespace.model(
        "Usage",
        {
            "id": fields.String(readonly=True, description="Usage ID"),
            "user_id": fields.String(required=True, description="User ID"),
            "credits_used": fields.Integer(required=True, description="Credits Used"),
            "seconds_used": fields.Integer(required=True, description="Minutes Used"),
            "created_at": fields.DateTime(
                required=True, description="Created Timestamp"
            ),
            "updated_at": fields.DateTime(
                required=True, description="Updated Timestamp"
            ),
        },
    )

    daily_usage_time_series_model = usage_namespace.model(
        "DailyUsageTimeSeries",
        {
            "date": fields.DateTime(required=True, description="Date"),
            "credits_used": fields.Integer(required=True, description="Credits Used"),
            "seconds_used": fields.Integer(required=True, description="Minutes Used"),
        },
    )

    usage_time_series_request_model = usage_namespace.model(
        "UsageTimeSeriesRequest",
        {
            "user_id": fields.String(required=True, description="User ID"),
            "start_date": fields.DateTime(required=True, description="Start Date"),
            "end_date": fields.DateTime(required=True, description="End Date"),
        },
    )


class UsageResources:
    """Centralized usage-related resources with improved error handling"""

    def __init__(self):

        self.auth_repository = AuthRepository()
        self.usage_repository = UsageRepository()

    def validate_user_authenticated(self) -> str:
        """Validate and retrieve authenticated user ID"""
        try:
            return getattr(g, "user_id")
        except AttributeError:
            usage_namespace.abort(401, "Authentication required")

    @usage_namespace.route("/weekly")
    class UsageGraph(Resource):
        @usage_namespace.doc(security="Bearer Auth")
        @usage_namespace.marshal_list_with(UsageModels.usage_graph_model)
        @usage_namespace.response(401, "Unauthorized", model=error_response_model)
        @usage_namespace.response(
            500, "Internal Server Error", model=error_response_model
        )
        @login_required
        def get(self):
            """Retrieve weekly usage graphs for the authenticated user"""
            try:
                user_id = UsageResources().validate_user_authenticated()
                weekly_graphs = self.usage_repository.get_weekly_usage_graphs(user_id)
                return weekly_graphs
            except Exception as e:
                usage_namespace.abort(500, f"Error retrieving usage graphs: {str(e)}")

    @usage_namespace.route("/usage")
    class UsageList(Resource):
        @usage_namespace.marshal_list_with(UsageModels.usage_model)
        def get(self):
            """List all usages"""
            return self.usage_repository.get_usages()

    @usage_namespace.route("/usage/<string:id>")
    @usage_namespace.response(404, "Usage not found")
    @usage_namespace.param("id", "The usage identifier")
    class Usage(Resource):
        @usage_namespace.marshal_with(UsageModels.usage_model)
        def get(self, id: str):
            """Fetch a specific usage record"""
            usage = self.usage_repository.get_usage_by_id(id)
            if not usage:
                usage_namespace.abort(404, f"Usage {id} doesn't exist")
            return usage

    @usage_namespace.route("/usage/time_series")
    class UsageTimeSeries(Resource):
        @usage_namespace.expect(UsageModels.usage_time_series_request_model)
        @usage_namespace.marshal_list_with(UsageModels.daily_usage_time_series_model)
        def get(self):
            """Get daily usage time series"""
            try:
                args = usage_namespace.payload
                user_id = args["user_id"]
                start_date = args["start_date"]
                end_date = args["end_date"]

                # Additional input validation
                if start_date > end_date:
                    raise BadRequest("Start date must be before or equal to end date")

                time_series = self.usage_repository.get_daily_usage_time_series(
                    user_id, start_date, end_date
                )
                return time_series
            except BadRequest as e:
                usage_namespace.abort(400, str(e))

    @usage_namespace.route("/usage-stats")
    class TokenStatsResource(Resource):
        @usage_namespace.doc(security="Bearer Auth")
        @usage_namespace.doc(
            params={
                "start_year": "Start Year",
                "end_year": "End Year",
                "month": "Month",
                "page": "Page Number",
                "week": "Week Number",
            }
        )
        @usage_namespace.response(
            401,
            "Unauthorized access. Authentication token required.",
            model=error_response_model,
        )
        @usage_namespace.response(
            500,
            "Internal server error occurred. Please try again later.",
            model=error_response_model,
        )
        @login_required
        def get(self):
            try:
                # Use request directly instead of usage_namespace.request
                start_year = request.args.get("start_year")
                end_year = request.args.get("end_year")
                month = request.args.get("month")
                page = request.args.get("page", default=1, type=int)
                week = request.args.get("week")

                user_id = getattr(g, "user_id")

                # Create a dictionary of arguments
                args = {
                    "start_year": start_year,
                    "end_year": end_year,
                    "month": month,
                    "page": page,
                    "week": week,
                }

                return usage_service.get_usages(user_id, args)

            except Exception as e:
                return {"success": False, "message": str(e)}, 400

    @usage_namespace.route("/total-counts")
    class TotalTokenCountsResource(Resource):
        @usage_namespace.doc(security="Bearer Auth")
        @usage_namespace.response(401, "Unauthorized", model=error_response_model)
        @usage_namespace.response(
            500, "Internal Server Error", model=error_response_model
        )
        @login_required
        def get(self):
            """Retrieve total usage counts"""
            try:
                user_id = UsageResources().validate_user_authenticated()
                return usage_service.get_total_counts(user_id)
            except Exception as e:
                return {"success": False, "message": str(e)}, 400
