from .namespace import subscription_webhook_namespace

from flask import request

from flask_restx import Resource, fields
from werkzeug.exceptions import BadRequest

from app.service.subscription.payment import PaymentService

payment_service = PaymentService()

# Define the model using Flask-RESTX fields
stripe_webhook_model = subscription_webhook_namespace.model(
    "StripeWebhook",
    {"STRIPE_SIGNATURE": fields.String(required=True, location="headers")},
)


@subscription_webhook_namespace.route("/processSubscription")
class StripeWebhook(Resource):
    @subscription_webhook_namespace.doc(
        description="Handle Stripe webhook events",
    )
    @subscription_webhook_namespace.expect(stripe_webhook_model)
    def post(self):
        """
        Handle the stripe webhook events
        """
        try:
            signature = request.headers.get("STRIPE_SIGNATURE")
            if not signature:
                raise BadRequest("Missing STRIPE_SIGNATURE header")
            raw_body = request.data
            payment_service.process_web_hook(raw_body, signature)
            return {"status": True}, 200
        except BadRequest as error:
            return {"message": str(error)}, 400
        except Exception as error:
            print("Error processing webhook", str(error))
            return {"message": "An unexpected error occurred"}, 500
