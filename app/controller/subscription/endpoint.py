from flask import g, request

from flask_restx import Resource
from werkzeug.exceptions import NotFound
from app.exceptions import handle_errors

from .namespace import subscription_namespace

from .schema import subscription_model, error_response_model

from app.controller.auth import login_required

from app.service.subscription.payment import PaymentService

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

payment_service = PaymentService()


@subscription_namespace.route("/activate_free_plan")
class FreeSubscription(Resource):
    @subscription_namespace.doc(security="Bearer Auth")
    @subscription_namespace.doc("free_subscription")
    @subscription_namespace.response(200, "Subscription created successfully")
    @subscription_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @subscription_namespace.response(400, "Bad request", model=error_response_model)
    @subscription_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @handle_errors
    @login_required
    def post(self):
        """Create a new subscription using Stripe Checkout"""

        user_id = getattr(g, "user_id")

        result = payment_service.activate_free_plan(user_id)

        if result:
            return {
                "message": "plan successfully purchased",
                "code": 201,
            }, 201


@subscription_namespace.route("/checkout-purchase")
class SubscriptionPurchase(Resource):
    @subscription_namespace.doc(security="Bearer Auth")
    @subscription_namespace.doc("create_subscription")
    @subscription_namespace.expect(subscription_model)
    @subscription_namespace.response(200, "Subscription created successfully")
    @subscription_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @subscription_namespace.response(400, "Bad request", model=error_response_model)
    @subscription_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def post(self):
        """Create a new subscription using Stripe Checkout"""

        user_id = getattr(g, "user_id")
        data = request.json
        result = payment_service.purchase_subscription(user_id, data["plan_id"])
        return {
            "message": "Checkout successfully created",
            "data": result,
            "code": 201,
        }, 201


@subscription_namespace.route("/upgrade")
class SubscriptionUpdate(Resource):
    @subscription_namespace.doc(security="Bearer Auth")
    @subscription_namespace.doc("update_subscription")
    @subscription_namespace.expect(subscription_model)
    @subscription_namespace.response(200, "Subscription updated successfully")
    @subscription_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @subscription_namespace.response(400, "Bad request", model=error_response_model)
    @subscription_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def put(self):
        """Update an existing subscription"""

        user_id = getattr(g, "user_id")
        data = request.json
        result = payment_service.update_subscription(user_id, data["plan_id"])
        return result, 200


@subscription_namespace.route("/cancel")
class SubscriptionCancel(Resource):
    @subscription_namespace.doc(security="Bearer Auth")
    @subscription_namespace.doc("cancel_subscription")
    @subscription_namespace.response(200, "Subscription cancelled successfully")
    @subscription_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @subscription_namespace.response(400, "Bad request", model=error_response_model)
    @subscription_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def put(self):
        """Cancel an existing subscription"""

        user_id = getattr(g, "user_id")
        result = payment_service.cancel_subscription(user_id)
        return result, 200


@subscription_namespace.route("/user-subscription")
class UserSubscription(Resource):
    @subscription_namespace.doc(security="Bearer Auth")
    @subscription_namespace.doc("user_subscription")
    @subscription_namespace.response(200, "Subscription fetch successfully")
    @subscription_namespace.response(
        401, "Unauthorized access", model=error_response_model
    )
    @subscription_namespace.response(400, "Bad request", model=error_response_model)
    @subscription_namespace.response(
        500, "Internal server error", model=error_response_model
    )
    @login_required
    @handle_errors
    def get(self):
        """Cancel an existing subscription"""

        user_id = getattr(g, "user_id")
        user_subscription = payment_service.get_user_subscription(user_id)

        if not user_subscription:
            raise NotFound("user_subscription not found")

        return {
            "message": "subscription fetch successfully",
            "data": user_subscription,
            "code": 200,
        }, 200
