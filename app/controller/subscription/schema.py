from flask_restx import fields, reqparse, inputs
from app.controller.subscription.namespace import (
    subscription_namespace,
    subscription_plan_namespace,
)

plan_model = subscription_namespace.model(
    "Plan", {"plan_id": fields.String(required=True, description="Stripe Plan ID")}
)

usage_model = subscription_namespace.model(
    "Usage",
    {"tokens_used": fields.Integer(required=True, description="Number of tokens used")},
)

error_response_model = subscription_namespace.model(
    "AdminErrorResponse",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)


response_model = subscription_namespace.model(
    "Response",
    {
        "message": fields.String(example="<error message:string>"),
        "code": fields.Integer(example="<error status code:integer>"),
    },
)


subscription_model = subscription_namespace.model(
    "Subscription",
    {
        "plan_id": fields.String(required=True, description="The plan identifier"),
    },
)

plan_model = subscription_plan_namespace.model(
    "Plan",
    {
        "_id": fields.String(readonly=True, description="The plan identifier"),
        "name": fields.String(required=True, description="The name of the plan"),
        "description": fields.String(
            required=True, description="The description of the plan"
        ),
        "price": fields.Float(required=True, description="The price of the plan"),
        "currency": fields.String(
            required=True, description="The currency of the plan", default="usd"
        ),
        "interval": fields.String(
            required=True,
            description="The billing interval (e.g., month, year)",
            default="month",
        ),
        "interval_count": fields.Integer(
            required=True,
            description="The number of intervals between billings",
            default=1,
        ),
        "active": fields.Boolean(
            readonly=True, description="Whether the plan is active"
        ),
        "image": fields.String(required=False, description="URL of the plan image"),
        "stripe_product_id": fields.String(
            readonly=True, description="The Stripe product ID"
        ),
        "stripe_price_id": fields.String(
            readonly=True, description="The Stripe price ID"
        ),
        "video_time": fields.Integer(
            required=True,
            description="The number of video time for billings in sec",
        ),
        "max_video_duration": fields.Integer(
            required=True,
            description="The max video duration in sec of billings",
        ),
        "features": fields.List(
            fields.String,
            required=True,
            description="Features of the plan",
        ),
        "upto_video_quality": fields.String(
            required=True,
            description="video quality in pixels like 480p,720p,1080p",
        ),
    },
)


update_plan_model = subscription_plan_namespace.model(
    "UpdatePlan",
    {
        "name": fields.String(required=False, description="The name of the plan"),
        "description": fields.String(
            required=False, description="The description of the plan"
        ),
        "price": fields.Float(required=False, description="The price of the plan"),
        "active": fields.Boolean(
            required=False, description="Whether the plan is active"
        ),
        "image": fields.String(required=False, description="URL of the plan image"),
        "video_time": fields.Integer(
            required=False,
            description="The number of video time for billings in sec",
        ),
        "max_video_duration": fields.Integer(
            required=False,
            description="The max video duration in sec of billings",
        ),
        "features": fields.List(
            fields.String,
            required=False,
            description="Features of the plan",
        ),
        "upto_video_quality": fields.String(
            required=False,
            description="video quality in pixels like 480p,720p,1080p",
        ),
    },
)

# Input model for voice list query parameters
plan_filter = reqparse.RequestParser()
plan_filter.add_argument(
    "interval",
    type=str,
    required=True,
    choices=["day", "month", "year"],
    help="Filter by interval",
    default="month",
)
plan_filter.add_argument(
    "active", type=inputs.boolean, required=False, help="Filter by active status"
)
