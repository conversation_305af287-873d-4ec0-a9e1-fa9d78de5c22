from flask import g, request, abort
from flask_restx import Resource


from .namespace import subscription_plan_namespace

from .schema import (
    plan_model,
    error_response_model,
    plan_filter,
    update_plan_model,
    response_model,
)

from app.controller.auth import is_admin, login_required


from app.service.subscription.plan import PlanService
from app.repositories.user_repository import AuthRepository

plan_service = PlanService()
auth_repository = AuthRepository()


@subscription_plan_namespace.route("/plan")
class PlanList(Resource):
    @subscription_plan_namespace.doc(security="Bearer Auth")
    @subscription_plan_namespace.expect(plan_filter)
    @subscription_plan_namespace.doc("list_plans")
    @subscription_plan_namespace.response(200, "successfully fetched", plan_model)
    @subscription_plan_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=error_response_model,
    )
    @subscription_plan_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=error_response_model,
    )
    @login_required
    def get(self):
        """List all subscription plans"""

        try:
            args = plan_filter.parse_args()
            active = args["active"]
            interval = args["interval"]

            user_id = getattr(g, "user_id")

            user_data = auth_repository.find_user_by_id(user_id)

            role = user_data["role"]

            plans = plan_service.get_subscription_plans(role, interval, active)

            return {
                "message": "Plans successfully fetched",
                "data": plans,
                "code": 200,
            }, 200
        except abort as error:
            raise error
        except Exception as e:
            subscription_plan_namespace.abort(
                500, f"An unexpected error occurred: {str(e)}"
            )

    @subscription_plan_namespace.doc(security="Bearer Auth")
    @subscription_plan_namespace.doc("create_plan")
    @subscription_plan_namespace.expect(plan_model)
    @subscription_plan_namespace.response(201, "successfully created", plan_model)
    @subscription_plan_namespace.response(
        400, "Bad Request - Invalid input data", model=error_response_model
    )
    @subscription_plan_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=error_response_model,
    )
    @subscription_plan_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=error_response_model,
    )
    @is_admin
    def post(self):
        """Create a new subscription plan"""
        try:
            data = request.json
            plan = plan_service.create_subscription_plan(data)

            return {
                "message": "Plan successfully created",
                "data": plan,
                "code": 201,
            }, 201
        except abort as error:
            # Re-raise aborts (400, 500) to be handled by Flask-RESTX
            raise error
        except Exception as e:
            # Handle any unexpected errors
            subscription_plan_namespace.abort(
                500, f"An unexpected error occurred: {str(e)}"
            )


@subscription_plan_namespace.route("/plan/<string:id>")
@subscription_plan_namespace.param("id", "The subscription plan identifier")
@subscription_plan_namespace.response(404, "Subscription plan not found")
class Plan(Resource):

    @subscription_plan_namespace.doc(security="Bearer Auth")
    @subscription_plan_namespace.doc("get_plan")
    @subscription_plan_namespace.response(200, "successfully fetched", plan_model)
    @subscription_plan_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=error_response_model,
    )
    @subscription_plan_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=error_response_model,
    )
    @login_required
    def get(self, id):
        """Fetch a subscription plan given its identifier"""
        try:
            plan = plan_service.get_subscription_plan(id)
            return {
                "message": "Plan successfully fetched",
                "data": plan,
                "code": 200,
            }, 200
        except abort as error:
            raise error
        except Exception as e:
            subscription_plan_namespace.abort(
                500, f"An unexpected error occurred: {str(e)}"
            )

    @subscription_plan_namespace.doc(security="Bearer Auth")
    @subscription_plan_namespace.doc("update_plan")
    @subscription_plan_namespace.expect(update_plan_model)
    @subscription_plan_namespace.response(201, "successfully updated", plan_model)
    @subscription_plan_namespace.response(
        400, "Bad Request - Invalid input data", model=error_response_model
    )
    @subscription_plan_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=error_response_model,
    )
    @subscription_plan_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=error_response_model,
    )
    @is_admin
    def put(self, id):
        """Update a subscription plan"""
        try:
            data = request.json
            updated_plan = plan_service.update_subscription_plan(id, data)
            return {
                "message": "Plan successfully Updated",
                "data": updated_plan,
                "code": 200,
            }, 200
        except abort as error:
            raise error
        except Exception as e:
            subscription_plan_namespace.abort(
                500, f"An unexpected error occurred: {str(e)}"
            )

    @subscription_plan_namespace.doc(security="Bearer Auth")
    @subscription_plan_namespace.doc("delete_plan")
    @subscription_plan_namespace.response(
        204, "Subscription plan deleted", response_model
    )
    @subscription_plan_namespace.response(
        401,
        "Unauthorized access. Authentication token required.",
        model=error_response_model,
    )
    @subscription_plan_namespace.response(
        500,
        "Internal server error occurred. Please try again later.",
        model=error_response_model,
    )
    @is_admin
    def delete(self, id):
        """Delete a subscription plan"""
        try:
            plan_service.delete_subscription_plan(id)
            return {
                "message": "Plan successfully deleted",
                "code": 204,
            }, 204
        except abort as error:
            raise error
        except Exception as e:
            subscription_plan_namespace.abort(
                500, f"An unexpected error occurred: {str(e)}"
            )
