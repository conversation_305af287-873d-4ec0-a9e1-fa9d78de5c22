"""Remove alias field from applications table

Revision ID: 004
Revises: 003
Create Date: 2024-12-19 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    """Remove alias field from applications table."""
    # Drop unique constraint on alias field
    try:
        op.drop_constraint('uq_applications_alias', 'applications', type_='unique')
    except Exception:
        # Constraint might not exist
        pass
    
    # Drop alias column
    try:
        op.drop_column('applications', 'alias')
    except Exception:
        # Column might not exist
        pass


def downgrade():
    """Add alias field back to applications table."""
    # Add alias column to applications table
    op.add_column('applications', sa.Column('alias', sa.String(), nullable=True))
    
    # Add unique constraint on alias field
    op.create_unique_constraint('uq_applications_alias', 'applications', ['alias'])
