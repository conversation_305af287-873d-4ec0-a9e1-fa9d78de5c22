"""Add alias field to applications table

Revision ID: 003
Revises: 002
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    """Add alias field to applications table."""
    # Add alias column to applications table
    op.add_column('applications', sa.Column('alias', sa.String(), nullable=True))
    
    # Add unique constraint on alias field
    op.create_unique_constraint('uq_applications_alias', 'applications', ['alias'])
    
    # Add api_keys and is_deleted fields if they don't exist
    # (These might have been added in previous migrations)
    try:
        op.add_column('applications', sa.Column('api_keys', sa.ARRAY(sa.String()), nullable=True))
    except Exception:
        # Column might already exist
        pass
    
    try:
        op.add_column('applications', sa.Column('is_deleted', sa.<PERSON>(), nullable=True, default=False))
    except Exception:
        # Column might already exist
        pass


def downgrade():
    """Remove alias field from applications table."""
    # Drop unique constraint
    op.drop_constraint('uq_applications_alias', 'applications', type_='unique')
    
    # Drop alias column
    op.drop_column('applications', 'alias')
    
    # Note: We don't remove api_keys and is_deleted fields in downgrade
    # as they might be needed by other parts of the system
