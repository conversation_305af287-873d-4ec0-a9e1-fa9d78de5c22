"""Remove API Key functionality and add Application Images

Revision ID: 003
Revises: 002
Create Date: 2025-01-27 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import ARRAY

# revision identifiers, used by Alembic.
revision = "003"
down_revision = "002"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create application_images table
    op.create_table(
        "application_images",
        sa.Column("id", sa.String(), primary_key=True),
        sa.Column("application_id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("image_name", sa.String(), nullable=False),
        sa.Column("image_type", sa.String(), nullable=False),
        sa.Column("image_size", sa.Integer(), nullable=False),
        sa.Column("image_path", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
    )

    # Add indexes for application_images
    op.create_index(
        "idx_application_images_application_id", "application_images", ["application_id"]
    )
    op.create_index("idx_application_images_user_id", "application_images", ["user_id"])

    # Add foreign key constraint for application_id
    op.create_foreign_key(
        "fk_application_images_application_id",
        "application_images",
        "applications",
        ["application_id"],
        ["id"],
    )

    # Update applications table - add new fields and remove api_keys_count
    op.add_column("applications", sa.Column("workflow_ids", ARRAY(sa.String()), nullable=True))
    op.add_column("applications", sa.Column("agent_ids", ARRAY(sa.String()), nullable=True))
    op.add_column("applications", sa.Column("image_ids", ARRAY(sa.String()), nullable=True))

    # Remove api_keys_count field from applications
    op.drop_column("applications", "api_keys_count")

    # Remove foreign key constraints that reference api_keys table
    op.drop_constraint("fk_analytics_events_api_key_id", "analytics_events", type_="foreignkey")
    op.drop_constraint("fk_usage_events_api_key_id", "usage_events", type_="foreignkey")

    # Remove indexes related to api_key_id
    op.drop_index("idx_analytics_events_api_key_id", "analytics_events")
    op.drop_index("idx_usage_events_api_key_id", "usage_events")

    # Remove api_key_id columns from tables
    op.drop_column("analytics_events", "api_key_id")
    op.drop_column("usage_events", "api_key_id")

    # Remove api_key_usage_count from user_activities
    op.drop_column("user_activities", "api_key_usage_count")

    # Drop the api_keys table entirely
    op.drop_table("api_keys")


def downgrade() -> None:
    # Recreate api_keys table
    op.create_table(
        "api_keys",
        sa.Column("id", sa.String(), primary_key=True),
        sa.Column("application_id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("public_key", sa.String(), nullable=False, unique=True),
        sa.Column("private_key_hash", sa.String(), nullable=False),
        sa.Column("scopes", ARRAY(sa.String()), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.Column("last_used_at", sa.DateTime(), nullable=True),
        sa.Column("usage_count", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
    )

    # Add back api_key_usage_count to user_activities
    op.add_column(
        "user_activities",
        sa.Column("api_key_usage_count", sa.Integer(), nullable=True, server_default="0"),
    )

    # Add back api_key_id columns
    op.add_column("analytics_events", sa.Column("api_key_id", sa.String(), nullable=True))
    op.add_column("usage_events", sa.Column("api_key_id", sa.String(), nullable=True))

    # Recreate indexes for api_key_id
    op.create_index("idx_analytics_events_api_key_id", "analytics_events", ["api_key_id"])
    op.create_index("idx_usage_events_api_key_id", "usage_events", ["api_key_id"])

    # Recreate foreign key constraints
    op.create_foreign_key(
        "fk_analytics_events_api_key_id", "analytics_events", "api_keys", ["api_key_id"], ["id"]
    )
    op.create_foreign_key(
        "fk_usage_events_api_key_id", "usage_events", "api_keys", ["api_key_id"], ["id"]
    )

    # Add back api_keys_count to applications
    op.add_column(
        "applications",
        sa.Column("api_keys_count", sa.Integer(), nullable=False, server_default="0"),
    )

    # Remove new fields from applications table
    op.drop_column("applications", "image_ids")
    op.drop_column("applications", "agent_ids")
    op.drop_column("applications", "workflow_ids")

    # Remove foreign key constraint for application_images
    op.drop_constraint(
        "fk_application_images_application_id", "application_images", type_="foreignkey"
    )

    # Remove indexes for application_images
    op.drop_index("idx_application_images_user_id", "application_images")
    op.drop_index("idx_application_images_application_id", "application_images")

    # Drop application_images table
    op.drop_table("application_images")
