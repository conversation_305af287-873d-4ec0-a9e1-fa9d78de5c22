"""Update analytics schema - add workflow_id, agent_id, mcp_id, api_key_id and remove tags/environment

Revision ID: 002
Revises: 001
Create Date: 2025-05-29 13:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import ARRAY

# revision identifiers, used by Alembic.
revision = "002"
down_revision = "001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update analytics_events table
    op.add_column('analytics_events', sa.Column('entity_type', sa.String(), nullable=False, server_default='unknown'))
    op.add_column('analytics_events', sa.Column('workflow_id', sa.String(), nullable=True))
    op.add_column('analytics_events', sa.Column('agent_id', sa.String(), nullable=True))
    op.add_column('analytics_events', sa.Column('mcp_id', sa.String(), nullable=True))
    op.add_column('analytics_events', sa.Column('api_key_id', sa.String(), nullable=True))
    
    # Update service_metrics table
    op.add_column('service_metrics', sa.Column('entity_type', sa.String(), nullable=False, server_default='unknown'))
    op.add_column('service_metrics', sa.Column('workflow_id', sa.String(), nullable=True))
    op.add_column('service_metrics', sa.Column('agent_id', sa.String(), nullable=True))
    op.add_column('service_metrics', sa.Column('mcp_id', sa.String(), nullable=True))
    op.add_column('service_metrics', sa.Column('total_execution_time_ms', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('service_metrics', sa.Column('average_execution_time_ms', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('service_metrics', sa.Column('success_count', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('service_metrics', sa.Column('failure_count', sa.Integer(), nullable=True, server_default='0'))
    
    # Update user_activities table
    op.add_column('user_activities', sa.Column('api_call_count', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('user_activities', sa.Column('api_key_usage_count', sa.Integer(), nullable=True, server_default='0'))
    
    # Update usage_events table
    op.add_column('usage_events', sa.Column('workflow_id', sa.String(), nullable=True))
    op.add_column('usage_events', sa.Column('agent_id', sa.String(), nullable=True))
    op.add_column('usage_events', sa.Column('mcp_id', sa.String(), nullable=True))
    op.add_column('usage_events', sa.Column('api_key_id', sa.String(), nullable=True))
    op.add_column('usage_events', sa.Column('execution_time_ms', sa.Integer(), nullable=True))
    op.add_column('usage_events', sa.Column('success', sa.Boolean(), nullable=True, server_default='true'))
    op.add_column('usage_events', sa.Column('error_message', sa.String(), nullable=True))
    
    # Update activation_events table
    op.add_column('activation_events', sa.Column('workflow_id', sa.String(), nullable=True))
    op.add_column('activation_events', sa.Column('agent_id', sa.String(), nullable=True))
    op.add_column('activation_events', sa.Column('mcp_id', sa.String(), nullable=True))
    
    # Remove tags and environment from applications table
    op.drop_column('applications', 'tags')
    op.drop_column('applications', 'environment')
    
    # Add indexes for new reference columns
    op.create_index('idx_analytics_events_workflow_id', 'analytics_events', ['workflow_id'])
    op.create_index('idx_analytics_events_agent_id', 'analytics_events', ['agent_id'])
    op.create_index('idx_analytics_events_mcp_id', 'analytics_events', ['mcp_id'])
    op.create_index('idx_analytics_events_api_key_id', 'analytics_events', ['api_key_id'])
    
    op.create_index('idx_service_metrics_workflow_id', 'service_metrics', ['workflow_id'])
    op.create_index('idx_service_metrics_agent_id', 'service_metrics', ['agent_id'])
    op.create_index('idx_service_metrics_mcp_id', 'service_metrics', ['mcp_id'])
    
    op.create_index('idx_usage_events_workflow_id', 'usage_events', ['workflow_id'])
    op.create_index('idx_usage_events_agent_id', 'usage_events', ['agent_id'])
    op.create_index('idx_usage_events_mcp_id', 'usage_events', ['mcp_id'])
    op.create_index('idx_usage_events_api_key_id', 'usage_events', ['api_key_id'])
    
    op.create_index('idx_activation_events_workflow_id', 'activation_events', ['workflow_id'])
    op.create_index('idx_activation_events_agent_id', 'activation_events', ['agent_id'])
    op.create_index('idx_activation_events_mcp_id', 'activation_events', ['mcp_id'])
    
    # Add foreign key constraint for api_key_id
    op.create_foreign_key('fk_analytics_events_api_key_id', 'analytics_events', 'api_keys', ['api_key_id'], ['id'])
    op.create_foreign_key('fk_usage_events_api_key_id', 'usage_events', 'api_keys', ['api_key_id'], ['id'])


def downgrade() -> None:
    # Remove foreign key constraints
    op.drop_constraint('fk_usage_events_api_key_id', 'usage_events', type_='foreignkey')
    op.drop_constraint('fk_analytics_events_api_key_id', 'analytics_events', type_='foreignkey')
    
    # Remove indexes
    op.drop_index('idx_activation_events_mcp_id', 'activation_events')
    op.drop_index('idx_activation_events_agent_id', 'activation_events')
    op.drop_index('idx_activation_events_workflow_id', 'activation_events')
    
    op.drop_index('idx_usage_events_api_key_id', 'usage_events')
    op.drop_index('idx_usage_events_mcp_id', 'usage_events')
    op.drop_index('idx_usage_events_agent_id', 'usage_events')
    op.drop_index('idx_usage_events_workflow_id', 'usage_events')
    
    op.drop_index('idx_service_metrics_mcp_id', 'service_metrics')
    op.drop_index('idx_service_metrics_agent_id', 'service_metrics')
    op.drop_index('idx_service_metrics_workflow_id', 'service_metrics')
    
    op.drop_index('idx_analytics_events_api_key_id', 'analytics_events')
    op.drop_index('idx_analytics_events_mcp_id', 'analytics_events')
    op.drop_index('idx_analytics_events_agent_id', 'analytics_events')
    op.drop_index('idx_analytics_events_workflow_id', 'analytics_events')
    
    # Add back tags and environment to applications table
    op.add_column('applications', sa.Column('environment', sa.String(), nullable=True))
    op.add_column('applications', sa.Column('tags', ARRAY(sa.String()), nullable=True))
    
    # Remove new columns from activation_events table
    op.drop_column('activation_events', 'mcp_id')
    op.drop_column('activation_events', 'agent_id')
    op.drop_column('activation_events', 'workflow_id')
    
    # Remove new columns from usage_events table
    op.drop_column('usage_events', 'error_message')
    op.drop_column('usage_events', 'success')
    op.drop_column('usage_events', 'execution_time_ms')
    op.drop_column('usage_events', 'api_key_id')
    op.drop_column('usage_events', 'mcp_id')
    op.drop_column('usage_events', 'agent_id')
    op.drop_column('usage_events', 'workflow_id')
    
    # Remove new columns from user_activities table
    op.drop_column('user_activities', 'api_key_usage_count')
    op.drop_column('user_activities', 'api_call_count')
    
    # Remove new columns from service_metrics table
    op.drop_column('service_metrics', 'failure_count')
    op.drop_column('service_metrics', 'success_count')
    op.drop_column('service_metrics', 'average_execution_time_ms')
    op.drop_column('service_metrics', 'total_execution_time_ms')
    op.drop_column('service_metrics', 'mcp_id')
    op.drop_column('service_metrics', 'agent_id')
    op.drop_column('service_metrics', 'workflow_id')
    op.drop_column('service_metrics', 'entity_type')
    
    # Remove new columns from analytics_events table
    op.drop_column('analytics_events', 'api_key_id')
    op.drop_column('analytics_events', 'mcp_id')
    op.drop_column('analytics_events', 'agent_id')
    op.drop_column('analytics_events', 'workflow_id')
    op.drop_column('analytics_events', 'entity_type')