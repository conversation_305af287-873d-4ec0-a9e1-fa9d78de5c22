"""
Create dashboard analytics tables

Revision ID: 004
Revises: 003
Create Date: 2024-01-01 12:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = "004"
down_revision = "003"
branch_labels = None
depends_on = None


def upgrade():
    """Create dashboard analytics tables"""

    # Create enums
    request_type_enum = postgresql.ENUM(
        "api_request",
        "workflow_exec",
        "auth_event",
        "agent_invoke",
        "mcp_request",
        name="requesttype",
    )
    request_type_enum.create(op.get_bind())

    request_status_enum = postgresql.ENUM(
        "success", "error", "pending", "timeout", name="requeststatus"
    )
    request_status_enum.create(op.get_bind())

    credit_category_enum = postgresql.ENUM(
        "agents", "workflows", "custom_mcps", "app_credits", "other", name="creditcategory"
    )
    credit_category_enum.create(op.get_bind())

    # Create dashboard_metrics table
    op.create_table(
        "dashboard_metrics",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("active_agents", sa.Integer(), default=0),
        sa.Column("credit_usage", sa.Float(), default=0.0),
        sa.Column("agent_requests", sa.Integer(), default=0),
        sa.Column("workflow_requests", sa.Integer(), default=0),
        sa.Column("custom_mcps", sa.Integer(), default=0),
        sa.Column("credit_usage_change", sa.Float(), default=0.0),
        sa.Column("agent_requests_change_pct", sa.Float(), default=0.0),
        sa.Column("workflow_requests_change_pct", sa.Float(), default=0.0),
        sa.Column("custom_mcps_change", sa.Integer(), default=0),
        sa.Column("total_cost", sa.Float(), default=0.0),
        sa.Column("app_credits_used", sa.Integer(), default=0),
        sa.Column("app_credits_cost", sa.Float(), default=0.0),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()
        ),
    )

    # Create indexes for dashboard_metrics
    op.create_index("idx_dashboard_metrics_date_user", "dashboard_metrics", ["date", "user_id"])
    op.create_index("idx_dashboard_metrics_date", "dashboard_metrics", ["date"])

    # Create credit_usage_breakdown table
    op.create_table(
        "credit_usage_breakdown",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("category", credit_category_enum, nullable=False),
        sa.Column("credits_used", sa.Float(), default=0.0),
        sa.Column("cost", sa.Float(), default=0.0),
        sa.Column("request_count", sa.Integer(), default=0),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()
        ),
    )

    # Create indexes for credit_usage_breakdown
    op.create_index(
        "idx_credit_breakdown_date_category", "credit_usage_breakdown", ["date", "category"]
    )
    op.create_index("idx_credit_breakdown_user_date", "credit_usage_breakdown", ["user_id", "date"])

    # Create api_request_events table
    op.create_table(
        "api_request_events",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("request_type", request_type_enum, nullable=False),
        sa.Column("endpoint", sa.String(), nullable=False),
        sa.Column("method", sa.String(), nullable=True),
        sa.Column("status", request_status_enum, nullable=False),
        sa.Column("timestamp", sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column("duration_ms", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("user_email", sa.String(), nullable=True),
        sa.Column("ip_address", sa.String(), nullable=True),
        sa.Column("user_agent", sa.Text(), nullable=True),
        sa.Column("request_data", postgresql.JSONB(), nullable=True),
        sa.Column("response_data", postgresql.JSONB(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("agent_id", sa.String(), nullable=True),
        sa.Column("workflow_id", sa.String(), nullable=True),
        sa.Column("application_id", sa.String(), nullable=True),
        sa.Column("credits_used", sa.Float(), default=0.0),
        sa.Column("cost", sa.Float(), default=0.0),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
    )

    # Create indexes for api_request_events
    op.create_index(
        "idx_api_events_timestamp_status", "api_request_events", ["timestamp", "status"]
    )
    op.create_index("idx_api_events_user_timestamp", "api_request_events", ["user_id", "timestamp"])
    op.create_index(
        "idx_api_events_type_timestamp", "api_request_events", ["request_type", "timestamp"]
    )
    op.create_index("idx_api_events_endpoint", "api_request_events", ["endpoint"])
    op.create_index("idx_api_events_agent_id", "api_request_events", ["agent_id"])
    op.create_index("idx_api_events_workflow_id", "api_request_events", ["workflow_id"])
    op.create_index("idx_api_events_application_id", "api_request_events", ["application_id"])

    # Create agent_performance_metrics table
    op.create_table(
        "agent_performance_metrics",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("hour", sa.Integer(), nullable=True),
        sa.Column("agent_id", sa.String(), nullable=False),
        sa.Column("agent_name", sa.String(), nullable=True),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("total_requests", sa.Integer(), default=0),
        sa.Column("successful_requests", sa.Integer(), default=0),
        sa.Column("failed_requests", sa.Integer(), default=0),
        sa.Column("avg_response_time_ms", sa.Float(), default=0.0),
        sa.Column("total_credits_used", sa.Float(), default=0.0),
        sa.Column("total_cost", sa.Float(), default=0.0),
        sa.Column("is_active", sa.Boolean(), default=True),
        sa.Column("deployment_status", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()
        ),
    )

    # Create indexes for agent_performance_metrics
    op.create_index("idx_agent_perf_date_agent", "agent_performance_metrics", ["date", "agent_id"])
    op.create_index("idx_agent_perf_user_date", "agent_performance_metrics", ["user_id", "date"])
    op.create_index("idx_agent_perf_active", "agent_performance_metrics", ["is_active"])

    # Create workflow_utilization_metrics table
    op.create_table(
        "workflow_utilization_metrics",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("hour", sa.Integer(), nullable=True),
        sa.Column("workflow_id", sa.String(), nullable=False),
        sa.Column("workflow_name", sa.String(), nullable=True),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("total_executions", sa.Integer(), default=0),
        sa.Column("successful_executions", sa.Integer(), default=0),
        sa.Column("failed_executions", sa.Integer(), default=0),
        sa.Column("avg_execution_time_ms", sa.Float(), default=0.0),
        sa.Column("completion_rate_pct", sa.Float(), default=0.0),
        sa.Column("total_credits_used", sa.Float(), default=0.0),
        sa.Column("total_cost", sa.Float(), default=0.0),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
        sa.Column(
            "updated_at", sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()
        ),
    )

    # Create indexes for workflow_utilization_metrics
    op.create_index(
        "idx_workflow_util_date_workflow", "workflow_utilization_metrics", ["date", "workflow_id"]
    )
    op.create_index(
        "idx_workflow_util_user_date", "workflow_utilization_metrics", ["user_id", "date"]
    )

    # Create app_credit_usage table
    op.create_table(
        "app_credit_usage",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column("application_id", sa.String(), nullable=True),
        sa.Column("application_name", sa.String(), nullable=True),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("credits_used", sa.Float(), default=0.0),
        sa.Column("cost", sa.Float(), default=0.0),
        sa.Column("cumulative_credits", sa.Float(), default=0.0),
        sa.Column("cumulative_cost", sa.Float(), default=0.0),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
    )

    # Create indexes for app_credit_usage
    op.create_index(
        "idx_app_credit_timestamp_app", "app_credit_usage", ["timestamp", "application_id"]
    )
    op.create_index("idx_app_credit_user_timestamp", "app_credit_usage", ["user_id", "timestamp"])

    # Create system_activity table
    op.create_table(
        "system_activity",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column("activity_type", sa.String(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("severity", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=True),
        sa.Column("user_id", sa.String(), nullable=True),
        sa.Column("customer_id", sa.String(), nullable=True),
        sa.Column("activity_metadata", postgresql.JSONB(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.func.now()),
    )

    # Create indexes for system_activity
    op.create_index(
        "idx_system_activity_timestamp_type", "system_activity", ["timestamp", "activity_type"]
    )
    op.create_index("idx_system_activity_severity", "system_activity", ["severity"])
    op.create_index("idx_system_activity_user_id", "system_activity", ["user_id"])


def downgrade():
    """Drop dashboard analytics tables"""

    # Drop tables
    op.drop_table("system_activity")
    op.drop_table("app_credit_usage")
    op.drop_table("workflow_utilization_metrics")
    op.drop_table("agent_performance_metrics")
    op.drop_table("api_request_events")
    op.drop_table("credit_usage_breakdown")
    op.drop_table("dashboard_metrics")

    # Drop enums
    op.execute("DROP TYPE IF EXISTS creditcategory")
    op.execute("DROP TYPE IF EXISTS requeststatus")
    op.execute("DROP TYPE IF EXISTS requesttype")
