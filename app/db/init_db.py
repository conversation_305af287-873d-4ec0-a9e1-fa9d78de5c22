import sys
import importlib.util

# Check if SQLAlchemy is installed
if importlib.util.find_spec("sqlalchemy") is None:
    print("Error: SQLAlchemy is not installed.")
    print("Please install it using: poetry add sqlalchemy")
    sys.exit(1)

try:
    from sqlalchemy.orm import Session
    import stripe
    from app.db.session import engine, SessionLocal
    from app.models.payment_models import Base, PaymentPlan
    from app.models.topup import TopupPlan
    from app.core.config import settings
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure all dependencies are installed: poetry install")
    sys.exit(1)

stripe.api_key = settings.STRIPE_API_KEY


def create_or_retrieve_product(product_name: str) -> stripe.Product:
    """Creates a product in Stripe or retrieves it if it already exists."""
    try:
        # Search for an active product with the given name
        products = stripe.Product.list(active=True)
        for product in products.auto_paging_iter():
            if product.name.lower() == product_name.lower():
                print(f"Product '{product_name}' already exists.")
                return product
        
        # If not found, create it
        print(f"Creating product '{product_name}' in Stripe...")
        return stripe.Product.create(name=product_name)
    except Exception as e:
        print(f"Error with Stripe product '{product_name}': {e}")
        sys.exit(1)


def create_or_retrieve_price(product: stripe.Product, amount: int, currency: str) -> stripe.Price:
    """Creates a recurring price for a product in Stripe or retrieves it if it exists."""
    try:
        # Search for a price for the given product and amount
        prices = stripe.Price.list(product=product.id, active=True)
        for price in prices.auto_paging_iter():
            if price.unit_amount == amount and price.currency == currency and price.recurring:
                print(f"Recurring price for '{product.name}' at {amount}{currency.upper()} already exists.")
                return price

        # If not found, create it
        print(f"Creating recurring price for '{product.name}' in Stripe...")
        return stripe.Price.create(
            product=product.id,
            unit_amount=amount,
            currency=currency,
            recurring={"interval": "month"},  # Monthly subscription
        )
    except Exception as e:
        print(f"Error with Stripe recurring price for '{product.name}': {e}")
        sys.exit(1)


def create_or_retrieve_onetime_price(product: stripe.Product, amount: int, currency: str) -> stripe.Price:
    """Creates a one-time price for a product in Stripe or retrieves it if it exists."""
    try:
        # Search for a one-time price for the given product and amount
        prices = stripe.Price.list(product=product.id, active=True)
        for price in prices.auto_paging_iter():
            if price.unit_amount == amount and price.currency == currency and not price.recurring:
                print(f"One-time price for '{product.name}' at {amount}{currency.upper()} already exists.")
                return price

        # If not found, create it
        print(f"Creating one-time price for '{product.name}' in Stripe...")
        return stripe.Price.create(
            product=product.id,
            unit_amount=amount,
            currency=currency,
            # No recurring parameter for one-time payments
        )
    except Exception as e:
        print(f"Error with Stripe one-time price for '{product.name}': {e}")
        sys.exit(1)


def init_db(db: Session):
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")

        # Check if plans already exist
        if db.query(PaymentPlan).first():
            print("Payment plans already exist in the database.")
            return

        print("Creating/retrieving Stripe products and prices...")

        # Free Plan
        free_product = create_or_retrieve_product("Free Plan")
        free_price = create_or_retrieve_price(free_product, 0, "usd")

        # Pro Plan
        pro_product = create_or_retrieve_product("Pro Plan")
        pro_price = create_or_retrieve_price(pro_product, 1000, "usd") # $10.00 in cents

        print("Creating default payment plans in the database...")
        free_plan_db = PaymentPlan(
            plan_id_code="free",
            name="Free Plan",
            credit_amount=20,
            price=0.00,
            stripe_price_id=free_price.id,
            is_default=True,
        )
        pro_plan_db = PaymentPlan(
            plan_id_code="pro",
            name="Pro Plan",
            credit_amount=100,
            price=10.00,
            stripe_price_id=pro_price.id,
            is_default=False,
        )
        db.add(free_plan_db)
        db.add(pro_plan_db)
        db.commit()
        print("Default payment plans created.")

        # Check if topup plans already exist
        if db.query(TopupPlan).first():
            print("Topup plans already exist in the database.")
            return

        print("Creating/retrieving Stripe products and prices for topup plans...")

        # Define default topup plans
        # Based on subscription plans:
        # - Free Plan: 20 credits for $0.00
        # - Pro Plan: 100 credits for $10.00
        #
        # Topup plans should be smaller and more affordable:
        default_topup_plans = [
            {
                "plan_id_code": "topup_small",
                "name": "Small Credit Topup",
                "credit_amount": 10,  # 10 credits for $2 (better value than pro plan)
                "price": 2.00
            },
            {
                "plan_id_code": "topup_medium",
                "name": "Medium Credit Topup",
                "credit_amount": 25,  # 25 credits for $4 (better value than pro plan)
                "price": 4.00
            },
            {
                "plan_id_code": "topup_large",
                "name": "Large Credit Topup",
                "credit_amount": 50,  # 50 credits for $7 (better value than pro plan)
                "price": 7.00
            }
        ]

        for plan_data in default_topup_plans:
            try:
                # Create product in Stripe
                product = create_or_retrieve_product(plan_data["name"])

                # Create one-time price in Stripe (not recurring)
                price = create_or_retrieve_onetime_price(
                    product,
                    int(plan_data["price"] * 100),  # Convert to cents
                    "usd"
                )

                # Create topup plan in database
                topup_plan = TopupPlan(
                    plan_id_code=plan_data["plan_id_code"],
                    name=plan_data["name"],
                    credit_amount=plan_data["credit_amount"],
                    price=plan_data["price"],
                    stripe_price_id=price.id,
                )
                db.add(topup_plan)

                print(f"Created topup plan: {plan_data['name']} - {plan_data['credit_amount']} credits for ${plan_data['price']}")

            except Exception as e:
                print(f"Error creating topup plan '{plan_data['name']}': {e}")
                continue

        db.commit()
        print("Default topup plans created.")

    except Exception as e:
        print(f"Error during DB initialization: {e}")
        db.rollback()
        sys.exit(1)


if __name__ == "__main__":
    db = SessionLocal()
    init_db(db)
    db.close()
