import sys
import importlib.util

# Check if SQLAlchemy is installed
if importlib.util.find_spec("sqlalchemy") is None:
    print("Error: SQLAlchemy is not installed.")
    print("Please install it using: poetry add sqlalchemy")
    sys.exit(1)

try:
    from app.db.session import engine
    from app.models.analytics import Base
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure all dependencies are installed: poetry install")
    sys.exit(1)


def init_db():
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")
    except Exception as e:
        print(f"Error creating database tables: {e}")
        sys.exit(1)


if __name__ == "__main__":
    init_db()
