import logging
from firebase_admin import credentials, messaging, initialize_app
from firebase_admin.exceptions import NotFoundError, FirebaseError

cred = credentials.Certificate("./asset/firebase-credentials.json")
app = initialize_app(cred)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FirebaseAdmin:

    def __init__(self):
        # Initialize Firebase
        pass

    def send_fcm_update(self, client_token, data):
        # Ensure all values in the data dictionary are strings
        string_data = {k: str(v) for k, v in data.items()}

        try:
            message = messaging.Message(
                data=string_data,
                token=client_token,
            )
            response = messaging.send(message)
            logging.info(f"Successfully sent message: {response}")
        except NotFoundError:
            logger.error(
                f"Error sending message: Device token not found or no longer valid: {client_token}"
            )
            # You might want to remove this token from your database or mark it as invalid
            return None
        except FirebaseError as e:
            logger.error(f"Firebase error sending message: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error sending message: {str(e)}")
            return None
