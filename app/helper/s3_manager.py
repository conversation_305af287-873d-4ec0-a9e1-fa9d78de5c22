import os
import boto3
import tempfile
from botocore.exceptions import Client<PERSON>rror

from config import (
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    S3_BUCKET_NAME,
)


class S3Uploader:
    def __init__(self):

        self.aws_access_key_id = AWS_ACCESS_KEY_ID
        self.aws_secret_access_key = AWS_SECRET_ACCESS_KEY
        self.s3_bucket_name = S3_BUCKET_NAME

        # self.s3_key_prefix = 'transcript/'
        self.URL = "https://ciny-dev.s3.amazonaws.com/"

        if not all(
            [self.aws_access_key_id, self.aws_secret_access_key, self.s3_bucket_name]
        ):
            raise ValueError("Missing required AWS environment variables")
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    def generate_post_presigned(self, folder, file_name, bucket_name=S3_BUCKET_NAME):
        object_key = os.path.join(folder, file_name)
        return self.s3_client.generate_presigned_post(
            Bucket=bucket_name, Key=object_key, ExpiresIn=300
        )

    def generate_presigned_url_for_download(
        self, folder, file_name, bucket_name=S3_BUCKET_NAME, expiration=3600
    ):
        """
        Generate a pre - signed URL for accessing an object.

        :param bucket_name: Name of the bucket.
        :param object_key: Key of the object.
        :param expiration: Expiration time in seconds(default: 3600 seconds).
        :return: Pre - signed URL as a string.
        """

        object_key = os.path.join(f"{S3_BUCKET_NAME}/{folder}/", file_name)

        return self.s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket_name, "Key": object_key},
            ExpiresIn=expiration,
        )

    def generate_presigned_url(
        self,
        folder,
        file_name,
        content_type="image/png",
        bucket_name=S3_BUCKET_NAME,
        expiration=3600,
    ):
        """
        Generate a pre-signed URL for uploading (putting) an object to S3.

        :param folder: The folder path within the bucket.
        :param file_name: The name of the file to be uploaded.
        :param bucket_name: Name of the bucket.
        :param expiration: Expiration time in seconds (default: 3600 seconds).
        :return: Pre-signed URL as a string.
        """
        object_key = os.path.join(folder, file_name)

        return self.s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": bucket_name,
                "Key": object_key,
                "ContentType": content_type,
            },
            ExpiresIn=expiration,
            HttpMethod="PUT",
        )

    def file_URL(self, folder, file_name):
        url = os.path.join(self.URL, f"{folder}/", file_name)
        return url

    def upload_file(self, folder, local_file_path, new_file_name=None):
        try:
            file_name = os.path.basename(local_file_path)
            if new_file_name is None:
                s3_object_key = os.path.join(f"{S3_BUCKET_NAME}/{folder}/", file_name)
            else:
                s3_object_key = os.path.join(
                    f"{S3_BUCKET_NAME}/{folder}/", new_file_name
                )

            self.s3_client.upload_file(
                local_file_path,
                self.s3_bucket_name,
                s3_object_key,
            )
            return os.path.join(self.URL, s3_object_key)
        except Exception as e:
            return f"Error uploading the file to S3: {str(e)}"

    def upload_file_to_s3(self, file, file_name, folder):
        try:
            s3_object_key = os.path.join(f"{folder}/", file_name)

            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(file)
                temp_file.seek(0)

                # Upload the file to S3 using the upload_file method
                self.s3_client.upload_file(
                    temp_file.name, self.s3_bucket_name, s3_object_key
                )
            return self.file_URL(folder, file_name)
        except Exception as e:
            print(f"Error uploading file to S3: {e}")
            return None

    def download_file(self, s3_object_key, local_file_path):
        try:
            self.s3_client.download_file(
                Bucket=self.s3_bucket_name, Key=s3_object_key, Filename=local_file_path
            )
            print(f"File downloaded successfully: {local_file_path}")
            return True
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "404":
                print(
                    f"Error: The object '{s3_object_key}' does not exist in the S3 bucket."
                )
            else:
                print(f"Error downloading file: {e}")
            return False
        except Exception as e:
            print(f"Error downloading file: {e}")
            return False

    def delete_file(self, folder, file_name):
        try:
            print(folder, file_name)
            s3_object_key = os.path.join(f"{folder}/", file_name)
            self.s3_client.delete_object(Bucket=self.s3_bucket_name, Key=s3_object_key)
            return True
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "404":
                print(
                    f"Error: The object '{s3_object_key}' does not exist in the S3 bucket."
                )
            else:
                print(f"Error deleting file: {e}")
            return False
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False
