import smtplib
import os
import sys
from email.mime.text import MIME<PERSON>ex<PERSON>
from email.mime.multipart import MIMEMultipart
from email.utils import formataddr

from jinja2 import FileSystemLoader, Environment

from config import <PERSON><PERSON>IL_ID, EMAIL_NAME, EMAIL_PASSWORD

from app.constant.user_enums import EmailType

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


class EmailService:
    def __init__(self):
        self.from_email = EMAIL_ID
        self.email_password = EMAIL_PASSWORD
        self.bot_name = EMAIL_NAME
        template_dir = "app/templates"
        self.env = Environment(loader=FileSystemLoader(template_dir))

    def send_email(self, subject, message, to_email):
        msg = MIMEMultipart()
        msg["From"] = formataddr((self.bot_name, self.from_email))
        msg["To"] = to_email
        msg["Subject"] = subject

        msg.attach(MIMEText(message, "html"))

        try:
            server = smtplib.SMTP("smtp.gmail.com", 587)
            server.starttls()
            server.login(self.from_email, self.email_password)

            server.sendmail(self.from_email, to_email, msg.as_string())

            server.quit()

            print(f"Email sent successfully to {to_email}")
        except Exception as e:
            print(f"Email to {to_email} failed: {str(e)}")

    def send_emails(self, subject, message, to_email_list):
        for to_email in to_email_list:
            self.send_email(subject, message, to_email)

    def send_email_with_template(
        self,
        name,
        email,
        link,
        email_type,
        metadata=None,
        platform="Youtube",
    ):

        # Prepare data for rendering the HTML template
        data = {
            "client_name": name,
            "link": link,
        }

        # Render the template
        if email_type == EmailType.VIDEO_GENERATED.value:
            subject = "Your Video is Ready! Click to Watch Now"
            template = self.env.get_template("video_generated.html")
        elif email_type == EmailType.VIDEO_UPLOADED.value:
            subject = f"Your {platform} Video is Live! 🎉📺"
            data["platform"] = platform
            template = self.env.get_template("video_uploaded.html")
        elif email_type == EmailType.DEACTIVATE.value:
            subject = "Your Ciny Platform Account Has Been Deactivated"
            template = self.env.get_template("deactivate.html")
        elif email_type == EmailType.WELCOME.value:
            subject = "Welcome to Ciny Platform! Your YouTube journey begins"
            template = self.env.get_template("welcome.html")
        elif email_type == EmailType.VIDEO_GENERATION_FAILED.value:
            data["failed_reason"] = metadata["failed_reason"]
            subject = "Your Video Generation Encountered an Error"
            template = self.env.get_template("video_generation_failed.html")
        elif email_type == EmailType.FORGOT_PASSWORD.value:
            subject = "Reset Your Password"
            template = self.env.get_template("forgot_password.html")
        elif email_type == EmailType.SIGNUP.value:
            subject = "Your Account is Ready For Verification."
            template = self.env.get_template("signup.html")

        html = template.render(data=data)

        self.send_emails(subject, html, [email])
