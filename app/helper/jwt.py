import jwt
from typing import Dict, Any

from config import SECRET_KEY


class JwtService:
    def sign(self, payload: Dict[str, Any], options: Dict[str, Any]) -> str:
        # Simulating JWT signing
        return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

    # Helper functions
    def decrypt_otp_token(self, token):
        try:
            return jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        except:
            return None
