from cryptography.fernet import Fernet


class EncryptDecrypt:
    def __init__(self):
        """
        Generates a new encryption key and initializes the Fernet object.
        """
        key = Fernet.generate_key()
        self.fernet = Fernet(key)

    def encrypt_message(self, message):
        """
        Encrypts a message using the Fernet key.
        """
        return self.fernet.encrypt(message.encode())

    def decrypt_message(self, encrypted_message):
        """
        Decrypts an encrypted message using the Fernet key.
        """
        return self.fernet.decrypt(encrypted_message).decode()


# Create an instance of the EncryptDecrypt class
encrypt_decrypt = EncryptDecrypt()
