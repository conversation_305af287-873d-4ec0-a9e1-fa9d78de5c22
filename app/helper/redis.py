import redis

from config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD


class RedisService:
    def __init__(self):
        self.redis = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=0,
            decode_responses=True,
            password=REDIS_PASSWORD,
        )

    def set_data_in_redis_with_ttl(self, key: str, value: str, ttl: int):
        self.redis.setex(key, ttl, value)

    def get_data_from_redis_using_key(self, key):
        return self.redis.get(key)

    def delete_data_with_key(self, key):
        self.redis.delete(key)
