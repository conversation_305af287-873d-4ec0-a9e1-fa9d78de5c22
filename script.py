import os
import cv2
import requests
from PIL import Image
from app.repositories.avatar_repository import AvatarRepository
from app.service.tavus import TavusService
from app.helper.s3_manager import S3Uploader
from app.repositories.media_repository import MediaRepository
from typing import Dict, Any
import mimetypes
from mutagen import File

s3_service = S3Uploader()
media_repository = MediaRepository()


def generate_thumbnail(video_path, output_path, time=0.0):
    # Open the video file
    video = cv2.VideoCapture(video_path)

    # Set the frame position to the specified time
    video.set(cv2.CAP_PROP_POS_MSEC, time * 1000)

    # Read the frame
    success, image = video.read()

    if success:
        # Convert the image from BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Create a PIL Image
        pil_image = Image.fromarray(image_rgb)

        # First, resize the image while maintaining aspect ratio
        # so that the smaller dimension is at least 480 pixels
        aspect_ratio = pil_image.width / pil_image.height
        if aspect_ratio > 1:
            # Image is wider than tall
            new_width = int(480 * aspect_ratio)
            new_height = 480
        else:
            # Image is taller than wide
            new_width = 480
            new_height = int(480 / aspect_ratio)

        pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Calculate coordinates for center crop
        left = (pil_image.width - 480) // 2
        top = (pil_image.height - 480) // 2
        right = left + 480
        bottom = top + 480

        # Crop the image to 480x480
        pil_image = pil_image.crop((left, top, right, bottom))

        # Save the thumbnail
        pil_image.save(output_path)

        print(f"Thumbnail generated: {output_path}")
    else:
        print(f"Failed to generate thumbnail for {video_path}")

    # Release the video capture object
    video.release()


def main():
    # Initialize the avatar repository
    avatar_repository = AvatarRepository()
    tavus_service = TavusService()
    s3_service = S3Uploader()

    avatar_list = tavus_service.get_replicas()

    for avatar in avatar_list:
        # Get the video URL
        video_url = avatar["thumbnail_video_url"]

        # Generate a unique filename for the thumbnail
        thumbnail_filename = f"thumbnail_{avatar['replica_id']}.jpg"

        # Set the output path for the thumbnail
        output_path = os.path.join("thumbnails", thumbnail_filename)

        # Ensure the output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Generate the thumbnail
        generate_thumbnail(video_url, output_path)

        thumbnail_link = s3_service.upload_file(
            "avatar", output_path, f"{avatar['replica_id']}.jpg"
        )

        # Update the avatar with the new thumbnail path
        avatar_repository.update_avatar_by_avatar_id(
            avatar["replica_id"],
            {
                "thumbnail_image": thumbnail_link,
            },
        )


def upload_file(url, name):

    # Download and save the video
    response = requests.get(url)
    response.raise_for_status()  # Raise an exception for bad responses

    with open(f"./temp/{name}", "wb") as f:
        f.write(response.content)

    output_path = f"./temp/{name}"

    thumbnail_link = s3_service.upload_file("profile", output_path, name)

    print("thumbnail_link", thumbnail_link)


def upload_music_file(file_path: str) -> Dict[str, Any]:
    """
    Upload a music file to S3 and generate metadata

    :param file_path: Local path to the music file
    :param user_id: ID of the user uploading the file
    :return: Metadata dictionary for the uploaded file
    """
    # Generate unique filename
    filename = f"{os.path.basename(file_path)}"

    # Determine MIME type
    mime_type, _ = mimetypes.guess_type(file_path)

    # Get audio file metadata
    audio = File(file_path)

    # Upload to S3
    try:
        link = s3_service.upload_file(
            "music",
            file_path,
            filename,
        )

        # Prepare metadata
        media_data = {
            "title": os.path.splitext(os.path.basename(file_path))[0],
            "description": None,
            "type": "music",
            "link": link,
            "mime_type": mime_type or "audio/mpeg",
            "resolution": None,
            "duration": audio.info.length if hasattr(audio, "info") else None,
            "thumbnail": None,
            "category": "inspirational",
        }

        return media_repository.create_media(media_data)

    except Exception as e:
        print(f"Error uploading file {file_path}: {e}")
        return None


def upload_music_folder(folder_path: str) -> list:
    """
    Upload all music files from a folder

    :param folder_path: Path to the folder containing music files
    :param user_id: ID of the user uploading the files
    :return: List of metadata for uploaded files
    """
    # List of common audio file extensions
    audio_extensions = [".mp3", ".wav", ".flac", ".m4a", ".ogg", ".aac"]

    # Store metadata for uploaded files
    uploaded_files = []

    # Iterate through files in the folder
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)

        # Check if file is an audio file
        if (
            os.path.isfile(file_path)
            and os.path.splitext(filename)[1].lower() in audio_extensions
        ):
            # Upload file and get metadata
            file_metadata = upload_music_file(file_path)
            if file_metadata:
                uploaded_files.append(file_metadata)

    return uploaded_files


# Example usage
def upload_music():

    folder_path = "./music/inspirational"

    # Upload and get metadata for all music files
    uploaded_music_files = upload_music_folder(folder_path)

    # Print metadata for uploaded files
    for music_file in uploaded_music_files:
        print(music_file)


if __name__ == "__main__":
    upload_music()
    # main()
    # upload_file(
    #     url="https://i.ytimg.com/an_webp/unZHE7yixM0/mqdefault_6s.webp?du=3000&sqp=CKyi7bgG&rs=AOn4CLDm4zyB04aXsm4H_UWE33DUB_UlLg",
    #     name="four.webp",
    # )
