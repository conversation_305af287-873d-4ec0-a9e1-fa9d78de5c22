import os
from dotenv import load_dotenv
import warnings

from app.loggers.logger import setup_logger

from moviepy.config import change_settings

from app.service.utils import save_video

load_dotenv()
setup_logger()


# Suppress deprecation warnings
warnings.filterwarnings("ignore", category=SyntaxWarning, module="moviepy")

# If you need to suppress only specific warnings:
warnings.filterwarnings(
    "ignore", message="invalid escape sequence", category=SyntaxWarning
)

IMAGEMAGICK_BINARY: str = os.environ["IMAGEMAGICK_BINARY"]

change_settings({"IMAGEMAGICK_BINARY": IMAGEMAGICK_BINARY})

# Define configuration variables
HOST: str = os.environ["HOST"]
PORT = int(os.environ["PORT"])
# WEBSOCKET_PORT = int(os.environ["WEBSOCKET_PORT"])

CACHE_PROVIDER: str = os.environ["CACHE_PROVIDER"]
REDIS_HOST: str = os.environ["REDIS_HOST"]
REDIS_PORT: str = os.environ["REDIS_PORT"]
REDIS_PASSWORD: str = os.environ["REDIS_PASSWORD"]

SECRET_KEY: str = os.environ["SECRET_KEY"]

FRONTEND_ENDPOINT: str = os.environ["FRONTEND_ENDPOINT"]
USER_JWT_COOKIE: str = os.environ["USER_JWT_COOKIE"]
DOMAIN: str = os.environ["DOMAIN"]
TOKEN_EXPIRY_IN_HOUR: int = os.environ["TOKEN_EXPIRY_IN_HOUR"]

LOGIN_URL: str = os.environ["LOGIN_URL"]

MONGO_URI: str = os.environ["MONGO_URI"]

API_GATEWAY_URL: str = os.environ["API_GATEWAY_URL"]

TAVUS_API_KEY: str = os.environ["TAVUS_API_KEY"]

# sentry
SENTRY_DSN: str = os.environ["SENTRY_DSN"]

# GET ORIGIN FROM ENV VARIABLES *OR* SET DEFAULT
ORIGIN: str = (
    os.environ["API_CORS_ORIGINS"] if "API_CORS_ORIGINS" in os.environ else "*"
)

DEBUG: str = os.getenv("DEBUG", False)

# API KEYS FOR LLMs
OPENAI_API_KEY: str = os.environ["OPENAI_API_KEY"]
CLAUDE_KEY: str = os.environ["CLAUDE_KEY"]
GEMINI_API_KEY: str = os.environ["GEMINI_API_KEY"]

GOOGLE_API_KEY: str = os.environ["GOOGLE_API_KEY"]


EMAIL_ID: str = os.environ["EMAIL_ID"]
EMAIL_PASSWORD: str = os.environ["EMAIL_PASSWORD"]

EMAIL_NAME: str = os.environ["EMAIL_NAME"]

EMAIL_SUBSTRING = os.environ["EMAIL_SUBSTRING"]

# AWS_ACCESS_KEY_ID = os.environ["AWS_ACCESS_KEY_ID"]
# AWS_SECRET_ACCESS_KEY = os.environ["AWS_SECRET_ACCESS_KEY"]
# S3_BUCKET_NAME = os.environ["S3_BUCKET_NAME"]
# REGION = os.environ["REGION"]

FE_URL: str = os.environ["FE_URL"]

# Google
GOOGLE_CLIENT_ID: str = os.environ["GOOGLE_CLIENT_ID"]
GOOGLE_CLIENT_SECRET: str = os.environ["GOOGLE_CLIENT_SECRET"]
REDIRECT_URL: str = os.environ["REDIRECT_URL"]

SESSION_FOLDER = os.environ["SESSION_FOLDER"]

PLAY_API_KEY: str = os.environ["PLAY_API_KEY"]
PLAY_USER_ID: str = os.environ["PLAY_USER_ID"]

STORYBLOCKS_API_KEY: str = os.environ["STORYBLOCKS_API_KEY"]
STORYBLOCKS_SCRECT_KEY: str = os.environ["STORYBLOCKS_SCRECT_KEY"]

PEXELS_API_KEY: str = os.environ["PEXELS_API_KEY"]


ASSEMBLY_AI_API_KEY: str = os.environ["ASSEMBLY_AI_API_KEY"]

AWS_ACCESS_KEY_ID: str = os.environ["AWS_ACCESS_KEY_ID"]
AWS_SECRET_ACCESS_KEY: str = os.environ["AWS_SECRET_ACCESS_KEY"]
S3_BUCKET_NAME: str = os.environ["S3_BUCKET_NAME"]

SERPER_API_KEY: str = os.environ["SERPER_API_KEY"]
TAVILY_API_KEY: str = os.environ["TAVILY_API_KEY"]

HEYGEN_API_KEY: str = os.environ["HEYGEN_API_KEY"]

SERPER_API_KEY: str = os.environ["SERPER_API_KEY"]

WEBHOOK_ENDPOINT: str = os.environ["WEBHOOK_ENDPOINT"]

RABBITMQ_BROKER_URL: str = os.environ["RABBITMQ_BROKER_URL"]
CELERY_BACKEND_URL: str = os.environ["CELERY_BACKEND_URL"]

SUTTERSTOCK_TOKEN: str = os.environ["SUTTERSTOCK_TOKEN"]

META_CLIENT_ID: str = os.environ["META_CLIENT_ID"]
META_CLIENT_SECRET: str = os.environ["META_CLIENT_SECRET"]
META_REDIRECT_URI: str = os.environ["META_REDIRECT_URI"]

WEBSHARE_PROXY_API: str = os.environ["WEBSHARE_PROXY_API"]

LEONARDO_API_KEY: str = os.environ["LEONARDO_API_KEY"]

STRIPE_PRIVATE_KEY: str = os.environ["STRIPE_PRIVATE_KEY"]
STRIPE_WEBHOOK_SECRET: str = os.environ["STRIPE_WEBHOOK_SECRET"]

GOOGLE_CONSOLE_API_KEY: str = os.environ["GOOGLE_CONSOLE_API_KEY"]

WAITLIST_ENABLED: str = os.environ["WAITLIST_ENABLED"]
