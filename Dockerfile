FROM python:3.11-slim
ARG REPO_URL
ARG GIT_TOKEN
ARG ENV

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false

# Add poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies required for gRPC and database connectivity
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Set working directory
WORKDIR /app

# Copy poetry files
COPY pyproject.toml ./
# Copy poetry.lock if it exists (optional for development)
COPY poetry.lock* ./

# Install dependencies (gRPC, database, and analytics dependencies)
RUN poetry install --no-interaction --no-ansi --no-root

# Copy application code
COPY app /app/app

# Generate gRPC stubs from proto definitions
RUN python -m app.scripts.generate_grpc

# Expose gRPC port
EXPOSE 50059

# Run the gRPC analytics service
CMD ["python", "-m", "app.main"]
