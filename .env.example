ENV= dev | prod

LOG_LEVEL=debug | info | warn | error

# Server
HOST='0.0.0.0'
PORT='5001'

API_CORS_ORIGINS='*'

# LLM MODEL KEYS
OPENAI_API_KEY="sk-proj"
GEMINI_KEY="-czw"
CLAUDE_KEY="sk-"


SECRET_KEY="68/78zixtfc0bSjBf/"

MONGO_URI="mongodb+srv:/"

DEBUG=True

CACHE_PROVIDER="redis"

# REDIS CONNECTION
REDIS_HOST="***********"
REDIS_PORT="6379"
REDIS_PASSWORD=''

FE_URL=""

# MAILER SECRET
EMAIL_ID=''
EMAIL_PASSWORD='zldy bwxx eabg ipuo'
EMAIL_NAME='no-reply'



EMAIL_SUBSTRING=['@rapidinnovation.dev', '@rapidinnovation.io']

GOOGLE_CLIENT_ID="-.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="GOCSPX-"
REDIRECT_URL="http://127.0.0.1:5001/api/auth/google-callback"
SESSION_FOLDER=""

OAUTHLIB_INSECURE_TRANSPORT=1
OAUTHLIB_RELAX_TOKEN_SCOPE=1



# See EnvironmentVariables.md for more information.

# Necessary API Keys
# -------------------

# TikTok Session ID
# Obtain your session ID by logging into TikTok and copying the sessionid cookie.
TIKTOK_SESSION_ID=""

# ImageMagick Binary Path
# Download ImageMagick from https://imagemagick.org/script/download.php
IMAGEMAGICK_BINARY="/opt/homebrew/bin/convert"


# Pexels API Key
# Register at https://www.pexels.com/api/ to get your API key.
PEXELS_API_KEY=""

# Optional API Keys
# -----------------

# OpenAI API Key
# Visit https://openai.com/api/ for details on obtaining an API key.
OPENAI_API_KEY="sk-proj-"


# AssemblyAI API Key
# Sign up at https://www.assemblyai.com/ to receive an API key.
ASSEMBLY_AI_API_KEY=""

# Google API Key
# Generate your API key through https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=""


HEYGEN_API_KEY="=="
 
STORYBLOCKS_API_KEY=""

STORYBLOCKS_SCRECT_KEY=""

TAVILY_API_KEY="tvly-"


WEBHOOK_ENDPOINT="http://localhost:8080/api/webhook"


INSTAGRAM_ID="pratik_rapid"
INSTAGRAM_PASS="Rapid@123"

SERPER_API_KEY=""


PLAY_API_KEY=""
PLAY_USER_ID=""



GEMINI_API_KEY=""