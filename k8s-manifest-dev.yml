apiVersion: v1
kind: ServiceAccount
metadata:
  name: analytics-api-ai-sa
  namespace: developer-ruh-ai-dev
  labels:
    name: analytics-api-ai-sa
    namespace: developer-ruh-ai-dev
    app: analytics-api-ai
    deployment: analytics-api-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-api-ai-dp
  namespace: developer-ruh-ai-dev
  labels:
    name: analytics-api-ai-dp
    namespace: developer-ruh-ai-dev
    app: analytics-api-ai
    serviceaccount: analytics-api-ai-sa
    deployment: analytics-api-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: analytics-api-ai
      deployment: analytics-api-ai-dp
  template:
    metadata:
      labels:
        namespace: developer-ruh-ai-dev
        app: analytics-api-ai
        deployment: analytics-api-ai-dp
    spec:
      serviceAccountName: analytics-api-ai-sa      
      containers:
      - name: analytics-api-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50056
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: analytics-api-ai-svc
  namespace: developer-ruh-ai-dev
spec:
  selector:
    app: analytics-api-ai
    deployment: analytics-api-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50056
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:developer-api-workflow-hpa
#   namespace:developer-ruh-ai-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:developer-api-workflow-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress



