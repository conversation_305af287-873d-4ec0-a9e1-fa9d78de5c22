#!/bin/bash

# Exit immediately if any command fails
set -e

# Function to print usage information.
usage() {
  echo "Usage: $0 <folder_name> <commit_hash> <branch_name>"
  exit 1
}

# Function to check and clone/pull the repository
update_repo() {
  if [ ! -d "$FOLDER_NAME" ]; then
    <NAME_EMAIL>:ciny-ai/ciny-python-backend.git -b $ENV "$FOLDER_NAME"
    cd "$FOLDER_NAME"
  else
    cd "$FOLDER_NAME" || exit 1
    git checkout $ENV
    git pull origin $ENV
  fi
}

# Function to build and push Docker image
build_image() {
  docker build -t "$1" -f "$2" .
}

# Function to stop and remove old Docker container
stop_and_remove_container() {
  if docker ps -a --format '{{.Names}}' | grep -Eq "^$CONTAINER_NAME$"; then
    echo "Stopping existing container: $CONTAINER_NAME"
    if docker stop "$CONTAINER_NAME"; then
      echo "Container $CONTAINER_NAME stopped successfully."
    else
      echo "Failed to stop container $CONTAINER_NAME."
    fi

    echo "Removing existing container: $CONTAINER_NAME"
    if docker rm "$CONTAINER_NAME"; then
      echo "Container $CONTAINER_NAME removed successfully."
    else
      echo "Failed to remove container $CONTAINER_NAME."
    fi
  else
    echo "No existing container named $CONTAINER_NAME to stop or remove."
  fi
}

# Function to run the new Docker container
run_new_container() {
  docker run -d --network=cini --restart always -p 5001:5001 --label io.portainer.accesscontrol.public --env-file /home/<USER>/backend-env/.env --name "$CONTAINER_NAME" "$IMAGE_NAME"

}

# Function to remove previous Docker images of the same app
remove_previous_images() {
  echo "##############################"
  echo "Removing old images"
  echo "##############################"

  # Remove previous Docker images that do not match the current commit hash
  docker images --format '{{.Repository}}:{{.Tag}}' | grep "^$FOLDER_NAME:" | grep -v "$COMMIT_HASH" | xargs -r docker rmi -f || true
}


# Ensure correct number of arguments
if [ $# -ne 3 ]; then
  usage
fi

FOLDER_NAME=$1
COMMIT_HASH=$2
ENV=$3
IMAGE_NAME=$1:$COMMIT_HASH
CELERY_IMAGE_NAME="$1-celery:$COMMIT_HASH"
CONTAINER_NAME=$FOLDER_NAME

# Update repository
update_repo

# Build image
build_image "$IMAGE_NAME" "Dockerfile"

# Stop and remove old Docker container
stop_and_remove_container

# Run new Docker container
run_new_container

# Build image for celery
build_image "$CELERY_IMAGE_NAME" "Dockerfile.celery"

# Remove unused images
remove_previous_images
