version: "3.3"

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    environment:
      - CELERY_BROKER_URL=${RABBITMQ_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_BACKEND_URL}
    volumes:
      - .:/app
    depends_on:
      - redis
    restart: always

  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    command: celery -A celery_worker.celery worker --loglevel=info --pool=eventlet
    volumes:
      - .:/app
    environment:
      - CELERY_BROKER_URL=${RABBITMQ_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_BACKEND_URL}
    depends_on:
      - redis
    restart: unless-stopped

  flower:
    image: mher/flower
    environment:
      - CELERY_BROKER_URL=${RABBITMQ_BROKER_URL}
      - FLOWER_PORT=5555
    ports:
      - 5555:5555

  redis:
    image: "redis:alpine"
    restart: always
