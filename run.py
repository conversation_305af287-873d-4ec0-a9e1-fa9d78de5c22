import asyncio
import os
import threading
import config
from celery import Celery
from functools import wraps

from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# from flask_talisman import Talisman


# from app.websocket.config import websocket_server

from flask_cors import CORS
from flask_executor import Executor
from werkzeug.middleware.proxy_fix import ProxyFix
from flask import Flask, jsonify, redirect, url_for

# from flask_socketio import SocketIO, emit, disconnect

from app.controller.health.app import health_check_blueprint
from app.controller.script.app import script_blueprint
from app.controller.auth.app import auth_blueprint, google_login
from app.controller.video.app import video_blueprint
from app.controller.avatar.app import avatar_blueprint
from app.controller.voice.app import voice_blueprint
from app.controller.subscription.app import subscription_blueprint
from app.controller.brand.app import brand_blueprint
from app.controller.template.app import template_blueprint
from app.controller.notification.app import notification_blueprint
from app.controller.media.app import media_blueprint


HOST: str = config.HOST
PORT: int = config.PORT

ORIGIN: str = config.ORIGIN

# Initialize Flask app
app = Flask(__name__)
executor = Executor(app)

# Celery configuration
app.config["CELERY_BROKER_URL"] = config.RABBITMQ_BROKER_URL
app.config["CELERY_RESULT_BACKEND"] = config.CELERY_BACKEND_URL

# app.config["PREFERRED_URL_SCHEME"] = "https"


# def set_security_headers():
#     csp = {"img-src": "*"}
#     feature_policy = {"geolocation": "'none'"}
#     Talisman(
#         app,
#         content_security_policy=csp,
#         feature_policy=feature_policy,
#         permissions_policy="geolocation=(), interest-cohort=()",
#         strict_transport_security=True,
#         strict_transport_security_include_subdomains=True,
#         strict_transport_security_preload=True,
#         force_https=True,
#         force_https_permanent=True,
#         # content_type_options=True
#     )


# set_security_headers()


# common exception Handler for whole app or all routes function
def get_http_exception_handler(app):
    """Overrides the default http exception handler to return JSON."""
    handle_http_exception = app.handle_http_exception

    @wraps(handle_http_exception)
    def ret_val(exception):
        exc = handle_http_exception(exception)
        return jsonify({"code": exc.code, "message": exc.description}), exc.code

    return ret_val


# Override the HTTP exception handler.
app.handle_http_exception = get_http_exception_handler(app)

limiter = Limiter(
    get_remote_address,
    app=app,
    default_limits=[
        # "1 per day",
        # "1 per hour",
        # "1 per minute",
        # "1 per second"
        os.getenv("RATE_LIMIT", "100 per minute")
    ],
    storage_uri=os.environ["MONGO_URI"],  # os.getenv('MONGO_URI'),
)


# CORS configuration
CORS(
    app,
    resources={
        r"/*": {
            "origins": ORIGIN.split(","),
            "methods": ["GET", "HEAD", "POST", "OPTIONS", "PUT", "DELETE", "PATCH"],
            "max_age": 300,
        }
    },
)
app.config["CORS_HEADERS"] = "Content-Type"

app.config["SECRET_KEY"] = "testing"

# ProxyFix middleware to handle headers when running behind a reverse proxy
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# Register the Google Blueprint with the Flask app
app.register_blueprint(health_check_blueprint, url_prefix="/api/healthcheck")
app.register_blueprint(script_blueprint, url_prefix="/api/script")
app.register_blueprint(auth_blueprint, url_prefix="/api/auth")
app.register_blueprint(google_login, url_prefix="/api/auth")
app.register_blueprint(video_blueprint, url_prefix="/api/video")
app.register_blueprint(avatar_blueprint, url_prefix="/api/avatar")
app.register_blueprint(voice_blueprint, url_prefix="/api/voice")
app.register_blueprint(subscription_blueprint, url_prefix="/api/subscription")
app.register_blueprint(brand_blueprint, url_prefix="/api/brand")
app.register_blueprint(media_blueprint, url_prefix="/api/media")
app.register_blueprint(template_blueprint, url_prefix="/api/template")
app.register_blueprint(notification_blueprint, url_prefix="/api/notification")

# socketio = SocketIO(
#     app,
#     cors_allowed_origins="*",
#     logger=True,
#     async_mode="threading",
#     engineio_logger=True,
# )


# @socketio.on("connect")
# def handle_connect():
#     client_id = request.sid
#     print(f"Client connected: {client_id}")


# @socketio.on("custom_event")
# def handle_custom_event(data):
#     client_id = request.sid
#     print(f"Received custom event: {data}")
#     emit(
#         "custom_response",
#         {"message": "Server received your custom event", "data": data},
#         room=client_id,
#     )


# @socketio.on_error_default
# def default_error_handler(e):
#     print(f"An error occurred: {str(e)}")
#     emit("error", {"message": "An error occurred during connection"})


# @socketio.on("disconnect")
# def handle_disconnect():
#     print(f"Client disconnected: {request.sid}")


@app.route("/")
def index():
    return redirect(url_for("api_index"))


@app.route("/api")
def api_index():
    return jsonify(
        {
            "message": "Welcome to the Ciny app API service! Please refer to the documentation for the API endpoints.",
            "health_check": "/api/healthcheck/",
            "auth": "/api/auth/",
            "script": "/api/script/",
            "video": "/api/video/",
            "avatar": "/api/avatar/",
            "voice": "/api/voice/",
            "subscription": "/api/subscription/",
            "brand": "/api/brand/",
            "template": "/api/template/",
        }
    )


def create_celery(app):
    celery = Celery(
        app.import_name,
        backend=app.config["CELERY_RESULT_BACKEND"],
        broker=app.config["CELERY_BROKER_URL"],
    )
    celery.conf.update(app.config)

    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    return celery


celery = create_celery(app)


if __name__ == "__main__":

    # Flask application
    def run_flask():
        app.run(
            debug=config.DEBUG,
            host=HOST,
            port=PORT,
            use_reloader=False,
            threaded=True,
        )

    flask_thread = threading.Thread(target=run_flask)

    # def run_flask_socketio():
    #     socketio.run(
    #         app,
    #         debug=config.DEBUG,
    #         host=config.HOST,
    #         port=config.PORT,
    #         use_reloader=False,
    #         allow_unsafe_werkzeug=True,  # Only use this in development
    #     )

    # # Start Flask+SocketIO in a separate thread
    # flask_thread = threading.Thread(target=run_flask_socketio)

    # Start Flask in a separate thread
    flask_thread.start()

    # server = websocket_server()

    # # Run the WebSocket server in the main thread
    # asyncio.get_event_loop().run_until_complete(server)
    # asyncio.get_event_loop().run_forever()
    # Run the WebSocket server in the main thread
    loop = asyncio.new_event_loop()
    # Set the new event loop as the current loop
    asyncio.set_event_loop(loop)
    # Assuming you have a WebSocket server setup
    # Uncomment and adjust the following line as needed:
    loop.run_forever()
